import React, { useEffect, useState } from "react";
import { Box, Button, Grid, Typography } from "@mui/material";
import CheckBoxOutlineBlankIcon from "@mui/icons-material/CheckBoxOutlineBlank";
import CheckBoxIcon from "@mui/icons-material/CheckBox";
import CircularProgress from "@mui/material/CircularProgress";

import { useSidebarState } from "react-admin";
import {
  GetLaunchReportsListDetails,
  GetAduOpenStoreWorkpackages,
} from "../service/launchReportsList";
import { UserData } from "../types";
import ExampleComponent from "./HandsonTable";
import { Snackbar, Alert } from "@mui/material";

const LaunchReportGrid = () => {
  const [sidebarIsOpen] = useSidebarState();
  const [allMenu, setAllMenu] = useState<any[]>([]);
  const [selectedKey, setSelectedKey] = useState<string | null>(
    "onboardingTotal"
  );
  const [tableData, setTableData] = useState<UserData[]>([]);
  const [allTypeValue, setAllTypeValue] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");

  useEffect(() => {
    fetchLaunchReportData();
  }, [refreshTrigger]);

  useEffect(() => {
    fetchTableData();
  }, [selectedKey, refreshTrigger]);

  const handleRefreshTrigger = () => {
    setRefreshTrigger((prev) => !prev);
  };

  const fetchLaunchReportData = async () => {
    try {
      const res = await GetLaunchReportsListDetails();
      console.log("resultttttttttt", res);
      if (res) {
        setAllMenu(res[0]);
      }
    } catch (error) {
      console.error(error);
    }
  };

  const colorList = [
    "#FFB3BA",
    "#FFDFBA",
    "#FFFFBA",
    "#BAFFC9",
    "#BAE1FF",
    "#D5BAFF",
    "#FFC8DD",
    "#C2F0FC",
    "#E2F0CB",
    "#F4C2C2",
    "#F0E5DE",
  ];

  const fetchTableData = async () => {
    setLoading(true);
    try {
      const res = await GetAduOpenStoreWorkpackages();

      // ✅ Check if response is an array
      if (!Array.isArray(res)) {
        throw new Error("API did not return an array");
      }

      setAllTypeValue(res);

      let filtered: any[] = [];

      switch (selectedKey) {
        case "onboardingTotal":
          filtered = res.filter((item: any) => item.launchCompleted == null);
          break;
        case "needsObMeeting":
          filtered = res.filter(
            (item: any) =>
              item.obMeetingCompleted == null && item.launchCompleted == null
          );
          break;
        case "dmsNotActive":
          filtered = res.filter(
            (item: any) =>
              item.dmsActiveDate == null && item.launchCompleted == null
          );
          break;
        case "opsNotCategorized":
          filtered = res.filter(
            (item: any) =>
              item.opCodesCategorized == null && item.launchCompleted == null
          );
          break;
        case "gridNotEntered":
          filtered = res.filter(
            (item: any) =>
              item.laborPricingGridEnteredInBzo == null &&
              item.launchCompleted == null
          );
          break;
        case "modelsNotMapped":
          filtered = res.filter(
            (item: any) =>
              item.modelMapping == null && item.launchCompleted == null
          );
          break;
        case "matrixNotEntered":
          filtered = res.filter(
            (item: any) =>
              item.partsMatrixEnteredInBzo == null &&
              item.launchCompleted == null
          );
          break;
        case "usersNotCreated":
          filtered = res.filter(
            (item: any) =>
              item.usersCreated == null && item.launchCompleted == null
          );
          break;
        case "launchNotScheduled":
          filtered = res.filter(
            (item: any) =>
              item.scheduledLaunch == null && item.launchCompleted == null
          );
          break;
        case "launchCompleted":
          filtered = res.filter((item: any) => item.launchCompleted != null);
          break;
        case "allStores":
          filtered = res;
          break;
        default:
          filtered = [];
      }

      setTableData(filtered);
    } catch (error: any) {
      console.error("Caught in fetchTableData:", error.message, error);
      setErrorMessage(
        "⚠️ Failed to fetch launch progress report data. Please try again."
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box
      sx={{
        paddingX: "10px",
        mt: "15px",
        width: "100%",
        "@media (max-width: 1440px)": { width: "83vw" },
        "@media (max-width: 1366px)": { width: "83vw" },
        "@media (max-width: 1280px)": { width: "83vw" },
        "@media (max-width: 1000px)": { width: "83vw" },
      }}>
      <Grid container spacing={2} width={"105%"}>
        {Object.entries(allMenu).map(
          ([key, value]: [string, number | string], index: number) => (
            <Grid item key={key}>
              <Button
                variant="contained"
                disableElevation
                onClick={() => !loading && setSelectedKey(key)}
                disabled={loading && selectedKey !== key}
                sx={{
                  backgroundColor: colorList[index % colorList.length],
                  color: "#000",
                  height: 100,
                  display: "flex",
                  flexDirection: "column",
                  justifyContent: "center",
                  textAlign: "center",
                  padding: 1,
                  width: 100,
                  minWidth: 80,
                  boxShadow: "none",
                  "&:hover": {
                    backgroundColor: colorList[index % colorList.length],
                    opacity: 0.9,
                  },
                  "&.Mui-disabled": {
                    backgroundColor: colorList[index % colorList.length],
                    color: "#aaa",
                    opacity: 1,
                  },
                  "@media (max-width: 1440px)": { width: 80 },
                  "@media (max-width: 1366px)": { width: 80 },
                  "@media (max-width: 1280px)": { width: 80 },
                  "@media (max-width: 1000px)": { width: 80 },
                }}>
                <Typography
                  variant="body2"
                  sx={{
                    mb:
                      key
                        .replace(/([A-Z])/g, " $1")
                        .replace(/^./, (str) => str.toUpperCase()) ===
                      "All Stores"
                        ? 3.5
                        : 1,
                    "@media (max-width: 1440px)": { fontSize: "0.7rem" },
                    "@media (max-width: 1366px)": { fontSize: "0.7rem" },
                    "@media (max-width: 1280px)": { fontSize: "0.7rem" },
                    "@media (max-width: 1000px)": { fontSize: "0.6rem" },
                  }}>
                  {key
                    .replace(/([A-Z])/g, " $1")
                    .replace(/^./, (str) => str.toUpperCase())}
                </Typography>
                <Box display="flex" alignItems="center" justifyContent="center">
                  {selectedKey === key ? (
                    <CheckBoxIcon fontSize="small" />
                  ) : (
                    <CheckBoxOutlineBlankIcon fontSize="small" />
                  )}
                  <Typography variant="h6" sx={{ ml: 1 }}>
                    {value}
                  </Typography>
                </Box>
              </Button>
            </Grid>
          )
        )}
      </Grid>

      {selectedKey && (
        <Box sx={{ mt: 1, width: "100%" }}>
          <div
            id="hand-table"
            className={sidebarIsOpen ? "sidebar-open" : "sidebar-closed"}
            style={{
              //height: "auto",
              width: selectedKey === "allStores" ? "80vw" : "80vw",
              marginBottom: 0,
              paddingBottom: 0,
            }}>
            {loading ? (
              <Box
                display="flex"
                justifyContent="center"
                alignItems="center"
                height={200}>
                <CircularProgress />
              </Box>
            ) : (
              <ExampleComponent
                tableData={tableData}
                selectedKey={selectedKey}
                allTypeValue={allTypeValue}
                refreshTrigger={handleRefreshTrigger}
              />
            )}
          </div>
        </Box>
      )}
      <Snackbar
        open={!!errorMessage}
        autoHideDuration={10000}
        onClose={() => setErrorMessage("")}
        anchorOrigin={{ vertical: "bottom", horizontal: "right" }}>
        <Alert
          onClose={() => setErrorMessage("")}
          severity="error"
          sx={{ width: "100%" }}>
          {errorMessage}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default LaunchReportGrid;
