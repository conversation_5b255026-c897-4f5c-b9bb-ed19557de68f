import { FC } from "react";
import { HotTable, HotTableClass } from "@handsontable/react";
import { registerAllModules } from "handsontable/registry";
import { ColumnSettings } from "handsontable/settings";
import "handsontable/dist/handsontable.full.min.css";
import "handsontable/styles/ht-theme-main.css";
import {
  UpdateTextFieldComments,
  GetDropDownValues,
  UpdateDropdownField,
  fetchMailField,
  updateScheduledMail,
} from "../service/launchReportsList";
import Handsontable from "handsontable";
import Core from "handsontable/core";
import type { CellProperties } from "handsontable/settings";
import { MenuItemConfig } from "handsontable/plugins/contextMenu";
import React, { useEffect, useState, useRef, useCallback } from "react";
import "../style.css";
import * as XLSX from "xlsx-js-style";
import {
  Grid,
  IconButton,
  Tooltip,
  Typography,
  Tabs,
  Tab,
} from "@mui/material";
import FileDownloadOutlinedIcon from "@mui/icons-material/FileDownloadOutlined";
import RefreshIcon from "@mui/icons-material/Refresh";
import MailIcon from "@mui/icons-material/Mail";
import { useSidebarState } from "react-admin";
import axios from "axios";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Box,
} from "@mui/material";
import debounce from "lodash/debounce";
import { useMemo } from "react";
import { useLocation } from "react-router-dom";
import { ClassNames } from "@emotion/react";
import { traceSpan } from "../utils/OTTTracing";
// Register all modules (includes Comments plugin)
registerAllModules();

interface ExampleComponentProps {
  tableData: any[]; // You can replace `any` with your specific data type
  selectedKey: any;
  allTypeValue: any[];
  refreshTrigger: () => void;
}

const ExampleComponent: FC<ExampleComponentProps> = ({
  tableData,
  selectedKey,
  allTypeValue,
  refreshTrigger,
}) => {
  type CellRenderer = (
    instance: Core,
    td: HTMLTableCellElement,
    row: number,
    col: number,
    prop: string | number,
    value: any,
    cellProperties: CellProperties
  ) => void;
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [openSnackbarMessage, setOpenSnackbarMessage] = useState("");
  const formatDateMMDDYYYY = (year: string, month: string, day: string) => {
    return `${month.padStart(2, "0")}/${day.padStart(2, "0")}/${year}`;
  };

  const withTooltip = (originalRenderer: CellRenderer): CellRenderer => {
    return function (instance, td, row, col, prop, value, cellProperties) {
      originalRenderer(instance, td, row, col, prop, value, cellProperties);

      let tooltipText = "";

      if (typeof value === "string" && /^\d{4}-\d{2}-\d{2}$/.test(value)) {
        // Parse manually to avoid timezone shifts
        const [year, month, day] = value.split("-");
        tooltipText = formatDateMMDDYYYY(year, month, day);
      } else if (value instanceof Date && !isNaN(value.getTime())) {
        // In case some cells have actual Date objects
        const mm = String(value.getMonth() + 1).padStart(2, "0");
        const dd = String(value.getDate()).padStart(2, "0");
        const yyyy = String(value.getFullYear());
        tooltipText = `${mm}/${dd}/${yyyy}`;
      } else {
        tooltipText = value ?? "";
      }

      td.title = tooltipText;
    };
  };

  const prevCommentRef = useRef<{ [key: string]: string }>({});
  const isInitializing = useRef(true);
  const isProgrammatic = useRef(false);
  const location = useLocation();
  const [activeTab, setActiveTab] = useState(0);
  const handleTabChange = async (
    _: any,
    newValue: React.SetStateAction<number>
  ) => {
    setActiveTab(newValue);
    setOpenSnackbar(false);
    setOpenSnackbarMessage("");
    if (newValue === 1) {
      try {
        const res = await fetchMailField();
        console.log("Value updated for", res);
        // Update form values
        setEmailForm((prev) => ({
          ...prev,
          email: res[0].toEmail || "",
          message: res[0].message || "",
          emailError: false,
        }));
      } catch (err) {
        console.error("Failed to fetch scheduled email data", err);
      }
    } else {
      setEmailForm({ email: "", message: "", emailError: false });
    }
  };

  const handleClose = () => {
    setActiveTab(0);
    setMailDialogOpen(false);
    setEmailForm({ email: "", message: "", emailError: false });
  };

  const [dropdownSources, setDropdownSources] = useState<{
    [key: string]: any[];
  }>({});

  const [mailDialogOpen, setMailDialogOpen] = useState(false);
  const [emailForm, setEmailForm] = useState({
    email: "",
    message: "",
    emailError: false,
  });
  const [isSending, setIsSending] = useState(false);
  const [isScheduleSending, setIsScheduleSending] = useState(false);
  const fieldNames = [
    "Tag Group Pay",
    "Onboarding Coordinator",
    "Salesperson",
    "DMS",
    "Coach",
  ];

  const [sidebarIsOpen] = useSidebarState();

  // Update mail button click handler
  const buttonClickSendMail = () => {
    traceSpan(`click_sendmail_icon`, {
      event: `click_sendmail_icon`,
      pageUrl: window.location.pathname,
      timestamp: new Date().toISOString(),
      userId: localStorage.getItem("user") || "",
    });
    setMailDialogOpen(true);
    setOpenSnackbar(false);
  };
  const isValidEmail = (email: string) =>
    /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email.trim());

  const validateMultipleEmails = (value: string) => {
    return value
      .split(",")
      .map((email) => email.trim())
      .every((email) => email === "" || isValidEmail(email));
  };

  const debouncedValidateEmails = useMemo(
    () =>
      debounce((value) => {
        const isValid = validateMultipleEmails(value);
        setEmailForm((prev) => ({
          ...prev,
          emailError: !isValid,
        }));
      }, 300),
    []
  );
  const allowedEmailCharsRegex = /^[a-zA-Z0-9@._,\s-]*$/;

  const handleEmailFormChange = (field: string, value: string) => {
    if (field === "email") {
      // Restrict invalid characters immediately (no lag)
      if (!allowedEmailCharsRegex.test(value)) return;

      setEmailForm((prev) => ({
        ...prev,
        email: value,
      }));

      // Debounced validation (avoid lag)
      debouncedValidateEmails(value);
    } else {
      setEmailForm((prev) => ({
        ...prev,
        [field]: value,
      }));
    }
  };
  // Handle schedule email
  const handleScheduleEmail = async () => {
    setIsScheduleSending(true);
    try {
      const response = await updateScheduledMail(
        emailForm.email.trim(),
        emailForm.message.trim()
      );
      console.log("Schedule updated:", response);

      // handleClose(); // Close the dialog if needed
      if (response) {
        setOpenSnackbar(true);
        setOpenSnackbarMessage(response);
        setTimeout(() => {
          handleClose();
          setIsScheduleSending(false);
        }, 2000);
      }
    } catch (err) {
      console.error("Failed to schedule email", err);
    } finally {
      // setIsScheduleSending(false); // ✅ Re-enable if needed
    }
  };

  // Handle send email
  const handleSendEmail = async () => {
    traceSpan(`download_excel_launchreport`, {
      event: `download_excel_launchreport`,
      pageUrl: window.location.pathname,
      timestamp: new Date().toISOString(),
      userId: localStorage.getItem("user") || "",
    });
    const blob = buttonClickCallback(1);
    if (!blob) {
      console.error("No file generated");
      return;
    }

    setIsSending(true); // ⛔ Disable button
    setOpenSnackbar(true);
    setOpenSnackbarMessage("Sending.......");
    const file = new File([blob], "Launch Progress Report.xlsx", {
      type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    });
    const today = new Date();
    const formattedDate = `${String(today.getMonth() + 1).padStart(
      2,
      "0"
    )}-${String(today.getDate()).padStart(2, "0")}-${today.getFullYear()}`;

    const formData = new FormData();
    // const fromMail = process.env.REACT_APP_SMTP_FROM_MAIL ?? "";
    // formData.append("from", fromMail);
    formData.append("to", emailForm.email);
    formData.append("subject", "Launch Progress Report " + formattedDate);
    formData.append("text", emailForm.message);
    formData.append("attachment", file);
    try {
      await axios.post(
        process.env.REACT_APP_EMAIL_SERVICE_URL + "/api/send-email",
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );
      setOpenSnackbarMessage("Mail Sent Successfully");
      setTimeout(() => {
        setMailDialogOpen(false);
        setIsSending(false);
      }, 1000);
      setEmailForm({ email: "", message: "", emailError: false });
    } catch (error) {
      console.error("Failed to send email:", error);
    } finally {
      //setIsSending(false); // ✅ Re-enable if needed
    }
  };

  useEffect(() => {
    function adjustMainHeight() {
      const main = document.getElementById("main-content");
      if (main) {
        main.style.height = "auto";
        const contentHeight = main.scrollHeight + 15;
        main.style.height = contentHeight + "px";
      }

      const mainHot = document.getElementById("hot");
      if (mainHot) {
        mainHot.style.width = sidebarIsOpen ? "90vw" : "87vw";
      }
    }

    //adjustMainHeight();

    window.addEventListener("resize", adjustMainHeight);

    return () => {
      window.removeEventListener("resize", adjustMainHeight);
    };
  }, [sidebarIsOpen]);
  useEffect(() => {
    // disable scroll when this page is mounted
    document.documentElement.style.overflow = "hidden";
    document.body.style.overflow = "hidden";

    return () => {
      // re-enable scroll when leaving this page
      document.documentElement.style.overflow = "";
      document.body.style.overflow = "";
    };
  }, []);

  useEffect(() => {
    // const syncScroll = () => {
    //   const container = document.getElementById("hot");
    //   const customScrollbar = document.getElementById("customScrollbar");
    //   if (container && customScrollbar && customScrollbar.firstElementChild) {
    //     const totalTableWidth = 900;
    //     const fixedColsCount = 3;
    //     const colWidth = 253;
    //     const fixedColumnsWidth = fixedColsCount * colWidth;
    //     const scrollableWidth = totalTableWidth - fixedColumnsWidth;
    //     customScrollbar.style.width = scrollableWidth + "px";
    //     const scrollableColsCount = 10 - fixedColsCount;
    //     const scrollableContentWidth = scrollableColsCount * colWidth;
    //     (customScrollbar.firstElementChild as HTMLElement).style.width =
    //       scrollableContentWidth + "px";
    //     const wtHolders = document.querySelectorAll(".wtHolder");
    //     wtHolders.forEach((wtHolder) => {
    //       const element = wtHolder as HTMLElement;
    //       if (element.closest(".ht_clone_left")) {
    //         element.style.setProperty("padding-bottom", "15px");
    //       }
    //       // Sync scroll positions both ways
    //       customScrollbar.addEventListener("scroll", () => {
    //         element.scrollLeft = customScrollbar.scrollLeft;
    //       });
    //       element.addEventListener("scroll", () => {
    //         customScrollbar.scrollLeft = element.scrollLeft;
    //       });
    //     });
    //   } else {
    //     console.warn(
    //       "Required elements not found: container, customScrollbar, or its first child."
    //     );
    //   }
    // };
    // Wait for next tick or Handsontable init, to be safe
    // const timeoutId = setTimeout(syncScroll, 100);
    // return () => {
    //   clearTimeout(timeoutId);
    // };
  }, []);

  useEffect(() => {
    fieldNames.forEach((field) => {
      GetDropDownValues(field).then((res: any) => {
        const source = res
          .map((opt: { customOptionValue: any }) => opt.customOptionValue)
          .sort((a: string, b: string) => a.localeCompare(b));
        const normalizedSource = moveSelectToTop(source);
        setDropdownSources((prev) => ({ ...prev, [field]: normalizedSource }));
      });
    });
  }, []);
  const moveSelectToTop = (source: any[]) => {
    const rest = source.filter((item) => item !== "-- Select --");
    return ["-- Select --", ...rest];
  };
  useEffect(() => {
    const hot = hotRef.current?.hotInstance;
    if (!hot) return;

    const afterFilterHandler = (filterValues: any) => {
      const visibleRows = hot.countVisibleRows?.() ?? hot.countRows();
      setShowNoData(visibleRows === 0);
      Object.keys(filterValues).forEach((colId) => {
        traceSpan(`filter_grid_${colId}`, {
          event: `filter_grid_${colId}`,
          pageUrl: window.location.pathname,
          timestamp: new Date().toISOString(),
          userId: localStorage.getItem("user") || "",
          column: colId,
          filterValue: JSON.stringify(filterValues[colId]),
        });
      });
    };

    hot.addHook("afterFilter", afterFilterHandler);
  }, []);
  useEffect(() => {
    const hot = hotRef.current?.hotInstance;

    if (!hot) return;

    // Wait until Handsontable is ready
    if (!isInitializing.current) {
      commentCells.forEach(({ row, col, comment }) => {
        safeSetComment(row, col, comment.value);
      });
    }
  }, [tableData]); // Or add any other dependency that updates the table
  function safeSetComment(row: number, col: number, commentValue: string) {
    isProgrammatic.current = true;
    hotRef.current?.hotInstance?.setCellMeta(row, col, "comment", {
      value: commentValue,
    });
    prevCommentRef.current[`${row}-${col}`] = commentValue;
    isProgrammatic.current = false;
  }
  const customDropdownRenderer = (
    instance: Handsontable,
    td: HTMLTableCellElement,
    row: number,
    col: number,
    prop: string | number,
    value: any,
    cellProperties: Handsontable.CellProperties
  ) => {
    // Use the default text renderer
    Handsontable.renderers.TextRenderer(
      instance,
      td,
      row,
      col,
      prop,
      value,
      cellProperties
    );
    // Display empty string if value is '--Select--'
    if (value == "-- Select --") {
      td.innerText = " ";
    }
    // Add dropdown icon
    const icon = document.createElement("span");
    icon.className = "dropdown-icon";
    icon.innerHTML = "▾"; // Or use ▼ / ⌄ or an actual icon

    // Make sure the icon doesn't block cell interaction
    icon.style.pointerEvents = "none";

    td.classList.add("always-dropdown-cell"); // Add class for styling
    td.appendChild(icon);
  };

  const formatDateRenderer = (
    instance: Handsontable.Core,
    td: HTMLTableCellElement,
    row: number,
    col: number,
    prop: string | number,
    value: any,
    cellProperties: Handsontable.CellProperties
  ) => {
    let displayValue = "";

    // if (value) {
    //   const parsedDate = new Date(value);
    //   const isValid = !isNaN(parsedDate.getTime());

    //   if (isValid) {
    //     const mm = String(parsedDate.getMonth() + 1).padStart(2, "0");
    //     const dd = String(parsedDate.getDate()).padStart(2, "0");
    //     const yyyy = String(parsedDate.getFullYear()).slice(-2);
    //     displayValue = `${mm}/${dd}/${yyyy}`;
    //   } else {
    //     displayValue = value; // fallback to original value if invalid
    //   }
    // }

    if (value && /^\d{4}-\d{2}-\d{2}$/.test(value)) {
      const [year, month, day] = value.split("-");
      displayValue = `${month}/${day}/${year.slice(-2)}`;
    } else if (value) {
      const parsedDate = new Date(value);
      if (!isNaN(parsedDate.getTime())) {
        const mm = String(parsedDate.getMonth() + 1).padStart(2, "0");
        const dd = String(parsedDate.getDate()).padStart(2, "0");
        const yyyy = String(parsedDate.getFullYear()).slice(-2);
        displayValue = `${mm}/${dd}/${yyyy}`;
      } else {
        displayValue = value;
      }
    }

    Handsontable.renderers.TextRenderer(
      instance,
      td,
      row,
      col,
      prop,
      displayValue,
      cellProperties
    );
    return td;
  };
  console.log("Table data >>>", tableData);
  const [showNoData, setShowNoData] = useState(false);
  const rowHeight = 22;
  const headerHeight = 30;
  const maxHeight = 600;

  var tableHeight =
    Math.min((tableData?.length || 1) * rowHeight + headerHeight, maxHeight) +
    50;
  const hotRef = useRef<HotTableClass>(null);
  const scrollRef = useRef(null);

  const TAGGED_CELL_CLASS = "tagged-cell";
  const columns: ColumnSettings[] = [
    {
      title: "OB Age",
      type: "numeric",
      data: "obAge",
      width: 30,
      headerClassName: "center-header",
      editor: false,
    },
    {
      title: "Group Name",
      type: "text",
      data: "groupName",
      width: 70,
      headerClassName: "center-header",
      editor: false,
    },
    {
      title: "Store Name",
      type: "text",
      data: "storeName",
      width: 180,
      headerClassName: "center-header",
      editor: false,
    },
    {
      title: "OB<br>Coord",
      type: "dropdown",
      data: "onboardingCoordinator",
      width: 50,
      selectOptions: dropdownSources["Onboarding Coordinator"],
      renderer: customDropdownRenderer,
      headerClassName: "center-header",
      strict: true,
      allowInvalid: false,
      editor: "select",
      className: "dropdown-only",
    },
    {
      title: "DMS",
      type: "dropdown",
      data: "dms",
      // className: "htCenter",
      width: 40,
      selectOptions: dropdownSources["DMS"],
      renderer: customDropdownRenderer,
      headerClassName: "center-header",
      strict: true,
      allowInvalid: false,
      editor: "select",
      className: "dropdown-only",
    },
    {
      title: "Account<br>Received",
      type: "date",
      dateFormat: "YYYY-MM-DD",
      correctFormat: true,
      renderer: formatDateRenderer,
      data: "agreementReceived",
      width: 55,
      datePickerConfig: {
        maxDate: new Date(
          new Date().getFullYear(),
          new Date().getMonth(),
          new Date().getDate()
        ),
      },
      headerClassName: "center-header",
      strict: true,
      allowInvalid: false,
    },
    {
      title: "Requested<br>DMS Access",
      type: "date",
      dateFormat: "YYYY-MM-DD",
      correctFormat: true,
      data: "requestedDmsAccess",
      width: 55,
      headerClassName: "center-header",
      renderer: formatDateRenderer,
      strict: true,
      allowInvalid: false,
    },
    {
      title: "DMS<br>Active<br>Date",
      type: "date",
      dateFormat: "YYYY-MM-DD",
      correctFormat: true,
      renderer: formatDateRenderer,
      data: "dmsActiveDate",
      className: "htCenter",
      width: 55,
      headerClassName: "center-header",
      strict: true,
      allowInvalid: false,
    },
    // {
    //   title: "Requested OB Meeting Comments",
    //   type: "text",
    //   data: "requestedObMeetingComments",
    //   width: 100,
    // },
    {
      title: "Requested<br>OB<br>Meeting",
      type: "date",
      dateFormat: "YYYY-MM-DD",
      correctFormat: true,
      renderer: formatDateRenderer,
      data: "requestedObMeeting",
      width: 55,
      headerClassName: "center-header",
      strict: true,
      allowInvalid: false,
    },

    {
      title: "OB<br>Meeting<br>Completed",
      type: "date",
      dateFormat: "YYYY-MM-DD",
      correctFormat: true,
      renderer: formatDateRenderer,
      data: "obMeetingCompleted",
      width: 55,
      headerClassName: "center-header",
      strict: true,
      allowInvalid: false,
    },
    {
      title: "OpCodes<br>Available",
      type: "date",
      dateFormat: "YYYY-MM-DD",
      correctFormat: true,
      renderer: formatDateRenderer,
      data: "opCodesAvailable",
      width: 55,
      headerClassName: "center-header",
      strict: true,
      allowInvalid: false,
    },
    {
      title: "OpCodes<br>Categorized",
      type: "date",
      dateFormat: "YYYY-MM-DD",
      correctFormat: true,
      renderer: formatDateRenderer,
      data: "opCodesCategorized",
      width: 55,
      headerClassName: "center-header",
      strict: true,
      allowInvalid: false,
    },
    {
      title: "Grid<br>Received",
      type: "date",
      dateFormat: "YYYY-MM-DD",
      correctFormat: true,
      renderer: formatDateRenderer,
      data: "laborPricingGridReceived",
      width: 55,
      headerClassName: "center-header",
      strict: true,
      allowInvalid: false,
    },
    {
      title: "Grid In<br>BZO",
      type: "date",
      dateFormat: "YYYY-MM-DD",
      correctFormat: true,
      renderer: formatDateRenderer,
      data: "laborPricingGridEnteredInBzo",
      width: 55,
      headerClassName: "center-header",
      strict: true,
      allowInvalid: false,
    },
    {
      title: "Model<br>Mapping",
      type: "date",
      dateFormat: "YYYY-MM-DD",
      correctFormat: true,
      renderer: formatDateRenderer,
      data: "modelMapping",
      width: 55,
      headerClassName: "center-header",
      strict: true,
      allowInvalid: false,
    },
    {
      title: "Matrix<br>Received",
      type: "date",
      dateFormat: "YYYY-MM-DD",
      correctFormat: true,
      renderer: formatDateRenderer,
      data: "partsMatrixReceived",
      width: 55,
      headerClassName: "center-header",
      strict: true,
      allowInvalid: false,
    },
    {
      title: "Matrix In<br>BZO",
      type: "date",
      dateFormat: "YYYY-MM-DD",
      correctFormat: true,
      renderer: formatDateRenderer,
      data: "partsMatrixEnteredInBzo",
      width: 55,
      headerClassName: "center-header",
      strict: true,
      allowInvalid: false,
    },
    {
      title: "Users<br>Created",
      type: "date",
      dateFormat: "YYYY-MM-DD",
      correctFormat: true,
      renderer: formatDateRenderer,
      data: "usersCreated",
      width: 55,
      headerClassName: "center-header",
      strict: true,
      allowInvalid: false,
    },
    {
      title: "Smoke Test<br>Completed",
      type: "date",
      dateFormat: "YYYY-MM-DD",
      correctFormat: true,
      renderer: formatDateRenderer,
      data: "smokeTestCompleted",
      width: 55,
      headerClassName: "center-header",
      strict: true,
      allowInvalid: false,
    },
    {
      title: "Ready For<br>Review",
      type: "date",
      dateFormat: "YYYY-MM-DD",
      correctFormat: true,
      renderer: formatDateRenderer,
      data: "readyForReview",
      width: 55,
      headerClassName: "center-header",
      strict: true,
      allowInvalid: false,
    },
    {
      title: "Review<br>Completed",
      type: "date",
      dateFormat: "YYYY-MM-DD",
      correctFormat: true,
      renderer: formatDateRenderer,
      data: "reviewCompleted",
      width: 55,
      headerClassName: "center-header",
      strict: true,
      allowInvalid: false,
    },

    {
      title: "Coach",
      type: "dropdown",
      data: "coach",
      width: 50,
      selectOptions: dropdownSources["Coach"],
      renderer: customDropdownRenderer,
      headerClassName: "center-header",
      strict: true,
      allowInvalid: false,
      editor: "select",
      className: "dropdown-only",
    },
    {
      title: "Sales<br>Person",
      type: "dropdown",
      data: "salesperson",
      width: 60,
      selectOptions: dropdownSources["Salesperson"],
      renderer: customDropdownRenderer,
      headerClassName: "center-header",
      strict: true,
      allowInvalid: false,
      editor: "select",
      className: "dropdown-only",
    },
    {
      title: "Scheduled<br>Launch",
      type: "date",
      dateFormat: "YYYY-MM-DD",
      correctFormat: true,
      renderer: formatDateRenderer,
      data: "scheduledLaunch",
      width: 55,
      headerClassName: "center-header",
      strict: true,
      allowInvalid: false,
    },
    {
      title: "Launch<br>Date",
      type: "date",
      dateFormat: "YYYY-MM-DD",
      correctFormat: true,
      renderer: formatDateRenderer,
      data: "launchDate",
      width: 55,
      headerClassName: "center-header",
      strict: true,
      allowInvalid: false,
    },
    {
      title: "Launch<br>Completed",
      type: "date",
      dateFormat: "YYYY-MM-DD",
      correctFormat: true,
      renderer: formatDateRenderer,
      data: "launchCompleted",
      width: 55,
      headerClassName: "center-header",
      strict: true,
      allowInvalid: false,
      datePickerConfig: {
        // Pikaday position options: 'top left', 'top right', 'bottom left', 'bottom right'
        position: "top right",
        reposition: false, // prevents auto repositioning
      },
    },
    {
      title: "Total Days<br>To Launch",
      type: "numeric",
      data: "totalDaysToLaunch",
      width: 50,
      headerClassName: "center-header",
      editor: false,
    },
    {
      title: "Group<br>Pay",
      type: "dropdown",
      data: "tagGroupPay",
      selectOptions: dropdownSources["Tag Group Pay"],
      renderer: customDropdownRenderer,
      width: 40,
      headerClassName: "center-header",
      strict: true,
      allowInvalid: false,
      editor: "select",
      className: "dropdown-only",
    },
  ].filter(
    (col) => selectedKey == "allStores" || col.data != "totalDaysToLaunch"
  );

  const commentColumnMapping: Record<string, string> = {
    groupName: "groupNameComments",
    storeName: "storeNameComments",
    requestedObMeeting: "requestedObMeetingComments",
    requestedDmsAccess: "requestedDmsAccessComments",
    obMeetingCompleted: "obMeetingCompletedComments",
    laborPricingGridReceived: "laborPricingGridReceivedComments",
    modelMapping: "modelMappingComments",
    partsMatrixReceived: "partsMatrixReceivedComments",
    usersCreated: "usersCreatedComments",
    coach: "coachComments",
    obAge: "obAgeComments",
    onboardingCoordinator: "onboardingCoordinatorComments",
    agreementReceived: "agreementReceivedComments",
    dms: "dmsComments",
    dmsActiveDate: "dmsActiveDateComments",
    opCodesAvailable: "opCodesAvailableComments",
    opCodesCategorized: "opCodesCategorizedComments",
    laborPricingGridEnteredInBzo: "laborPricingGridEnteredInBzoComments",
    partsMatrixEnteredInBzo: "partsMatrixEnteredInBzoComments",
    smokeTestCompleted: "smokeTestCompletedComments",
    readyForReview: "readyForReviewComments",
    reviewCompleted: "reviewCompletedComments",
    salesperson: "salespersonComments",
    scheduledLaunch: "scheduledLaunchComments",
    launchCompleted: "launchCompletedComments",
    launchDate: "launchDateComments",
    totalDaysToLaunch: "totalDaysToLaunchComments",
    tagGroupPay: "tagGroupPayComments",
  };
  const tagColumnMapping: Record<string, string> = {
    groupName: "taggrouphold",
    storeName: "tagStoreHold",
    dms: "tagDmsAction",
    requestedDmsAccess: "tagRequestedDmsAccessFollowup",
    requestedObMeeting: "tagRequestedObMeetingFollowup",
    obMeetingCompleted: "tagObMeetingCompletedAction",
    laborPricingGridReceived: "tagLaborPricingGridReceived",
    scheduledLaunch: "tagScheduledLaunchActionNeeded",
    partsMatrixReceived: "tagPartsMatrixReceivedFollowup", // double mapping (check if intentional)
    obAge: "tagObAgeAction",
    onboardingCoordinator: "tagOnboardingCoordinatorAction",
    agreementReceived: "tagAgreementReceivedAction",
    dmsActiveDate: "tagDmsActiveDateAction",
    opCodesAvailable: "tagOpCodesAvailableAction",
    opCodesCategorized: "tagOpCodesCategorizedAction",
    laborPricingGridEnteredInBzo: "tagLaborPricingGridEnteredInBzoAction",
    modelMapping: "tagModelMappingAction",
    partsMatrixEnteredInBzo: "tagPartsMatrixEnteredInBzoAction",
    usersCreated: "tagUsersCreatedAction",
    smokeTestCompleted: "tagSmokeTestCompletedAction",
    readyForReview: "tagReadyForReviewAction",
    reviewCompleted: "tagReviewCompletedAction",
    coach: "tagCoachAction",
    salesperson: "tagSalespersonAction",
    launchCompleted: "tagLaunchCompletedAction",
    launchDate: "tagStoreLaunchDate",
    totalDaysToLaunch: "tagTotalDaysToLaunchAction",
    tagGroupPay: "tagTagGroupPayAction",
    tagStoreReadyToLaunch: "tagStoreReadyToLaunch",
  };

  // Map column keys to indices
  const columnKeys = columns.map((col: any) => col.data);
  const commentCells: {
    row: number;
    col: number;
    comment: { value: string };
  }[] = tableData.flatMap((row: Record<string, any>, rowIndex: number) =>
    Object.entries(commentColumnMapping).flatMap(([dataKey, commentKey]) => {
      const colIndex = columnKeys.indexOf(dataKey);
      const commentValue = row[commentKey];

      // Only add comment cell if value is non-empty and column exists
      if (colIndex !== -1 && commentValue?.trim()) {
        return [
          {
            row: rowIndex,
            col: colIndex,
            comment: { value: commentValue },
          },
        ];
      }

      return []; // Skip empty comment
    })
  );

  const tagCells = tableData.flatMap(
    (row: Record<string, any>, rowIndex: number) =>
      Object.entries(tagColumnMapping).flatMap(([dataKey, tagKey]) => {
        const tagValue = row[tagKey];
        const colIndex = columnKeys.indexOf(dataKey);
        const isGroupHold = row?.taggrouphold === "t";
        const isStoreHold = row?.tagStoreHold === "t";
        const isLaunchReady = row?.tagStoreReadyToLaunch === "t";
        if (colIndex !== -1 && tagValue === "t") {
          if (isGroupHold) {
            return columnKeys.map((_, colIdx) => ({
              row: rowIndex,
              col: colIdx,
              className: "onholdGrp-cell",
            }));
          }

          if (isStoreHold) {
            return columnKeys.map((_, colIdx) => ({
              row: rowIndex,
              col: colIdx,
              className: "onhold-cell",
            }));
          }

          if (isLaunchReady) {
            return columnKeys.map((_, colIdx) => ({
              row: rowIndex,
              col: colIdx,
              className: "launch-ready-cell",
            }));
          }

          // default tagged
          return [
            {
              row: rowIndex,
              col: colIndex,
              className: "tagged-cell",
            },
          ];
        }

        return [];
      })
  );

  const mergedCells = [...commentCells, ...tagCells];

  const handleTextFieldUpdate = (
    row: number,
    col: number,
    key: string,
    value: any
  ) => {
    const newValue = value.value;
    const cellKey = `${row}-${col}`;
    const prevValue = prevCommentRef.current[cellKey] ?? "";

    const fieldNameMapComments: { [key: string]: string } = {
      modelMappingComments: "Model Mapping - Comments",
      groupNameComments: "Group Name Comments",
      storeNameComments: "Store Name Comments",
      requestedObMeetingComments: "Requested OB Meeting - Comments",
      requestedDmsAccessComments: "Requested DMS Access - Comments",
      obMeetingCompletedComments: "OB Meeting Completed - Comments",
      laborPricingGridReceivedComments:
        "Labor Pricing/Grid Received - Comments",
      partsMatrixReceivedComments: "Parts Matrix Received - Comments",
      usersCreatedComments: "Users Created - Comments",
      coachComments: "Coach - Comments",
      obAgeComments: "obAgeComments",
      onboardingCoordinatorComments: "onboardingCoordinatorComments",
      agreementReceivedComments: "agreementReceivedComments",
      dmsComments: "dmsComments",
      dmsActiveDateComments: "dmsActiveDateComments",
      opCodesAvailableComments: "opCodesAvailableComments",
      opCodesCategorizedComments: "opCodesCategorizedComments",
      laborPricingGridEnteredInBzoComments:
        "laborPricingGridEnteredInBzoComments",
      partsMatrixEnteredInBzoComments: "partsMatrixEnteredInBzoComments",
      smokeTestCompletedComments: "smokeTestCompletedComments",
      readyForReviewComments: "readyForReviewComments",
      reviewCompletedComments: "reviewCompletedComments",
      salespersonComments: "salespersonComments",
      scheduledLaunchComments: "scheduledLaunchComments",
      launchCompletedComments: "launchCompletedComments",
      launchDateComments: "launchDateComments",
      //totalDaysToLaunchComments: "totalDaysToLaunchComments",
      tagGroupPayComments: "tagGroupPayComments",
    };
    const fieldNameMap: { [key: string]: string } = {
      groupName: "Tag Group Hold",
      storeName: "Tag Store Hold",
      dms: "Tag DMS Action",
      requestedDmsAccess: "Tag Requested DMS Access Followup",
      requestedObMeeting: "Tag Requested OB Meeting Followup",
      obMeetingCompleted: "Tag OB Meeting Completed Action",
      laborPricingGridReceived: "Tag Labor Pricing Grid Received",
      partsMatrixReceived: "Tag Parts Matrix Received Followup",
      scheduledLaunch: "Tag Scheduled Launch Action Needed",
      obAge: "tagObAgeAction",
      onboardingCoordinator: "tagOnboardingCoordinatorAction",
      agreementReceived: "tagAgreementReceivedAction",
      dmsActiveDate: "tagDmsActiveDateAction",
      opCodesAvailable: "tagOpCodesAvailableAction",
      opCodesCategorized: "tagOpCodesCategorizedAction",
      laborPricingGridEnteredInBzo: "tagLaborPricingGridEnteredInBzoAction",
      modelMapping: "tagModelMappingAction",
      partsMatrixEnteredInBzo: "tagPartsMatrixEnteredInBzoAction",
      usersCreated: "tagUsersCreatedAction",
      smokeTestCompleted: "tagSmokeTestCompletedAction",
      readyForReview: "tagReadyForReviewAction",
      reviewCompleted: "tagReviewCompletedAction",
      coach: "tagCoachAction",
      salesperson: "tagSalespersonAction",
      launchCompleted: "tagLaunchCompletedAction",
      launchDate: "tagStoreLaunchDate",
      // totalDaysToLaunch: "tagTotalDaysToLaunchAction",
      tagGroupPay: "tagTagGroupPayAction",
    };

    if (selectedKey == "allStores") {
      fieldNameMap.totalDaysToLaunch = "tagTotalDaysToLaunchAction";
      fieldNameMapComments.totalDaysToLaunchComments =
        "totalDaysToLaunchComments";
    }
    const workPackageId = tableData[row]?.slno;
    const storname = tableData[row]?.storeName;
    const groupname = tableData[row]?.groupName;
    // tag functionality
    if (
      key === "addTags" ||
      key === "readyToLaunchTag" ||
      key === "removeTags"
    ) {
      const fieldName: string = columns[col]?.data as string; // e.g. "modelMappingComments"
      let activity = "";
      if (key === "addTags") {
        traceSpan(`add_launchreporttag`, {
          event: `add_launchreporttag`,
          pageUrl: window.location.pathname,
          timestamp: new Date().toISOString(),
          userId: localStorage.getItem("user") || "",
          file: `Tags added for ${fieldName}`,
        });
        activity = `Tags added for ${fieldName}`;
      } else if (key === "readyToLaunchTag") {
        traceSpan(`add_readyToLaunchtag`, {
          event: `add_readyToLaunchtag`,
          pageUrl: window.location.pathname,
          timestamp: new Date().toISOString(),
          userId: localStorage.getItem("user") || "",
          file: `Tags added for ${fieldName}`,
        });
        activity = `Tags added for ${fieldName}`;
      } else {
        traceSpan(`remove_launchreporttag`, {
          event: `remove_launchreporttag`,
          pageUrl: window.location.pathname,
          timestamp: new Date().toISOString(),
          userId: localStorage.getItem("user") || "",
          file: `Tags removed for ${fieldName}`,
        });
        activity = `Tags removed for ${fieldName}`;
      }

      const fieldValue = value;
      const displayName =
        key === "readyToLaunchTag"
          ? "tagStoreReadyToLaunch"
          : fieldNameMap[fieldName];

      if (displayName && fieldValue && workPackageId) {
        UpdateTextFieldComments(
          displayName,
          fieldValue,
          workPackageId,
          storname,
          groupname,
          activity
        )
          .then(() => {
            console.log(`Tags saved for ${fieldName}`);
            if (key === "addTags" || key === "readyToLaunchTag") {
              if (
                displayName == "tagStoreReadyToLaunch" &&
                key === "readyToLaunchTag"
              ) {
                console.log("neela");
                UpdateTextFieldComments(
                  "Tag Store Hold",
                  "false",
                  workPackageId,
                  storname,
                  groupname,
                  "Store Hold Tags removed for storeName"
                )
                  .then(() => {
                    // console.log(`Tags saved for ${fieldName}`);
                  })
                  .catch((err) => {
                    //console.error("Failed to save tags", err);
                  });
              }
              if (displayName == "Tag Store Hold" && key === "addTags") {
                console.log("orange");
                UpdateTextFieldComments(
                  "tagStoreReadyToLaunch",
                  "false",
                  workPackageId,
                  storname,
                  groupname,
                  "Ready To Launch Tags removed for storeName"
                )
                  .then(() => {
                    // console.log(`Tags saved for ${fieldName}`);
                  })
                  .catch((err) => {
                    //console.error("Failed to save tags", err);
                  });
              }
            } else if (key === "removeTags") {
              UpdateTextFieldComments(
                "tagStoreReadyToLaunch",
                "false",
                workPackageId,
                storname,
                groupname,
                "Ready To Launch Tags removed for storeName"
              )
                .then(() => {
                  // console.log(`Tags saved for ${fieldName}`);
                })
                .catch((err) => {
                  //console.error("Failed to save tags", err);
                });
            }
          })
          .catch((err) => {
            console.error("Failed to save tags", err);
          });
      }
    }
    // deleteComment Functionality
    else if (key === "commentsRemove") {
      const fieldName = columns[col].data + "Comments";
      const fieldValue = value.value;
      const activity = `Comment deleted for ${fieldName}`;
      traceSpan(`remove_launchreportcomment`, {
        event: `remove_launchreportcomment`,
        pageUrl: window.location.pathname,
        timestamp: new Date().toISOString(),
        userId: localStorage.getItem("user") || "",
        file: `Comment deleted for ${fieldName}`,
      });
      const displayName = fieldNameMapComments[fieldName];
      if (displayName && workPackageId) {
        UpdateTextFieldComments(
          displayName,
          "",
          workPackageId,
          storname,
          groupname,
          activity
        )
          .then(() => {
            console.log(`Comment Deleted for ${fieldName}`);
          })
          .catch((err) => {
            console.error("Failed to save comment", err);
          });
      }
    }

    if (key === "comment" && newValue !== prevValue) {
      console.log("commmm", columns[col]?.data + "Comments");
      const fieldName = columns[col]?.data + "Comments";
      traceSpan(`input_launchreportcomment_textarea`, {
        event: `input_launchreportcomment_textarea`,
        pageUrl: window.location.pathname,
        timestamp: new Date().toISOString(),
        userId: localStorage.getItem("user") || "",
        value: newValue,
        storename: storname,
        groupname: groupname,
      });
      const activity = `Comment updated for ${fieldName}`;
      const displayName = fieldNameMapComments[fieldName];
      console.log("displayNamemm", newValue, prevValue);
      console.log("workPackageId", workPackageId);
      if (displayName && workPackageId) {
        console.log("testtstttsst");
        UpdateTextFieldComments(
          displayName,
          newValue,
          workPackageId,
          storname,
          groupname,
          activity
        )
          .then(() => {
            traceSpan(`edit_launchreportcomment`, {
              event: `edit_launchreportcomment`,
              pageUrl: window.location.pathname,
              timestamp: new Date().toISOString(),
              userId: localStorage.getItem("user") || "",
              file: `Comment updated for ${fieldName}`,
            });
            console.log(`Comment updated for ${fieldName}`);
            prevCommentRef.current[cellKey] = newValue; // Update tracker
          })
          .catch((err) => {
            console.error("Failed to update comment:", err);
          });
      }
    }
    // AddComment Functionality
    // else if (
    //   key === "comment" &&
    //   typeof value?.value === "string" &&
    //   value?.value == ""
    // ) {
    //   const fieldName = columns[col].data + "Comments"; // e.g. "modelMappingComments"
    //   const fieldValue = value.value;
    //   const displayName = fieldNameMapComments[fieldName];

    //   if (displayName && workPackageId) {
    //     UpdateTextFieldComments(displayName, "", workPackageId)
    //       .then(() => {
    //         console.log(`Comment Deleted for ${fieldName}`);
    //       })
    //       .catch((err) => {
    //         console.error("Failed to save comment", err);
    //       });
    //   }
    // }
    // // Edit/Clear comment Functionality
    // else if (key === "comment" && value?.value && value?.value != "") {
    //   const newValue = typeof value === "string" ? value : value?.value ?? "";
    //   const cellKey = `${row}-${col}`;
    //   const prevValue = prevCommentRef.current[cellKey];

    //   const fieldName = columns[col].data + "Comments"; // e.g. "modelMappingComments"
    //   const fieldValue = value.value;
    //   if (
    //     (prevValue && newValue !== prevValue) ||
    //     (typeof prevValue === "string" && prevValue == "")
    //   ) {
    //     const displayName = fieldNameMapComments[fieldName];

    //     if (displayName && fieldValue && workPackageId) {
    //       UpdateTextFieldComments(displayName, fieldValue, workPackageId)
    //         .then(() => {
    //           console.log(`Comment saved for ${fieldName}`);
    //         })
    //         .catch((err) => {
    //           console.error("Failed to save comment", err);
    //         });
    //     }
    //   }
    // }
  };
  function convertDateFormat(dateStr: string): string {
    if (!dateStr || typeof dateStr !== "string") {
      throw new Error("Invalid input: dateStr is required.");
    }

    // ✅ If already in YYYY-MM-DD format, return it
    if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
      return dateStr;
    }

    const parts = dateStr.split("/");

    if (parts.length !== 3) {
      throw new Error("Invalid date format. Expected MM/DD/YYYY.");
    }

    let [month, day, year] = parts;

    if (!month || !day || !year) {
      throw new Error("Missing date parts. Expected MM/DD/YYYY.");
    }

    month = month.trim().padStart(2, "0");
    day = day.trim().padStart(2, "0");

    return `${year.trim()}-${month}-${day}`;
  }

  const handleSaveDropdown = (
    row: number,
    prop: string,
    newValue: any,
    oldValue: any
  ) => {
    traceSpan(`click_addstore_dropdown`, {
      event: `click_addstore_dropdown`,
      pageUrl: window.location.pathname,
      timestamp: new Date().toISOString(),
      userId: localStorage.getItem("user") || "",
      value: newValue,
    });
    const workPackageId = Number(tableData[row]?.slno);
    const storname = tableData[row]?.storeName;
    const groupname = tableData[row]?.groupName;

    const contentValue = newValue;
    const fieldNameMap: { [key: string]: string } = {
      tagGroupPay: "Tag Group Pay",
      onboardingCoordinator: "Onboarding Coordinator",
      salesperson: "Salesperson",
      dms: "DMS",
      coach: "Coach",
    };

    const displayName = fieldNameMap[prop];
    const activity = `Value updated for ${displayName}`;
    if (displayName && contentValue && workPackageId) {
      UpdateDropdownField(
        displayName,
        contentValue,
        workPackageId,
        storname,
        groupname,
        activity
      )
        .then(() => {
          console.log(`Value updated for ${displayName}`);
        })
        .catch((err) => {
          console.error("Failed to save comment", err);
        });
    }
  };
  const handleSaveDatePicker = (
    row: number,
    prop: string,
    newValue: any,
    oldValue: any
  ) => {
    const workPackageId = tableData[row]?.slno;
    const storname = tableData[row]?.storeName;
    const groupname = tableData[row]?.groupName;
    //13/03/2025-2025-03-13
    const DateValue = newValue && convertDateFormat(newValue);
    const fieldNameMap: { [key: string]: string } = {
      agreementReceived: "Agreement Received",
      requestedDmsAccess: "Requested DMS Access",
      dmsActiveDate: "DMS Active Date",
      obMeetingCompleted: "OB Meeting Completed",
      opCodesAvailable: "Op Codes Available",
      opCodesCategorized: "Op Codes Categorized",
      laborPricingGridReceived: "Labor Pricing/Grid Received",

      laborPricingGridEnteredInBzo: "Labor Pricing/Grid Entered in BZO",
      modelMapping: "Model Mapping",
      partsMatrixReceived: "Parts Matrix Received",
      partsMatrixEnteredInBzo: "Parts Matrix Entered in BZO",
      usersCreated: "Users Created",
      smokeTestCompleted: "Smoke Test Completed",
      readyForReview: "Ready For Review",
      reviewCompleted: "Review Completed",
      scheduledLaunch: "Scheduled Launch",
      launchCompleted: "Launch Completed",
      launchDate: "Launch Date",
      requestedObMeeting: "Requested OB Meeting",
    };
    const displayName = fieldNameMap[prop];
    const activity = `Comment saved for ${displayName}`;

    if (displayName && DateValue && workPackageId) {
      UpdateTextFieldComments(
        displayName,
        DateValue,
        workPackageId,
        storname,
        groupname,
        activity
      )
        .then(() => {
          console.log(`Comment saved for ${displayName}`);
          // const hot = hotRef.current?.hotInstance;
          // hot?.render();
          //setRefreshTrigger((prev) => prev + 1);
          if (
            displayName == "Launch Completed" ||
            displayName == "DMS Active Date" ||
            displayName == "Agreement Received" ||
            displayName == "OB Meeting Completed" ||
            displayName == "Op Codes Categorized" ||
            displayName == "Labor Pricing/Grid Entered in BZO" ||
            displayName == "Model Mapping" ||
            displayName == "Parts Matrix Entered in BZO" ||
            displayName == "Users Created" ||
            displayName == "Scheduled Launch"
          ) {
            refreshTrigger();
          }
        })
        .catch((err) => {
          console.error("Failed to save comment", err);
        });
    }
  };

  const columnsToHide = [
    "laborPricingGridReceivedComments",
    "modelMappingComments",
    "obMeetingCompletedComments",
    "partsMatrixReceivedComments",
    "requestedDmsAccessComments",
    "usersCreatedComments",
    "coachComments",
    "groupNameComments",
    "storeNameComments",
    "requestedObMeetingComments",
  ];
  const allowedTagColumns = [
    "groupName",
    "storeName",
    "dms",
    "requestedDmsAccess",
    "requestedObMeeting",
    "obMeetingCompleted",
    "laborPricingGridReceived",
    "scheduledLaunch",
    "partsMatrixReceived",
    "obAge",
    "onboardingCoordinator",
    "agreementReceived",
    "dmsActiveDate",
    "opCodesAvailable",
    "opCodesCategorized",
    "laborPricingGridEnteredInBzo",
    "modelMapping",
    "partsMatrixEnteredInBzo",
    "usersCreated",
    "smokeTestCompleted",
    "readyForReview",
    "reviewCompleted",
    "coach",
    "salesperson",
    "launchCompleted",
    "launchDate",
    "totalDaysToLaunch",
    "tagGroupPay",
  ];
  const hiddenColumnIndexes = columns
    .map((col: any, index: any) =>
      columnsToHide.includes(col.data) ? index : -1
    )
    .filter((index) => index !== -1);
  //comments allowing columns
  const allowedCommentColumns = [
    "obAge",
    "groupName",
    "storeName",
    "onboardingCoordinator",
    "dms",
    "agreementReceived",
    "requestedDmsAccess",
    "dmsActiveDate",
    "requestedObMeeting",
    "obMeetingCompleted",
    "opCodesAvailable",
    "opCodesCategorized",
    "laborPricingGridReceived",
    "laborPricingGridEnteredInBzo",
    "modelMapping",
    "partsMatrixReceived",
    "partsMatrixEnteredInBzo",
    "usersCreated",
    "smokeTestCompleted",
    "readyForReview",
    "reviewCompleted",
    "coach",
    "salesperson",
    "scheduledLaunch",
    "launchCompleted",
    "launchDate",
    "totalDaysToLaunch",
    "tagGroupPay",
  ];
  const columnTagMap: { [key: string]: string } = {
    groupName: "On hold tag",
    storeName: "On hold tag",
    dms: "Action Needed tag",
    obMeetingCompleted: "Action Needed tag",
    laborPricingGridReceived: "Action Needed tag",
    partsMatrixReceived: "Action Needed tag",
    scheduledLaunch: "Action Needed tag",
    requestedDmsAccess: "Follow Up tag",
    requestedObMeeting: "Follow Up tag",

    obAge: "Action Needed tag",

    onboardingCoordinator: "Action Needed tag",

    agreementReceived: "Action Needed tag",

    dmsActiveDate: "Action Needed tag",

    opCodesAvailable: "Action Needed tag",
    opCodesCategorized: "Action Needed tag",

    laborPricingGridEnteredInBzo: "Action Needed tag",
    modelMapping: "Action Needed tag",

    partsMatrixEnteredInBzo: "Action Needed tag",
    usersCreated: "Action Needed tag",
    smokeTestCompleted: "Action Needed tag",
    readyForReview: "Action Needed tag",
    reviewCompleted: "Action Needed tag",
    coach: "Action Needed tag",
    salesperson: "Action Needed tag",

    launchCompleted: "Action Needed tag",
    launchDate: "Action Needed tag",
    totalDaysToLaunch: "Action Needed tag",
    tagGroupPay: "Action Needed tag",

    // Add more column-property-to-tag-name mappings as needed
  };
  //context menu custom items for enabling and disabling
  const contextMenu: {
    callback: (key: string, options: any[]) => void;
    items: Record<string, MenuItemConfig>;
  } = {
    callback: (key, options) => {
      const cell = options[0]; // selection info

      const hot = hotRef.current?.hotInstance;
      if (!hot || !options?.[0]) return;

      const { start } = options[0];
      const row = start?.row;
      const col = start?.col;

      if (row == null || col == null) return;

      if (key === "addTags" || key === "readyToLaunchTag") {
        if (col === 1) {
          const colCount = hot.countCols(); // get total number of columns

          for (let i = 0; i < colCount; i++) {
            hot.setCellMeta(row, i, "className", "onholdGrp-cell");
          }

          hot.render(); // force re-render to apply the class
        } else if (col === 2) {
          const colCount = hot.countCols(); // get total number of columns

          for (let i = 0; i < colCount; i++) {
            hot.setCellMeta(
              row,
              i,
              "className",
              key === "readyToLaunchTag" ? "launch-ready-cell" : "onhold-cell"
            );
          }

          hot.render(); // force re-render to apply the class
        } else {
          hot.setCellMeta(row, col, "className", "tagged-cell");
          hot.render();
        }

        handleTextFieldUpdate(row, col, key, "true");
      } else if (key === "removeTags") {
        if (col === 1 || col === 2) {
          const colCount = hot.countCols(); // get total number of columns

          for (let i = 0; i < colCount; i++) {
            hot.removeCellMeta(row, i, "className");
          }

          hot.render();
        } else {
          hot.removeCellMeta(row, col, "className");
          hot.render();
        }

        // hot.removeCellMeta(row, col, "className");
        // hot.render();
        handleTextFieldUpdate(row, col, key, "false");
      } else if (key === "commentsRemove") {
        hot.setCellMeta(row, col, "comment", undefined);
        hot.render(); // Already calling render here
        handleTextFieldUpdate(row, col, key, "");
      }
    },
    items: {
      commentsAddEdit: {
        name: "Add/Edit Comment",
        disabled(this: Handsontable) {
          const selection = this.getSelectedLast();
          if (!selection) return true;
          const [, col] = selection;
          const prop = this.colToProp(col);
          return !allowedCommentColumns.includes(prop as string);
        },
        callback(this: Handsontable) {
          // Add this callback to handle comment editing
          const selection = this.getSelectedLast();
          if (!selection) return;
          const [row, col] = selection;
          this.getPlugin("comments").showAtCell(row, col);
          this.render(); // Force refresh after showing comment editor
        },
      },
      commentsRemove: {
        name: "Delete Comment",
        disabled(this: Handsontable) {
          const selection = this.getSelectedLast();
          if (!selection) return true;
          const [row, col] = selection;
          const prop = this.colToProp(col);

          // First check if it's in allowed columns
          if (!allowedCommentColumns.includes(prop as string)) return true;

          // Then check if the cell actually has a comment
          const cellMeta = this.getCellMeta(row, col);
          return !cellMeta.comment || !cellMeta.comment.value;
        },
      },
      addTags: {
        // name: "Add Tags",
        name(this: Handsontable) {
          const selection = this.getSelectedLast();
          if (!selection) return "Add Tags";

          const [, col] = selection;
          const prop = this.colToProp(col);
          const tagName = columnTagMap[prop as string] || "Add Tags";

          return `${tagName}`;
        },
        disabled(this: Handsontable) {
          const selection = this.getSelectedLast();
          if (!selection) return true;

          const [row, col] = selection;
          const prop = this.colToProp(col);

          // Condition 1: Column must be in allowedTagColumns
          const isAllowedColumn = allowedTagColumns.includes(prop as string);
          let className: any = "";
          // Then check if the cell actually has a tag applied
          if (
            Number.isInteger(row) &&
            row >= 0 &&
            Number.isInteger(col) &&
            col >= 0
          ) {
            const cellMeta = this.getCellMeta(row, col);
            className = cellMeta.className || "";
          }

          if (col === 1) {
            return className.includes("onholdGrp-cell");
          } else if (col === 2) {
            return className.includes("onhold-cell");
          } else {
            return className.includes("tagged-cell");
          }

          // // Condition 2: Row must not be on hold
          // const rowData = tableData?.[row];
          // const isRowOnHold = rowData?.["taggrouphold"] === "t";
          // const isStoreRowOnHold = rowData?.["tagStoreHold"] === "t";
          // return !isAllowedColumn || isRowOnHold || isStoreRowOnHold;
        },
      },
      readyToLaunchTag: {
        name: "Ready to Launch Tag",
        hidden(this: Handsontable) {
          const selection = this.getSelectedLast();
          if (!selection) return true; // hide if no selection
          const [, col] = selection;
          return col !== 2; // hide if not column 2
        },
        disabled(this: Handsontable) {
          const selection = this.getSelectedLast();
          if (!selection) return true;
          const [row, col] = selection;

          if (col !== 2) return true;

          const cellMeta = this.getCellMeta(row, col);
          const className = cellMeta.className || "";

          // disable if already tagged
          return className.includes("launch-ready-cell");
        },
        callback(this: Handsontable) {
          const selection = this.getSelectedLast();
          if (!selection) return;
          const [row, col] = selection;

          // Apply launch-ready class
          this.setCellMeta(row, col, "className", "launch-ready-cell");
          this.render();
        },
      },

      removeTags: {
        name: "Remove Tags",
        disabled(this: Handsontable) {
          const selection = this.getSelectedLast();
          if (!selection) return true;
          const [row, col] = selection;
          const prop = this.colToProp(col);

          // First check if it's in allowed columns
          if (!allowedTagColumns.includes(prop as string)) return true;

          // Then check if the cell actually has a tag applied
          const cellMeta = this.getCellMeta(row, col);
          const className = cellMeta.className || "";

          // Special case for group/store hold which apply to entire row
          if (col === 1) {
            return !className.includes("onholdGrp-cell");
          } else if (col === 2) {
            return !(
              className.includes("onhold-cell") ||
              className.includes("launch-ready-cell")
            );
          } else {
            return !className.includes("tagged-cell");
          }
        },
      },
    },
  };
  const buttonClickReset = () => {
    const hot = hotRef.current?.hotInstance;
    if (!hot) return;

    const filtersPlugin = hot.getPlugin("filters");
    filtersPlugin.clearConditions(); // clears all filter conditions
    filtersPlugin.filter(); // reapply filter (clears them effectively)
  };

  const buttonClickCallback = (type: number) => {
    const hot = hotRef.current?.hotInstance;
    if (!hot) return;

    const data = hot.getData();
    const rawHeaders = hot.getColHeader();
    const headers = rawHeaders.map((header) =>
      String(header)
        .replace(/<[^>]*>?/gm, " ")
        .trim()
    );

    const cleanedData = data.map((row) =>
      row.map((cell: string) => {
        if (cell === "-- Select --") return "";
        if (/^\d{4}-\d{2}-\d{2}$/.test(cell)) {
          const date = new Date(cell);
          if (!isNaN(date.getTime())) {
            const mm = String(date.getMonth() + 1).padStart(2, "0");
            const dd = String(date.getDate()).padStart(2, "0");
            const yy = String(date.getFullYear()).slice(-2);
            return `${mm}/${dd}/${yy}`;
          }
        }
        return cell;
      })
    );

    const worksheetData1 = [["Launch Report"], headers, ...cleanedData];
    const ws1 = XLSX.utils.aoa_to_sheet(worksheetData1);

    ws1["!merges"] = [
      { s: { r: 0, c: 0 }, e: { r: 0, c: headers.length - 1 } },
    ];
    ws1["A1"].s = {
      font: { bold: true, sz: 14 },
      alignment: { horizontal: "center", vertical: "center" },
    };
    ws1["!cols"] = headers.map(() => ({ wch: 20 }));
    ws1["!rows"] = [{ hpt: 28 }, { hpt: 20 }, ...data.map(() => ({ hpt: 18 }))];

    headers.forEach((header, col) => {
      const cellAddress = XLSX.utils.encode_cell({ r: 1, c: col });
      ws1[cellAddress] = ws1[cellAddress] || { t: "s", v: header };
      ws1[cellAddress].s = {
        font: { bold: true },
        alignment: { horizontal: "center", vertical: "center" },
        fill: { patternType: "solid", fgColor: { rgb: "D9E1F2" } },
        border: {
          top: { style: "thin", color: { rgb: "000000" } },
          bottom: { style: "thin", color: { rgb: "000000" } },
          left: { style: "thin", color: { rgb: "000000" } },
          right: { style: "thin", color: { rgb: "000000" } },
        },
      };
    });

    for (let row = 0; row < data.length; row++) {
      for (let col = 0; col < headers.length; col++) {
        const meta = hot.getCellMeta(row, col);
        const meta1 = hot.getCellMeta(row, col + 1);
        let className = meta.className || "";
        //if (col === 0) className = meta1.className || "";

        const cellRef = XLSX.utils.encode_cell({ r: row + 2, c: col });
        if (!ws1[cellRef]) {
          ws1[cellRef] = { t: "s", v: data[row][col] ?? "" };
        }

        ws1[cellRef].s = {
          alignment: { vertical: "center", horizontal: "left" },
          fill: className?.includes("tagged-cell")
            ? { patternType: "solid", fgColor: { rgb: "FFFF00" } }
            : className?.includes("onholdGrp-cell")
            ? { patternType: "solid", fgColor: { rgb: "FAD7A0" } }
            : className?.includes("onhold-cell")
            ? { patternType: "solid", fgColor: { rgb: "FAD7A0" } }
            : className?.includes("launch-ready-cell")
            ? { patternType: "solid", fgColor: { rgb: "A3E4D7" } }
            : undefined,
        };

        if (meta.comment?.value && !meta.tag) {
          ws1[cellRef].c = [{ t: meta.comment.value, a: "User" }];
          ws1[cellRef].c.hidden = true;
        }
      }
    }

    // Sheet2 logic (only if type === 1)
    let ws2: XLSX.WorkSheet | null = null;
    if (type === 1) {
      const data2 = convertDataToRows();
      const headers1 = headerMapping.map((h) => h.label);

      const worksheetData2 = [
        ["Launch Report"],
        headers1,
        ...allTypeValue.map((row) =>
          headerMapping.map(({ key }) => {
            const value = row[key];
            const isDateField = Object.keys(dateFieldMapping).includes(key);

            if (isDateField && value && typeof value !== "number") {
              const date = new Date(value);
              if (!isNaN(date.getTime())) {
                const mm = String(date.getMonth() + 1).padStart(2, "0");
                const dd = String(date.getDate()).padStart(2, "0");
                const yy = String(date.getFullYear()).slice(-2);
                return `${mm}/${dd}/${yy}`;
              }
            } else if (value === "-- Select --") {
              return "";
            }
            return value ?? "";
          })
        ),
      ];

      ws2 = XLSX.utils.aoa_to_sheet(worksheetData2);

      // Merge title row
      ws2["!merges"] = [
        { s: { r: 0, c: 0 }, e: { r: 0, c: headers1.length - 1 } },
      ];
      ws2["!cols"] = headers1.map(() => ({ wch: 20 }));

      // Style header row
      headers1.forEach((_, colIndex) => {
        const cellRef = XLSX.utils.encode_cell({ r: 1, c: colIndex });
        if (ws2 && ws2[cellRef]) {
          ws2[cellRef].s = {
            font: { bold: true },
            fill: { fgColor: { rgb: "D9D9D9" } },
            alignment: { horizontal: "center", vertical: "center" },
            border: {
              top: { style: "thin", color: { rgb: "000000" } },
              bottom: { style: "thin", color: { rgb: "000000" } },
              left: { style: "thin", color: { rgb: "000000" } },
              right: { style: "thin", color: { rgb: "000000" } },
            },
          };
        }
      });

      // Apply row styling based on tagColumnMapping
      allTypeValue.forEach((rowObj, rowIndex) => {
        Object.entries(tagColumnMapping).forEach(([dataKey, tagKey]) => {
          const tagValue = rowObj[tagKey];
          const colIndex = headerMapping.findIndex(
            ({ key }) => key === dataKey
          );
          const excelRow = rowIndex + 2;

          if (colIndex === -1 || tagValue !== "t") return;

          const applyStyleToRow = (color: string) => {
            headerMapping.forEach((_, colIdx) => {
              const ref = XLSX.utils.encode_cell({ r: excelRow, c: colIdx });
              ws2![ref] = ws2![ref] || { t: "s", v: "" };
              ws2![ref].s = { fill: { fgColor: { rgb: color } } };
            });
          };

          if (tagKey === "taggrouphold") applyStyleToRow("FAD7A0");
          else if (tagKey === "tagStoreHold") applyStyleToRow("FAD7A0");
          else if (tagKey === "tagStoreReadyToLaunch")
            applyStyleToRow("A3E4D7");
          else {
            const ref = XLSX.utils.encode_cell({ r: excelRow, c: colIndex });
            ws2![ref] = ws2![ref] || { t: "s", v: "" };
            ws2![ref].s = { fill: { fgColor: { rgb: "FFFF00" } } };
          }
        });
      });

      // Add hidden comments
      allTypeValue.forEach((rowObj, rowIndex) => {
        headerMapping.forEach(({ key }, colIndex) => {
          const commentKey = `${key}Comments`;
          const tagKey = `${key}Tag`;
          const comment = rowObj[commentKey];
          const tag = rowObj[tagKey];

          if (comment?.trim() && !tag) {
            const ref = XLSX.utils.encode_cell({
              r: rowIndex + 2,
              c: colIndex,
            });
            ws2![ref] = ws2![ref] || { t: "s", v: "" };
            ws2![ref].c = [{ t: comment.trim(), a: "User" }];
            ws2![ref].c.hidden = true;
          }
        });
      });

      // Title styling
      if (ws2["A1"]) {
        ws2["A1"].s = {
          font: { bold: true, sz: 14 },
          alignment: { horizontal: "center" },
        };
      }

      ws2["!rows"] = [{ hpt: 30 }, { hpt: 20 }];
    }

    // ✅ Create final workbook
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws1, selectedKey);
    if (ws2) XLSX.utils.book_append_sheet(wb, ws2, "All Stores");

    const now = new Date();
    const pad = (n: number) => n.toString().padStart(2, "0");
    const ms = now.getMilliseconds().toString().padStart(3, "0");
    const timestamp = `${now.getFullYear()}-${pad(now.getMonth() + 1)}-${pad(
      now.getDate()
    )}_${pad(now.getHours())}-${pad(now.getMinutes())}-${pad(
      now.getSeconds()
    )}-${ms}`;
    const fileName = "Onboarding Tenant Status_" + timestamp + ".xlsx";

    if (type === 0) {
      XLSX.writeFile(wb, fileName);
    } else {
      const excelBuffer = XLSX.write(wb, { bookType: "xlsx", type: "array" });
      return new Blob([excelBuffer], {
        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      });
    }
  };

  const convertDataToRows = () => {
    return allTypeValue.map((rowObj) =>
      headerMapping.map(({ key }) => {
        // Convert snake_case to camelCase
        const camelKey = key.replace(/_([a-z])/g, (_, g) => g.toUpperCase());
        return rowObj[camelKey] ?? "";
      })
    );
  };

  const headerMapping = [
    { label: "OB Age", key: "obAge" },
    { label: "Group Name", key: "groupName" },
    { label: "Store Name", key: "storeName" },
    { label: "OB Coord", key: "onboardingCoordinator" },
    { label: "DMS", key: "dms" },
    { label: "Agreement Received", key: "agreementReceived" },
    { label: "Requested DMS Access", key: "requestedDmsAccess" },
    { label: "DMS Active Date", key: "dmsactiveDate" },
    { label: "Requested OB Meeting", key: "requestedObMeeting" },
    { label: "OB Meeting Completed", key: "obMeetingCompleted" },
    { label: "Op Codes Available", key: "opCodesAvailable" },
    { label: "Op Codes Categorized", key: "opCodesCategorized" },
    { label: "Grid Received", key: "laborPricingGridReceived" },
    { label: "Grid in BZO", key: "laborPricingGridEnteredInBzo" },
    { label: "Model Mapping", key: "modelMapping" },
    { label: "Matrix Received", key: "partsMatrixReceived" },
    { label: "Matrix in BZO", key: "partsMatrixEnteredInBzo" },
    { label: "Users Created", key: "usersCreated" },
    { label: "Smoke Test Completed", key: "smokeTestCompleted" },
    { label: "Ready For Review", key: "readyForReview" },
    { label: "Review Completed", key: "reviewCompleted" },
    { label: "Coach", key: "coach" },
    { label: "Sales Person", key: "salesperson" },
    { label: "Scheduled Launch", key: "scheduledLaunch" },
    { label: "Launch Completed", key: "launchCompleted" },
    { label: "Launch Date", key: "launchDate" },
    { label: "Total Days To Launch", key: "totalDaysToLaunch" },
    { label: "Group Pay", key: "tagGroupPay" },
  ];

  const dateFieldMapping = {
    agreementReceived: "agreementReceived",
    requestedDmsAccess: "requestedDmsAccess",
    dmsActiveDate: "dmsActiveDate",
    obMeetingCompleted: "obMeetingCompleted",
    opCodesAvailable: "opCodesAvailable",
    opCodesCategorized: "opCodesCategorized",
    laborPricingGridReceived: "laborPricingGridReceived",
    laborPricingGridEnteredInBzo: "laborPricingGridEnteredInBzo",
    modelMapping: "modelMapping",
    partsMatrixReceived: "partsMatrixReceived",
    partsMatrixEnteredInBzo: "partsMatrixEnteredInBzo",
    usersCreated: "usersCreated",
    smokeTestCompleted: "smokeTestCompleted",
    readyForReview: "readyForReview",
    reviewCompleted: "reviewCompleted",
    scheduledLaunch: "scheduledLaunch",
    launchCompleted: "launchCompleted",
    launchDate: "launchDate",
    requestedObMeeting: "requestedObMeeting",
  };

  const hideLaunchColumnsForKeys = [
    "onboardingTotal",
    "needsObMeeting",
    "dmsNotActive",
    "opsNotCategorized",
    "gridNotEntered",
    "modelsNotMapped",
    "matrixNotEntered",
    "usersNotCreated",
    "launchNotScheduled",
  ];

  // Keys for hiding 'OB Age'
  const hideObAgeForKeys = ["launchCompleted"];
  const visibleColumns = columns
    .filter((col) => {
      if (
        hideLaunchColumnsForKeys.includes(selectedKey) &&
        col.data === "totalDaysToLaunch"
      ) {
        return false;
      }
      if (hideObAgeForKeys.includes(selectedKey) && col.data === "obAge") {
        return false;
      }
      return true;
    })
    .map((col) => {
      let baseRenderer;

      if (typeof col.renderer === "string") {
        baseRenderer = Handsontable.renderers.getRenderer(col.renderer);
      } else {
        baseRenderer = col.renderer || Handsontable.renderers.TextRenderer;
      }

      return {
        ...col,
        renderer: withTooltip(baseRenderer),
      };
    });

  const legends = [
    { color: "#FAD7A0 ", label: "On Hold" },
    { color: "#A3E4D7", label: "Launch Ready" },
    { color: "#FFFF00", label: "Action / Follow Up" },
  ];
  return (
    <>
      <div
        style={{
          display: "flex",
          alignItems: "center",
          marginBottom: "0px",
          width: "100%",
        }}>
        {/* Legends on the left */}
        <div style={{ display: "flex", gap: "10px", flexGrow: 1 }}>
          {legends.map((item) => (
            <div
              key={item.label}
              style={{ display: "flex", alignItems: "center" }}>
              <div
                style={{
                  width: 12,
                  height: 12,
                  backgroundColor: item.color,
                  marginRight: 5,
                }}></div>
              <span>{item.label}</span>
            </div>
          ))}
        </div>

        {/* Export Button on the far right */}
        {tableData.length > 0 && !showNoData && (
          <>
            <Tooltip title="Send Mail">
              <IconButton
                onClick={buttonClickSendMail}
                color="primary"
                style={{
                  marginLeft: "auto",
                }}>
                <MailIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="Export to Excel">
              <IconButton
                onClick={() => buttonClickCallback(0)}
                color="primary"
                style={{
                  marginLeft: "auto",
                }}>
                <FileDownloadOutlinedIcon />
              </IconButton>
            </Tooltip>
          </>
        )}
        <Tooltip title="Reset Filters">
          <IconButton
            onClick={() => buttonClickReset()}
            color="primary"
            style={{
              marginLeft: "auto",
            }}>
            <RefreshIcon />
          </IconButton>
        </Tooltip>
      </div>

      <>
        <div
          id="hot"
          style={{
            height: `${tableHeight}px`, // fixed height
            width: "99%",
            maxHeight: `${tableHeight}px`,
            overflow: "auto", // enables both vertical + horizontal scroll
          }}>
          <HotTable
            ref={hotRef}
            rowHeaders={(rowIndex) => ""} // Suppress default numbers
            afterGetRowHeader={(row, TH) => {
              if (TH.firstChild) {
                TH.removeChild(TH.firstChild); // Remove default content
              }
              const icon = document.createElement("span");
              icon.innerHTML = "&#10148;";
              icon.style.display = "inline-block";
              icon.style.fontSize = "10px";
              TH.appendChild(icon);
            }}
            afterRender={() => {
              console.log("Handsontable finished rendering");
            }}
            rowHeaderWidth={25}
            columnSorting={true}
            data={tableData}
            columns={visibleColumns}
            colWidths={100}
            colHeaders={true}
            rowHeights={22}
            stretchH="none" // ensures horizontal scroll works (no auto stretch)
            width="100%"
            fixedRowsTop={0}
            fixedColumnsLeft={3}
            autoColumnSize={false} // prevents auto resize so scroll works
            autoWrapRow={true}
            wordWrap={false}
            filters={true}
            dropdownMenu={{
              items: {
                filter_by_value: { name: "Filter by value" },
                filter_action_bar: { name: "Action bar" },
              },
            }}
            contextMenu={contextMenu}
            comments={true}
            cell={mergedCells}
            afterInit={() => {
              isInitializing.current = false;
            }}
            afterSetCellMeta={(row, col, key, value) => {
              if (
                key === "comment" &&
                !isInitializing.current &&
                !isProgrammatic.current &&
                value?.value?.trim() !== undefined
              ) {
                handleTextFieldUpdate(row, col, key, value);
                hotRef.current?.hotInstance?.render(); // Force refresh after comment update
              }
            }}
            afterChange={(changes, source) => {
              changes?.forEach(([row, prop, oldValue, newValue]) => {
                if (
                  typeof prop === "string" &&
                  [
                    "tagGroupPay",
                    "onboardingCoordinator",
                    "salesperson",
                    "dms",
                    "coach",
                  ].includes(prop)
                ) {
                  handleSaveDropdown(row, prop as string, newValue, oldValue);
                } else {
                  handleSaveDatePicker(row, prop as string, newValue, oldValue);
                }
              });
            }}
            // Prevent manual typing in date cells
            beforeKeyDown={(event) => {
              const hot = hotRef.current?.hotInstance;
              const selected = hot?.getSelectedLast();
              if (!selected) return;

              // ✅ Skip restriction if user is typing in the comment textarea
              if (
                (event.target as HTMLElement)?.classList.contains(
                  "htCommentTextArea"
                )
              ) {
                return;
              }

              const col = selected[1];
              const meta = hot?.getCellMeta(selected[0], col);

              if (meta?.type === "date") {
                // Block keyboard input except navigation keys
                if (
                  event.key.length === 1 || // any character
                  event.key === "Backspace" ||
                  event.key === "Delete"
                ) {
                  event.stopImmediatePropagation();
                  event.preventDefault();
                }
              }
            }}
            afterColumnSort={(currentSortConfig, destinationSortConfigs) => {
              const hot = hotRef.current?.hotInstance;
              if (!hot) return;
              if (destinationSortConfigs.length > 0) {
                destinationSortConfigs.forEach((config) => {
                  const columnIndex = config.column;
                  const order = config.sortOrder;
                  const header = hot.getColHeader(columnIndex);

                  console.log(
                    `Column "${header}" (index ${columnIndex}) is sorted: ${order}`
                  );
                });
              } else {
                console.log("No column is currently sorted.");
              }
            }}
            licenseKey="non-commercial-and-evaluation"
          />
        </div>

        {(showNoData || tableData.length === 0) && (
          <div
            style={{
              marginTop: 75,
              left: 0,
              right: 0,
              bottom: 0,
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              fontSize: "1.2rem",
              fontWeight: "bold",
              color: "#666",
            }}>
            No data available
          </div>
        )}
      </>

      <Dialog
        open={mailDialogOpen}
        onClose={handleClose}
        maxWidth="sm"
        fullWidth
        PaperProps={{ sx: { borderRadius: 2 } }}>
        <Tabs value={activeTab} onChange={handleTabChange} sx={{ px: 3 }}>
          <Tab label="Send Now" />
          <Tab label="Schedule" />
        </Tabs>

        <DialogContent sx={{ pt: 1 }}>
          <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
            <TextField
              label="Email"
              required
              fullWidth
              variant="standard"
              value={emailForm.email}
              onChange={(e) => handleEmailFormChange("email", e.target.value)}
              error={emailForm.emailError}
              helperText={
                emailForm.emailError
                  ? "Please enter valid email(s), separated by commas"
                  : ""
              }
              sx={{ mb: 1 }}
            />

            <TextField
              label="Message"
              multiline
              rows={4}
              fullWidth
              variant="outlined"
              placeholder="Maximum 256 letters"
              value={emailForm.message}
              onChange={(e) => handleEmailFormChange("message", e.target.value)}
              inputProps={{ maxLength: 256 }}
              sx={{ mt: 1 }}
            />
          </Box>
        </DialogContent>

        <DialogActions sx={{ px: 3, pb: 2, gap: 1 }}>
          {openSnackbar === true && (
            <span
              style={{
                color: "green",
                display: "block",
                textAlign: "left",
                fontWeight: "bold",
                marginTop: "8px",
              }}>
              {openSnackbarMessage}
            </span>
          )}

          <Button
            onClick={handleClose}
            sx={{
              backgroundColor: "#ff9800",
              color: "white",
              "&:hover": { backgroundColor: "#f57c00" },
            }}>
            Cancel
          </Button>
          {activeTab === 0 ? (
            <Button
              onClick={handleSendEmail}
              variant="contained"
              disabled={
                !emailForm.email.trim() || emailForm.emailError || isSending
              }
              sx={{
                backgroundColor: "#ff9800",
                "&:hover": { backgroundColor: "#f57c00" },
              }}>
              Send Now
            </Button>
          ) : (
            <Button
              onClick={handleScheduleEmail}
              variant="contained"
              disabled={
                !emailForm.email.trim() ||
                emailForm.emailError ||
                isScheduleSending
              }
              sx={{
                backgroundColor: "#ff9800",
                "&:hover": { backgroundColor: "#f57c00" },
              }}>
              Save
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </>
  );
};

export default ExampleComponent;
