import React from "react";
import { Box } from "@mui/material";
import { AgGridReact } from "ag-grid-react";
import ReportGridDefs from "./ReportGridDefs";
import { Constants } from "../utils/constants";

const LaunchReportGrid = () => {
  const { columnDefs, defaultColDef, onGridReady,launchReportList } = ReportGridDefs();

  return (
    <Box sx={{ paddingX: "10px", mt: "15px", width: "100%" }}>
      <div
        className={Constants.ag_grid_theme}
        style={{ height: "90vh", width: "84vw" }}
      >
        <AgGridReact
          columnDefs={columnDefs}
          editType="fullRow"
          rowData={launchReportList}
          defaultColDef={defaultColDef}
          rowSelection="single"
          onGridReady={(params: any) => onGridReady(params)} 
          singleClickEdit={true}
          suppressColumnVirtualisation={true}
          suppressChangeDetection={true}
          stopEditingWhenCellsLoseFocus={true}
        />
      </div>
    </Box>
  );
};

export default LaunchReportGrid;
