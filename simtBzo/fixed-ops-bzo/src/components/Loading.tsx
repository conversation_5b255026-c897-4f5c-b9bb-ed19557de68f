import React from "react";
import { Box } from "@mui/material";
import CircularProgress from "@mui/material/CircularProgress";

const Loading = (props?: any) => {
  const { size } = props;
  return (
    <Box
      sx={{
        display: "flex",
        width: "100%",
        height: size === "small" ? "10vh" : "88vh",
        justifyContent: "center",
        alignItems: "center",
      }}
    >
      <CircularProgress style={{ width: "50px", height: "50px" }} />
    </Box>
  );
};

export default Loading;
