import { useEffect } from "react";
import { useSnackbar } from "notistack";
import { IconButton } from "@mui/material";
import {
  Close,
  CheckCircleOutline,
  ErrorOutline,
  InfoOutlined,
} from "@mui/icons-material";

const MultipleSnackbars = ({ messages, setSnackbarMessages }: any) => {
  const { enqueueSnackbar, closeSnackbar } = useSnackbar();

  useEffect(() => {
    if (messages?.length) {
      messages.forEach((msg: any, index: number) => {
        let message = `${msg.grid_type}: ${msg.message}`;
        setTimeout(() => {
          enqueueSnackbar(
            <div
              style={{
                display: "flex",
                alignItems: "center",
                fontWeight: 500,
                fontFamily: "Roboto",
              }}
            >
              {getIcon(msg.code)}
              <span>{message}</span>
            </div>,
            {
              variant: "default",
              autoHideDuration: 3000,
              style: {
                backgroundColor: getSnackbarColor(msg.code),
              },
              action: (key) => (
                <IconButton onClick={() => closeSnackbar(key)} size="small">
                  <Close style={{ color: "white" }} />
                </IconButton>
              ),
            }
          );
        }, index * 1000);
      });

      // Clear messages after the last snackbar is shown
      setTimeout(() => {
        setSnackbarMessages(null); // or setMessages([]) if preferred
      }, messages.length * 2000 + 3000); // Wait until last message disappears
    }
  }, [messages, enqueueSnackbar]);

  return null;
};

const getSnackbarColor = (code: any) => {
  switch (code) {
    case 1:
      return "#2e7d32"; // Green
    case 0:
      return "#d32f2f"; // Orange
    default:
      return "#0288d1"; // Blue (for info)
  }
};
const getIcon = (code: any) => {
  switch (code) {
    case 1:
      return <CheckCircleOutline style={{ color: "white", marginRight: 8 }} />;
    case 0:
      return <ErrorOutline style={{ color: "white", marginRight: 8 }} />;
    default:
      return <InfoOutlined style={{ color: "white", marginRight: 8 }} />;
  }
};

export default MultipleSnackbars;
