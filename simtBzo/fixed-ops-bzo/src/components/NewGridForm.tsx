import { Box, Button, InputAdornment, Typography } from "@mui/material";
import { makeStyles } from "@material-ui/core/styles";
import {
  FileField,
  FileInput,
  Form,
  FormDataConsumer,
  NumberInput,
  SaveButton,
  SelectArrayInput,
  SelectInput,
  Toolbar,
  minValue,
  required,
  useNotify,
  useRecordContext,
} from "react-admin";
import CancelIcon from "@mui/icons-material/Cancel";
import { useEffect, useState } from "react";
import { DatePicker, DatePickerProps } from "antd";
import { Constants } from "../utils/constants";
import dayjs from "dayjs";
import StoreQueries from "../service/DataFetchQueries/storeQueries";
import InsertOrUpdateGridType from "../stores/LaborGrid/InsertOrUpdateGridType";
import { traceSpan } from "../utils/OTTTracing";

const useStyles = makeStyles((theme: any) => ({
  gridForm: {
    width: "100%",
  },
  toolbarCenter: {
    backgroundColor: "white",
    justifyContent: "center",
  },
  toolbarLeft: {
    backgroundColor: "#eaf1f6",
    justifyContent: "left",
    padding: 0,
  },
}));

const NewGridForm = (props: any) => {
  const notify = useNotify();
  const {
    handleSubmitForm,
    setAddGrid,
    addGrid,
    component,
    installDateValue,
    setInstallDateValue,
    updateDate,
    setUpdateDate,
    action,
    enableSave,
    cancel,
    gridTypeChoice,
    getGridTypesList,
    hasDefaultType,
    setStatusMessage,
    setOpenSnackbar,
    setStatusMessageType,
    cancelGridAddition,
    submitLoader,
    setSubmitLoader,
  } = props;
  const record: any = useRecordContext();
  const { GetOpcodesListQuery } = StoreQueries;
  const [opcodeListChoice, setOpcodeListChoice] = useState<any>([]);
  const [invalidDateMsg, setInvalidDateMsg] = useState("");
  const classes = useStyles();
  const twoDecimalPlaces = (value: any) => {
    if (!/^\d+(\.\d{1,2})?$/.test(value)) {
      return "Value can contain only up to two decimal places";
    }
    return undefined;
  };
  const validateDoorRate = [
    required(),
    minValue(0.01, "Value should be greater than 0"),
    twoDecimalPlaces,
  ];
  const handleFileChange = (event: any) => {
    traceSpan(`click_uploadfile_button`, {
      event: `click_uploadfile_button`,
      pageUrl: window.location.pathname,
      timestamp: new Date().toISOString(),
      userId:localStorage.getItem('user') || ''
    });

    if (!event) {
      notify("Invalid file format. Please select a CSV file", {
        type: "error",
        autoHideDuration: 5000,
      });
    }
  };
  useEffect(() => {
    GetOpcodesListQuery(record.tenantId, record.storeId).then((res: any) => {
      const opcodeChoice: any[] = res.map((item: any) => ({
        id: item.opcode,
        name: item.opcode,
      }));
      setOpcodeListChoice(opcodeChoice);
    });
  }, []);

  const onDateChange: DatePickerProps["onChange"] = (date, dateString) => {
    traceSpan('select_storeinstalldate_datepicker', {
      event: 'select_storeinstalldate_datepicker',
      pageUrl: window.location.pathname,
      timestamp: new Date().toISOString(),
      userId:
      localStorage.getItem('user') || '',
      value:dateString,
      });

    setInstallDateValue(date);
    setUpdateDate(true);
    Number(dayjs(date).format("YYYY")) > 2100 ||
    Number(dayjs(date).format("YYYY")) < 2000
      ? setInvalidDateMsg("Enter a valid date")
      : setInvalidDateMsg("");
  };

  const handleSubmit = (values: any) => {
    if (installDateValue && !invalidDateMsg) {
      handleSubmitForm(values);
    } else {
      setSubmitLoader(false);
    }
  };
  return (
    <Box width={"inherit"} height={"auto"} display={"flex"}>
      <Form
        onSubmit={(values: any) => {
          traceSpan(`click_${component === "laborGrid"?'newlaborgridsubmit':'newpartsgridsubmit'}_button`, {
            event: `click_${component === "laborGrid"?'newlaborgridsubmit':'newpartsgridsubmit'}_button`,
            pageUrl: window.location.pathname,
            timestamp: new Date().toISOString(),
            userId:localStorage.getItem('user') || ''
          });
          setSubmitLoader(true);
          setTimeout(() => {
            handleSubmit(values);
          }, 1000);
        }}
        className={classes.gridForm}
        defaultValues={{
          gridFor: null,
          opcode: null,
          gridType: null,
        }}
      >
        <Box>
          <Box
            display={"flex"}
            flexDirection={addGrid === "grid" ? "column" : "row"}
            alignItems={addGrid === "grid" ? "" : "center"}
            width={"100%"}
          >
            {/*------------------------- Grid Type input for Labor Grid--------------------- */}
            {component === "laborGrid" && (
              <div
                style={{
                  display: "flex",
                  alignItems: "flex-start",
                  flexDirection: addGrid === "grid" ? "column" : "row",
                }}
              >
                <SelectInput
                  sx={{
                    mr: 2,
                    width: addGrid === "grid" ? "100%" : 250,
                    mt: "18px",
                  }}
                  source="gridFor"
                  label="Grid Type"
                  fullWidth
                  choices={[
                    { id: "opcode", name: "Opcode" },
                    { id: "model", name: "Model" },
                  ]}
                  validate={required()}
                  helperText={addGrid === "grid" && false}
                  onFocus={() => {
                    traceSpan('gridtype_dropdown_focused', {
                      event: 'gridtype_dropdown_focused',
                      pageUrl: window.location.pathname,
                      timestamp: new Date().toISOString(),
                      userId: localStorage.getItem('user') || ''
                    });
                  }}
                  onChange={(event) => {
                    traceSpan('select_gridtype_dropdown', {
                      event: 'select_gridtype_dropdown',
                      pageUrl: window.location.pathname,
                      timestamp: new Date().toISOString(),
                      userId: localStorage.getItem('user') || '',
                      value: event.target.value
                    });
                  }}
                />
                <FormDataConsumer<{ gridFor: string }>>
                  {({ formData, ...rest }) =>
                    formData.gridFor &&
                    formData.gridFor === "opcode" && (
                      <SelectInput
                        helperText={false}
                        source="opcode"
                        label="Opcodes"
                        sx={{
                          width: addGrid === "grid" ? "100%" : 250,
                          mr: 2,
                          mt: "18px",
                        }}
                        fullWidth
                        choices={opcodeListChoice}
                        validate={required()}
                        onFocus={(e) => {
                          traceSpan('opcodes_dropdown_focused', {
                            event: 'opcodes_dropdown_focused',
                            pageUrl: window.location.pathname,
                            timestamp: new Date().toISOString(),
                            userId: localStorage.getItem('user') || ''
                          });
                      }}
                      onChange={(event) => {
                        traceSpan('select_opcodes_dropdown', {
                          event: 'select_opcodes_dropdown',
                          pageUrl: window.location.pathname,
                          timestamp: new Date().toISOString(),
                          userId: localStorage.getItem('user') || '',
                          value: event.target.value
                        });
                      }}
                      />
                    )
                  }
                </FormDataConsumer>
                <FormDataConsumer<{ gridFor: string; opcode: string }>>
                  {({ formData, ...rest }) =>
                    formData.gridFor &&
                    formData.gridFor === "model" && (
                      <Box
                        sx={{
                          display: "flex",
                          width: addGrid === "grid" ? "100%" : 250,
                          mr: 2,
                          flexDirection: "row",
                          mt: "10px",
                        }}
                      >
                        <SelectArrayInput
                          helperText={false}
                          source="gridType"
                          label="Grid Name"
                          fullWidth
                          choices={gridTypeChoice}
                          validate={required()}
                          createLabel="Add a new category"
                        />
                        <InsertOrUpdateGridType
                          getGridTypesList={getGridTypesList}
                          hasDefaultType={hasDefaultType}
                          isGridModal={false}
                          isAdd={true}
                          setStatusMessage={setStatusMessage}
                          setOpenSnackbar={setOpenSnackbar}
                          setStatusMessageType={setStatusMessageType}
                        />
                      </Box>
                    )
                  }
                </FormDataConsumer>
                <FormDataConsumer<{ gridFor: string; opcode: string }>>
                  {({ formData, ...rest }) => (
                    <Box
                      sx={{
                        mr: 2,
                        width: addGrid === "grid" ? "100%" : 250,
                      }}
                    >
                      <Typography
                        sx={{
                          color:
                            (!installDateValue && updateDate) || invalidDateMsg
                              ? "#d32f2f"
                              : "grey",
                          fontSize: "12px",
                        }}
                      >
                        Store Install Date*
                      </Typography>
                      <DatePicker
                        size={"large"}
                        onChange={onDateChange}
                        onFocus={() => {
                                    traceSpan('storeinstalldate_datepicker_focused', {
                                    event: 'storeinstalldate_datepicker_focused',
                                    pageUrl: window.location.pathname,
                                    timestamp: new Date().toISOString(),
                                    userId:
                                    localStorage.getItem('user') || ''
                                     });
                                }}
                        format={"MM/DD/YYYY"}
                        value={installDateValue}
                        variant="filled"
                        style={{
                          width: "100%",
                          border: "none",
                          borderBottom:
                            (!installDateValue && updateDate) || invalidDateMsg
                              ? "1px solid #d32f2f"
                              : "1px solid #958a8a",
                          borderRadius: 0,
                          padding: "11px",
                          marginBottom:
                            (addGrid === "fixed_rate" ||
                              addGrid === "upload") &&
                            ((!installDateValue && updateDate) ||
                              invalidDateMsg)
                              ? "0px"
                              : addGrid === "fixed_rate" || addGrid === "upload"
                              ? "24px"
                              : "4px",
                        }}
                      />
                      {!installDateValue && updateDate && (
                        <p
                          style={{
                            fontSize: 12,
                            color: "#d32f2f",
                            marginLeft: "4px",
                            margin: "4px 16px",
                          }}
                        >
                          Required
                        </p>
                      )}
                      {invalidDateMsg && (
                        <p
                          style={{
                            fontSize: 12,
                            color: "#d32f2f",
                            marginLeft: "4px",
                            margin: addGrid === "upload" ? "0px" : "4px 16px",
                          }}
                        >
                          {invalidDateMsg}
                        </p>
                      )}
                    </Box>
                  )}
                </FormDataConsumer>
              </div>
            )}

            {/*------------------------ Fixed Rate input for Labor Grid ---------------------*/}
            {addGrid === "fixed_rate" && (
              <NumberInput
                sx={{
                  width: 250,
                  // width: "32%"
                  mt: "17px",
                }}
                source="fixedRate"
                validate={validateDoorRate}
                label="Door Rate"
                fullWidth
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">$</InputAdornment>
                  ),
                }}
              />
            )}
          </Box>
          <Box
            sx={{
              display: "flex",
              width: 520,
              // : "100%",
            }}
          >
            {/*------------------------File Upload input---------------------*/}
            {addGrid === "upload" && (
              <FileInput
                validate={[required()]}
                source="csvFile"
                label={"Upload csv file"}
                accept=".csv, text/csv" // Restrict file selection to CSV files
                onChange={handleFileChange}
                helperText={
                  component === "laborGrid" &&
                  "File should contain 11 columns and minimum 17 rows of data. Data should be integer values only."
                }
              >
                <FileField source="src" title="title" />
              </FileInput>
            )}
          </Box>
        </Box>
        <Toolbar
          className={
            addGrid === "grid" ? classes.toolbarCenter : classes.toolbarLeft
          }
        >
          {action === Constants.actions.edit && component === "partsMatrix" ? (
            <>
              <SaveButton label="UPDATE" alwaysEnable />

              <Button
                disabled={enableSave ? false : true}
                onClick={() => {
                  traceSpan(`click_newpartsgridcancel_button`, {
                    event: `click_newpartsgridcancel_button`,
                    pageUrl: window.location.pathname,
                    timestamp: new Date().toISOString(),
                    userId:localStorage.getItem('user') || ''
                  });
                  cancel();
                }}
                color="primary"
                variant="contained"
                startIcon={<CancelIcon />}
                sx={{ m: 2 }}
              >
                CANCEL
              </Button>
            </>
          ) : (
            <>
              <SaveButton
                label="Submit"
                disabled={submitLoader} // Disable the button when submitLoader is true
                onClick={() => setUpdateDate(true)}
              />
              <Button
                color="primary"
                variant="contained"
                startIcon={<CancelIcon />}
                sx={{ m: 2 }}
                onClick={cancelGridAddition}
              >
                CANCEL
              </Button>
            </>
          )}
        </Toolbar>
      </Form>
    </Box>
  );
};

export default NewGridForm;
