import { Alert, Snackbar } from "@mui/material";
import React from "react";
import Slide, { SlideProps } from "@mui/material/Slide";
import { NotificationType } from "../types";

interface NotificationSnackbarProps {
  message: string;
  onClose?: () => void;
  open: boolean;
  type?: NotificationType;
}

const SnackBarMessage: React.FC<NotificationSnackbarProps> = ({
  message,
  onClose,
  open,
  type = "success",
}) => {
  return (
    <Snackbar
      open={open}
      anchorOrigin={{
        vertical: "bottom",
        horizontal: "right",
      }}
      autoHideDuration={5000}
      onClose={onClose}
      //   TransitionComponent={(props: SlideProps)=> <Slide {...props} direction="up" />}
    >
      <Alert
        onClose={onClose}
        severity={type} //success, info, warning, error
        variant="filled"
        sx={{ width: "100%" }}
      >
        {message}
      </Alert>
    </Snackbar>
  );
};
export default SnackBarMessage;
