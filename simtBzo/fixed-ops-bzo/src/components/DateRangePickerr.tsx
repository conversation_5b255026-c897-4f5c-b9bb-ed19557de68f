import { useState } from "react";
import { DatePicker, Button, Space } from "antd";
import dayjs, { Dayjs } from "dayjs";

const { RangePicker } = DatePicker;

interface DateRangePickerWithApplyProps {
  onApply: (range: [Dayjs, Dayjs]) => void;
}

export default function DateRangePickerWithApply({
  onApply,
}: DateRangePickerWithApplyProps) {
  const [tempRange, setTempRange] = useState<[Dayjs, Dayjs]>([
    dayjs().subtract(7, "day"),
    dayjs(),
  ]);

  const handleApply = () => {
    onApply(tempRange); // send selected range back to parent
  };

  return (
    <Space>
      <RangePicker
        value={tempRange}
        onChange={(values) => {
          if (values) setTempRange(values as [Dayjs, Dayjs]);
        }}
        allowClear={false}
      />
      <Button type="primary" onClick={handleApply}>
        Apply
      </Button>
    </Space>
  );
}
