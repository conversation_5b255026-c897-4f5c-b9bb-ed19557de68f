import {
  Box,
  Button,
  CircularProgress,
  FormControl,
  FormLabel,
  IconButton,
  InputLabel,
  MenuItem,
  Modal,
  Select,
  Tooltip,
  Typography,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import DeleteIcon from "@mui/icons-material/Delete";
import { Confirm } from "react-admin";
import { traceSpan } from "../utils/OTTTracing";

const DeleteButton: React.FC<any> = (props) => {
  const {
    gridTypeData,
    removeRow,
    handleDelete,
    getLaborGridList,
    record,
    reassignGridType,
    openDelete,
    setReassignGridType,
    newDefaultGridType,
    gridTypeChoice,
    setNewDefaultGridType,
    gridTypeList,
    setExpandedAccordionIndex,
  } = props;

  const [loading, setLoading] = useState(false);
  const [gridTypes, setGridTypes] = useState<any>();
  const [open, setOpen] = useState(false);
  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    traceSpan(`click_laborgriddelete_button`, {
      event: `click_laborgriddelete_button`,
      pageUrl: window.location.pathname,
      timestamp: new Date().toISOString(),
      userId:localStorage.getItem('user') || ''
    });
    event.preventDefault();
    event.stopPropagation();
    if (!removeRow) {
      getLaborGridList({
        realm: record.realmName,
        storeId: record.storeId,
        callType: "Grid",
        gridType: gridTypeData.gridType,
        gridFor: gridTypeData.gridFor,
        createdDate: gridTypeData.createdDate,
        tenantId: record.tenantId
      });
    }
    setOpen(true);
  };

  useEffect(() => {
    let gridType = gridTypeChoice?.filter(
      (item: any) => !item.isDefaultGridType && item.count != 0
    );
    setGridTypes(gridType);
  }, [gridTypeChoice, gridTypeList]);

  useEffect(() => {
    if (openDelete === false) {
      setReassignGridType(false);
      setNewDefaultGridType("");
    }
    setOpen(openDelete);
  }, [openDelete]);

  const handleDialogClose = (
    event: React.MouseEvent<HTMLElement>,
    reason?: string
  ) => {
    traceSpan(`click_laborgriddeletecancel_button`, {
      event: `click_laborgriddeletecancel_button`,
      pageUrl: window.location.pathname,
      timestamp: new Date().toISOString(),
      userId:localStorage.getItem('user') || ''
    });
    event.preventDefault();
    event.stopPropagation();
    if (reason === "backdropClick") return;
    setOpen(false);
    setReassignGridType(false);
    setNewDefaultGridType("");
  };

  const handleConfirm = async (event: React.MouseEvent<HTMLElement>) => {
    traceSpan(`click_laborgriddeleteconfirm_button`, {
      event: `click_laborgriddeleteconfirm_button`,
      pageUrl: window.location.pathname,
      timestamp: new Date().toISOString(),
      userId:localStorage.getItem('user') || ''
    });
    event.preventDefault();
    event.stopPropagation();
    let success;
    if (reassignGridType) {
      success =
        gridTypes.length > 0 && (await handleDelete(gridTypeData, "delete"));
        if (success) {
          setExpandedAccordionIndex(null);
          setReassignGridType(false);
          setOpen(false);
          setNewDefaultGridType("");
        }
    } else {
      success = await handleDelete(gridTypeData, "delete");
      if (success) {
        setExpandedAccordionIndex(null);
        setReassignGridType(false);
        setOpen(false);
        setNewDefaultGridType("");
      }
    }
  };
  return (
    <>
      <Tooltip title="Delete">
        <IconButton
          aria-label="delete"
          onClick={handleClick}
          sx={{ width: "fit-content", height: "fit-content" }}
        >
          {loading ? <CircularProgress size={24} /> : <DeleteIcon />}
        </IconButton>
      </Tooltip>
      <Confirm
        isOpen={open}
        // loading={loading}
        title={"Delete Labor Grid"}
        content={
          <>
            <Typography sx={{ fontSize: "14px" }}>
              Are you sure you want to delete this labor grid?
            </Typography>
            {reassignGridType && (
              <Box>
                {" "}
                <Typography sx={{ fontSize: "14px", mt: 1, color: "grey" }}>
                  Assign a default grid type other than{" "}
                  {`'${gridTypeData.gridType}'`} before deleting the last grid.
                </Typography>
                <FormControl
                  sx={{ margin: 0, mt: 1, width: "40%" }}
                  size="small"
                  variant="outlined"
                >
                  <InputLabel
                    id="demo-simple-select-small-outlined-label"
                    sx={{
                      mt: "4px",
                      fontSize: "14px",
                    }}
                  >
                    Grid Type
                  </InputLabel>
                  <Select
                    variant="outlined"
                    labelId="demo-simple-select-small-outlined-label"
                    id="demo-simple-select-small-outlined"
                    value={newDefaultGridType}
                    label="Age"
                    onChange={(e: any) => setNewDefaultGridType(e.target.value)}
                    sx={{
                      height: "30px",
                      marginTop: "9px",
                      fontSize: "12px",
                    }}
                  >
                    {gridTypes.map((item: any) => {
                      return (
                        !item.isDefaultGridType &&
                        item.count != 0 && (
                          <MenuItem sx={{ fontSize: "12px" }} value={item.name}>
                            {" "}
                            {item.name}{" "}
                          </MenuItem>
                        )
                      );
                    })}
                  </Select>
                </FormControl>
                {gridTypes.length == 0 && (
                  <Typography sx={{ fontSize: "12px", mt: 1, color: "red" }}>
                    Please add a labor grid to switch the default grid type.
                  </Typography>
                )}
              </Box>
            )}
          </>
        }
        onConfirm={handleConfirm}
        onClose={handleDialogClose}
      />
    </>
  );
};

export default DeleteButton;
