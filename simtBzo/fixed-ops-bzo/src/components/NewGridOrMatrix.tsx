import {
  Backdr<PERSON>,
  Box,
  Button,
  CircularProgress,
  FormControlLabel,
  Grid,
  Paper,
  Radio,
  RadioGroup,
  Typography,
} from "@mui/material";
import NewGridForm from "./NewGridForm";
import DeletePartsButton from "../stores/PartsMatrix/DeletePartsButton";
import Loading from "./Loading";
import NewMatrixForm from "../stores/PartsMatrix/NewMatrixForm";
import { traceSpan } from "../utils/OTTTracing";

const NewGridOrMatrix = (props: any) => {
  const {
    addGrid,
    handleRadioChange,
    incompleteError,
    invalidError,
    invalidLength,
    getGridWrapper,
    handleSubmitLaborGrid,
    handleSubmitPartsMatrix,
    setAddGrid,
    component,
    addEmptyRow,
    handleRemoveLastRow,
    initialRowData,
    matrixSourceList,
    matrixTypeList,
    fetchPartsMatrixRowData,
    listLoading,
    installDateValue,
    setInstallDateValue,
    updateDate,
    setUpdateDate,
    gridTypeChoice,
    getGridTypesList,
    hasDefaultType,
    defaultGridType,
    setStatusMessage,
    setOpenSnackbar,
    setStatusMessageType,
    setNoOfRows,
    noOfRows,
    cancelGridAddition,
    addOrRemoveGridRow,
    submitLoader,
    setSubmitLoader,
  } = props;
  return (
    <Paper
      sx={{
        backgroundColor: "#eaf1f6",
        border: "solid 2px",
        borderColor: "#003d6b",
        margin: "16px",
        fontSize: 14,
        color: "#003d6b",
        padding: "15px",
      }}
    >     

      <Typography sx={{ fontWeight: "bold" }}>
        {component === "laborGrid"
          ? "Add new labor grid"
          : "Add new parts matrix"}
      </Typography>
      <RadioGroup
        sx={{ fontWeight: "bold" }}
        row
        aria-labelledby="demo-row-radio-buttons-group-label"
        name="row-radio-buttons-group"
        value={addGrid}
        onChange={handleRadioChange}
      >
        <FormControlLabel
          value={component === "laborGrid" ? "grid" : "matrix"}
          control={
            <Radio
              size="small"
              sx={{
                color: "#003d6b",
                "&.Mui-checked": {
                  color: "#003d6b",
                },
              }}
              onChange={(event) => {
                traceSpan(`click_${component === "laborGrid" ? "newgrid" : "newmatrix"}_radiobutton`, {
                  event: `click_${component === "laborGrid" ? "newgrid" : "newmatrix"}_radiobutton`,
                  pageUrl: window.location.pathname,
                  timestamp: new Date().toISOString(),
                  userId: localStorage.getItem('user') || '',
                  value: event.target.value, 
                });
              }}
            />
          }
          label={component === "laborGrid" ? "New Grid" : "New Matrix"}
        />
        <FormControlLabel
          value="upload"
          control={
            <Radio
              size="small"
              sx={{
                color: "#003d6b",
                "&.Mui-checked": {
                  color: "#003d6b",
                },
              }}
              onChange={() => {
                traceSpan(`click_${component === "laborGrid" ? "laborgriduploadfile" : "partsgriduploadfile"}_radiobutton`, {
                  event: `click_${component === "laborGrid" ? "laborgriduploadfile" : "partsgriduploadfile"}_radiobutton`,
                  pageUrl: window.location.pathname,
                  timestamp: new Date().toISOString(),
                  userId: localStorage.getItem('user') || ''
                });
              }}
            />
          }
          label="Upload File"
        />
        {component === "laborGrid" && (
          <FormControlLabel
            value="fixed_rate"
            control={
              <Radio
                size="small"
                sx={{
                  color: "#003d6b",
                  "&.Mui-checked": {
                    color: "#003d6b",
                  },
                }}
                onChange={() => {
                traceSpan(`click_doorrate_radiobutton`, {
                  event: `click_doorrate_radiobutton`,
                  pageUrl: window.location.pathname,
                  timestamp: new Date().toISOString(),
                  userId: localStorage.getItem('user') || ''
                });
              }}
              />
            }
            label="Door Rate"
          />
        )}
      </RadioGroup>
      {component === "laborGrid" && addGrid === "grid" ? (
        <Paper
          sx={{ margin: "15px", padding: "15px", width: "fit-content" }}
          elevation={3}
        >
          <Grid container>
            <Grid item>
              <Typography
                fontSize={"small"}
                alignContent={"left"}
                marginBottom={"5px"}
                color={"red"}
              >
                {incompleteError && " * Please completely fill in the grid. "}
                {invalidError && "* Enter numeric values only."}
                {invalidLength &&
                  "* Value should contain only five digits with up to two decimal places, e.g. 12345.67"}
              </Typography>

              {getGridWrapper("add")}
              <Box display="flex" justifyContent="flex-end">
                <Button
                  sx={{
                    textTransform: "none",
                    fontSize: "12px",
                  }}
                  onClick={() => {
                    setNoOfRows((prev: number) => {
                      const updatedRows = prev + 1;
                      addOrRemoveGridRow("add", updatedRows);
                      return updatedRows;
                    });
                  }}
                >
                  + Add new row
                </Button>
                <Button
                  sx={{
                    textTransform: "none",
                    fontSize: "12px",
                  }}
                  disabled={noOfRows <= 16}
                  onClick={() => {
                    setNoOfRows((prev: number) => {
                      const updatedRows = prev - 1;
                      addOrRemoveGridRow("remove", updatedRows);
                      return updatedRows;
                    });
                  }}
                >
                  - Remove last row
                </Button>
              </Box>
            </Grid>
            <Grid
              item
              display={"flex"}
              justifyContent={"center"}
              alignItems={"center"}
              flexDirection={"column"}
              sx={{ width: "350px", m: "14px" }}
            >
              <NewGridForm
                cancelGridAddition={cancelGridAddition}
                handleSubmitForm={(values: any) =>
                  handleSubmitLaborGrid(values, "insert")
                }
                gridTypeChoice={gridTypeChoice}
                getGridTypesList={getGridTypesList}
                defaultGridType={defaultGridType}
                hasDefaultType={hasDefaultType}
                setAddGrid={setAddGrid}
                addGrid={addGrid}
                component={component}
                installDateValue={installDateValue}
                setInstallDateValue={setInstallDateValue}
                updateDate={updateDate}
                setUpdateDate={setUpdateDate}
                setStatusMessage={setStatusMessage}
                setOpenSnackbar={setOpenSnackbar}
                setStatusMessageType={setStatusMessageType}
                submitLoader={submitLoader}
                setSubmitLoader={setSubmitLoader}
              />
            </Grid>
          </Grid>
        </Paper>
      ) : component === "partsMatrix" && addGrid === "matrix" ? (
        listLoading ? (
          <Loading size="small" />
        ) : (
          <Paper sx={{ margin: "15px", padding: "15px" }} elevation={3}>
            {/* Parts matrix AG grid */}
            <Button
              style={{
                textTransform: "none",
                fontSize: "12px",
              }}
              onClick={() => {
                traceSpan(`click_addpartmatrixrow_button`, {
                  event: `click_addpartmatrixrow_button`,
                  pageUrl: window.location.pathname,
                  timestamp: new Date().toISOString(),
                  userId:localStorage.getItem('user') || ''
                });
                addEmptyRow();
              }}
            >
              Add row
            </Button>
            <DeletePartsButton
              removeLastRow={handleRemoveLastRow}
              RowData={initialRowData}
              removeRow={true}
              rowIndex=''
            />
            {incompleteError && (
              <Typography
                fontSize={"small"}
                alignContent={"left"}
                marginBottom={"5px"}
                color={"red"}
              >
                {incompleteError}
              </Typography>
            )}

            <Grid container>
              <Grid item xs={6}>
                {getGridWrapper("add")}
              </Grid>
              <Grid
                item
                xs={6}
                display={"flex"}
                justifyContent={"center"}
                alignItems={"center"}
              >
                <NewMatrixForm
                  handleSubmitPartsMatrix={handleSubmitPartsMatrix}
                  setAddGrid={setAddGrid}
                  component={component}
                  addGrid={addGrid}
                  matrixSourceList={matrixSourceList}
                  matrixTypeList={matrixTypeList}
                  fetchPartsMatrixRowData={fetchPartsMatrixRowData}
                  setInstallDateValue={setInstallDateValue}
                  installDateValue={installDateValue}
                  updateDate={updateDate}
                  setUpdateDate={setUpdateDate}
                  setStatusMessage={setStatusMessage}
                  setOpenSnackbar={setOpenSnackbar}
                  setStatusMessageType={setStatusMessageType}
                />
              </Grid>
            </Grid>
          </Paper>
        )
      ) : component === "laborGrid" &&
        (addGrid === "upload" || addGrid === "fixed_rate") ? (
        // File Upload or Fixed Rate for Labor Grid comes from here
        <NewGridForm
        cancelGridAddition={cancelGridAddition}
          handleSubmitForm={(values: any) =>
            handleSubmitLaborGrid(values, addGrid)
          }
          setAddGrid={setAddGrid}
          addGrid={addGrid}
          component={component}
          gridTypeChoice={gridTypeChoice}
          getGridTypesList={getGridTypesList}
          defaultGridType={defaultGridType}
          hasDefaultType={hasDefaultType}
          matrixSourceList={matrixSourceList}
          matrixTypeList={matrixTypeList}
          fetchPartsMatrixRowData={fetchPartsMatrixRowData}
          listLoading={listLoading}
          installDateValue={installDateValue}
          setInstallDateValue={setInstallDateValue}
          updateDate={updateDate}
          setUpdateDate={setUpdateDate}
          setStatusMessage={setStatusMessage}
          setOpenSnackbar={setOpenSnackbar}
          setStatusMessageType={setStatusMessageType}
          submitLoader={submitLoader}
          setSubmitLoader={setSubmitLoader}
        />
      ) : addGrid === "upload" && component === "partsMatrix" ? (
        // File Upload for Parts Matrix comes from here
        <NewMatrixForm
          handleSubmitPartsMatrix={handleSubmitPartsMatrix}
          setAddGrid={setAddGrid}
          addGrid={addGrid}
          component={component}
          matrixSourceList={matrixSourceList}
          matrixTypeList={matrixTypeList}
          fetchPartsMatrixRowData={fetchPartsMatrixRowData}
          listLoading={listLoading}
          installDateValue={installDateValue}
          setInstallDateValue={setInstallDateValue}
          updateDate={updateDate}
          setUpdateDate={setUpdateDate}
          setStatusMessage={setStatusMessage}
          setOpenSnackbar={setOpenSnackbar}
          setStatusMessageType={setStatusMessageType}
        />
      ) : null}
    </Paper>
  );
};

export default NewGridOrMatrix;
