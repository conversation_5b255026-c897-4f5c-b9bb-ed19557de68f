import { <PERSON>, Button } from "@mui/material";
import React, { useState, useRef } from "react";
import { AgGridReact } from "ag-grid-react";
import { Constants } from "../../utils/constants";
import DbSettingsGridDefs from "./DbSettingsGridDefs";
import SnackBarMessage from "../../components/SnackBarMessage";

import { NotificationType } from "../../types";
import { useNavigate } from "react-router-dom";
import { traceSpan } from "../../utils/OTTTracing";

const DbSettings = () => {
  const navigate = useNavigate();

  const [openSnackbar, setOpenSnackbar] = useState<boolean>(false);
  const [statusMessage, setStatusMessage] = useState<string>("");
  const [statusMessageType, setStatusMessageType] =
    useState<NotificationType>();

  // Grid API ref
  const gridApiRef = useRef<any>(null);

  const handleSaveSuccess = (
    type: NotificationType,
    message: string,
    status: boolean
  ) => {
    setStatusMessageType(type);
    setStatusMessage(message);
    setOpenSnackbar(status);
  };

  const onFilterChanged = (e: any) => {
       const filterValues = e.api.getFilterModel();
          Object.keys(filterValues).forEach((colId) => {
            traceSpan(`filter_grid_${colId}`, {
              event: `filter_grid_${colId}`,
          pageUrl: window.location.pathname,
          timestamp: new Date().toISOString(),
          userId: localStorage.getItem("user") || "",
          column: colId,
          filterValue: JSON.stringify(filterValues[colId])
            });
          });
    gridApiRef.current?.redrawRows();
  };

  const onSortChanged = (params: any) => {
    const sortModel = params.columnApi.getColumnState()
          .filter((col: any) => col.sort != null)
          .map((col: any) => ({
            colId: col.colId,
            sort: col.sort
          }));
      
        sortModel.forEach((sortItem: { colId: string; sort: 'asc' | 'desc' }) => {
          traceSpan(`sort_grid_${sortItem.colId}`, {
             event: `sort_grid_${sortItem.colId}`,
        pageUrl: window.location.pathname,
        timestamp: new Date().toISOString(),
        userId: localStorage.getItem('user') || '',
        column: sortItem.colId,
        direction: sortItem.sort
          });
        });
    gridApiRef.current?.redrawRows();
  };

  const {
    columnDefs,
    defaultColDef,
    onGridReady: defsOnGridReady,
    allDataList,
  } = DbSettingsGridDefs({ onSaveSuccess: handleSaveSuccess });

  const onGridReady = (params: any) => {
    gridApiRef.current = params.api;
    defsOnGridReady(params); // Call your defs logic too
  };

  return (
    <>
      <Box sx={{ paddingX: "10px", width: "100%", marginTop: "20px" }}>
        <Button variant="outlined" 
        onClick={() => {
          traceSpan(`click_adddbsettings_button`, {
              event: `click_adddbsettings_button`,
              pageUrl: window.location.pathname,
              timestamp: new Date().toISOString(),
              userId:localStorage.getItem('user') || ''
          });
          navigate("create")}}>
          Add Db Settings
        </Button>
      </Box>
      <Box sx={{ paddingX: "10px", width: "100%", marginTop: "20px" }}>
        <div
          className={Constants.ag_grid_theme}
          style={{ height: "90vh", width: "100%" }}>
          <AgGridReact
            columnDefs={columnDefs}
            editType="fullRow"
            rowData={allDataList}
            defaultColDef={defaultColDef}
            rowSelection="single"
            onGridReady={onGridReady}
            singleClickEdit={false}
            suppressClickEdit={true}
            suppressColumnVirtualisation={true}
            suppressChangeDetection={true}
            suppressCellSelection={true}
            suppressRowClickSelection={true}
            stopEditingWhenCellsLoseFocus={false}
            onFilterChanged={onFilterChanged}
            onSortChanged={onSortChanged}
          />
        </div>
      </Box>
      <SnackBarMessage
        onClose={() => setOpenSnackbar(false)}
        open={openSnackbar}
        message={statusMessage}
        type={statusMessageType}
      />
    </>
  );
};

export default DbSettings;
