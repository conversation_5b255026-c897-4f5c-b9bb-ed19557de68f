import React from "react";
import SettingsQueries from "../../service/Mutations/Settings";
import { ColDef } from "ag-grid-community";
import ActionCellRenderer from "../../components/Settings/DbSettings/ActionCellRenderer";
import { NotificationType } from "../../types";

// Custom cell renderer for checkbox (Centered)
const CheckboxRenderer = (props: any) => {
  return (
    <div
      style={{
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
      }}>
      <input
        type="checkbox"
        checked={props.value}
        disabled
        style={{ margin: "5px", padding: "0", width: "130px", height: "17px" }}
      />
    </div>
  );
};

// Custom cell editor for checkbox (Prevent immediate stop of row editing)
const CheckboxEditor = (props: any) => {
  const [checked, setChecked] = React.useState(props.value);

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setChecked(event.target.checked);
    // Don't stop editing immediately, allow the user to decide when to finish editing
    props.node.setDataValue(props.colDef.field, event.target.checked);
  };

  return (
    <div
      style={{
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
      }}>
      <input
        type="checkbox"
        checked={checked}
        onChange={handleChange}
        style={{ margin: "5px", padding: "0", width: "130px", height: "17px" }}
      />
    </div>
  );
};

const DbSettingsGridDefs = ({
  onSaveSuccess,
}: {
  onSaveSuccess: (
    type: NotificationType,
    message: string,
    status: boolean
  ) => void;
}) => {
  const { GetDbSettings } = SettingsQueries;
  const [allDataList, setAllDataList] = React.useState<any>([]);

  const getDbSettingsList = () => {
    GetDbSettings().then((res: any) => {
      setAllDataList(res);
    });
  };

  const onGridReady = (params: any) => {
    params?.api?.showLoadingOverlay();
    getDbSettingsList();
  };

  const handleSaveSuccess = (
    type: NotificationType,
    message: string,
    status: boolean
  ) => {
    console.log("Child reported save success!");
    onSaveSuccess(type, message, status);
    getDbSettingsList();
  };

  const columnDefs: ColDef[] = [
    {
      headerName: "Server Number",
      field: "serverNo",
      cellStyle: { textAlign: "left" },
      editable: false,
      flex: 1,
    },
    {
      headerName: "IP Address",
      field: "ipAddress",
      editable: true,
      flex: 2,
      cellStyle: { textAlign: "left" },
      cellClass: (params) => {
        return `element-ipAddress-${params.node.rowIndex}`;
      },
    },
    {
      headerName: "Db Name",
      field: "dbName",
      editable: true,
      flex: 2,
      cellClass: (params) => {
        return `element-dbName-${params.node.rowIndex}`;
      },
    },
    {
      headerName: "User Name",
      field: "dbUser",
      editable: true,
      flex: 2,
      cellClass: (params) => {
        return `element-dbUser-${params.node.rowIndex}`;
      },
    },
    {
      headerName: "Password",
      field: "dbPassword",
      editable: true,
      flex: 2,
      cellClass: (params) => {
        return `element-dbPassword-${params.node.rowIndex}`;
      },
    },
    {
      headerName: "Port",
      field: "dbPort",
      editable: true,
      flex: 1,
      cellClass: (params) => {
        return `element-dbPort-${params.node.rowIndex}`;
      },
    },
    {
      headerName: "IsFull",
      field: "isFull",
      flex: 1,
      editable: true, // Allow editing
      cellRendererFramework: CheckboxRenderer, // Use custom checkbox renderer
      cellEditorFramework: CheckboxEditor, // Use custom checkbox editor for editing
      cellStyle: { textAlign: "center" },
      cellClass: (params) => {
        return `element-isFull-${params.node.rowIndex}`;
      },
    },
    {
      headerName: "Action",
      flex: 1,
      filter: false,
      sortable: false,
      cellRenderer: ActionCellRenderer,
      cellRendererParams: {
        onSaveSuccess: handleSaveSuccess,
      },
      editable: false,
    },
  ];

  const defaultColDef = React.useMemo(() => {
    return {
      cellStyle: { textAlign: "left" },
      resizable: true,
      suppressMenu: true,
      unSortIcon: true,
      enableValue: true,
      sortable: true,
      floatingFilter: true,
      filter: true,
      wrapHeaderText: true,
      autoHeaderHeight: true,
      wrapText: true,
      autoHeight: true,
      flex: 3,
    };
  }, []);

  return {
    columnDefs,
    defaultColDef,
    onGridReady,
    allDataList,
  };
};

export default DbSettingsGridDefs;
