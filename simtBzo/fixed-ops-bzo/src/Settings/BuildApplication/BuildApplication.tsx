import React, { useEffect, useState } from "react";
import {
  Box,
  Button,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  Typography,
  Paper,
  CircularProgress,
} from "@mui/material";
import { Confirm } from "react-admin";
import BuildQuerries from "../../service/Mutations/BuildApp";
import ActivityLog from "../../ActivityLog/ActivityLog";
import SnackBarMessage from "../../components/SnackBarMessage";
import { traceSpan } from "../../utils/OTTTracing";

function BuildApplication() {
  const { GetBuildRun, GetApplicationActions, GetBuildRestartSimtEnv } =
    BuildQuerries;

  const [environment, setEnvironment] = useState<string>("");
  const [service, setService] = useState<string>("");
  const [action, setAction] = useState<string>("");
  const [confirmMsg, setConfirmMsg] = useState<boolean>(false);
  const [title, setTitle] = useState<string>("");
  const [content, setContent] = useState<string>("");
  const [appData, setAppData] = useState([]);
  const [Filereddata, setFilereddata] = useState([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [refreshKey, setRefreshKey] = useState(0);
  const [openSnackbar, setOpenSnackbar] = useState<boolean>(false);
  const [statusMessage, setStatusMessage] = useState<string>("");
  const [statusMessageType, setStatusMessageType] = useState<any>("");
  // Fetch dropdown data on component mount
  useEffect(() => {
    GetApplicationActions().then((res: any) => {
      const nodes = res;
      setAppData(nodes);
    });
  }, []);
  // useEffect(() => {
  //   // Initial load
  //   setLoading(true);
  //   const timer = setTimeout(() => {
  //     setLoading(false);
  //   }, 1000); // simulate 1 second loading

  //   // Setup 5 second interval to refresh
  //   const interval = setInterval(() => {
  //     setLoading(true);
  //     setRefreshKey((prev) => prev + 1);

  //     setTimeout(() => {
  //       setLoading(false);
  //     }, 1000); // simulate loading again
  //   }, 15000); // every 5 seconds

  //   return () => {
  //     clearInterval(interval);
  //     clearTimeout(timer);
  //   };
  // }, []);
  const getBuildFunction = () => {
    GetBuildRun()
      .then((res: any) => {
        setConfirmMsg(false);
        console.log("Response from API:", res);
      })
      .catch((err) => {
        console.error("Error in API call:", err);
      });
  };
  const getBuildRestartSimtEnvFun = () => {
    const input = {
      pAction: action,
      pService: service,
      pEnvironment: environment,
    };

    GetBuildRestartSimtEnv(input)
      .then((res: any) => {
        setConfirmMsg(false);
        setOpenSnackbar(true);
        setStatusMessage(res.string);
        setStatusMessageType("success");
        setEnvironment("");
        setService("");
        setAction("");
      })
      .catch((err) => {
        console.error("Error in API call:", err);
      })
      .finally(() => {
        setTimeout(() => {
          setLoading(false);
        }, 3000);
      });
  };

  const handleSubmit = () => {
    traceSpan(`click_buildapplicationrun_button`, {
      event: `click_buildapplicationrun_button`,
      pageUrl: window.location.pathname,
      timestamp: new Date().toISOString(),
      userId: localStorage.getItem("user") || "",
    });
    setConfirmMsg(true);
    setTitle("Confirm Build");
    setContent(
      `Are you sure you want to ${action} the ${service} in ${environment} ?`
    );
  };
  const handleConfirmBuild = () => {
    traceSpan(`click_confirm_button`, {
          event: `click_confirm_button`,
          pageUrl: window.location.pathname,
          timestamp: new Date().toISOString(),
          userId:localStorage.getItem('user') || ''
        });
    // getBuildFunction();
    getBuildRestartSimtEnvFun();
    setLoading(true);
  };
  const handleChangeEnv = (e: any) => {
    traceSpan(`select_environment_dropdown`, {
      event: `select_environment_dropdown`,
      pageUrl: window.location.pathname,
      timestamp: new Date().toISOString(),
      userId: localStorage.getItem("user") || "",
      value: e.target.value,
    });
    const value = e.target.value;
    setEnvironment(e.target.value);
    const filterData = appData.filter(
      (item: any) => item.environment === value
    );
    setFilereddata(filterData);
  };

  const uniqueEnvironments = appData
    .map((item: any) => item.environment)
    .filter((env, index, self) => self.indexOf(env) === index);
  const uniqueActions = Filereddata.map((item: any) => item.action).filter(
    (env, index, self) => self.indexOf(env) === index
  );
  const uniqueApplication = Filereddata.map(
    (item: any) => item.application
  ).filter((env, index, self) => self.indexOf(env) === index);
  return (
    <Box>
      <Paper
        elevation={3}
        sx={{
          padding: 4,
          mx: "auto",
          mt: 6,
          borderRadius: 3,
          maxWidth: 700,
          backgroundColor: "#fafafa",
        }}>
        <Typography variant="h5" fontWeight="bold" gutterBottom>
          Build Application
        </Typography>

        <Box sx={{ display: "flex", alignItems: "center", gap: 3, mb: 4 }}>
          <FormControl fullWidth sx={{ maxWidth: 250 }}>
            <InputLabel>Environment</InputLabel>
            <Select
              label="Environment"
              value={environment}
              onChange={(e) => handleChangeEnv(e)}>
              {uniqueEnvironments.map((env) => (
                <MenuItem key={env} value={env}>
                  {env}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <FormControl fullWidth sx={{ maxWidth: 250 }}>
            <InputLabel>Application</InputLabel>
            <Select
              label="Application"
              value={service}
              disabled={Filereddata.length == 0 ? true : false}
              onChange={(e) => {
                traceSpan(`click_application_dropdown`, {
                      event: `click_application_dropdown`,
                      pageUrl: window.location.pathname,
              timestamp: new Date().toISOString(),
              userId:localStorage.getItem('user') || '',
                      value: e.target.value
                    });
                setService(e.target.value as string)}}>
              {uniqueApplication.map((env) => (
                <MenuItem key={env} value={env}>
                  {env}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <FormControl fullWidth sx={{ maxWidth: 250 }}>
            <InputLabel>Action</InputLabel>
            <Select
              label="Action"
              value={action}
              disabled={Filereddata.length == 0 ? true : false}
              onChange={(e) => {
                traceSpan(`click_action_dropdowm`, {
                      event: `click_action_dropdown`,
                      pageUrl: window.location.pathname,
              timestamp: new Date().toISOString(),
              userId:localStorage.getItem('user') || '',
                      value: e.target.value,
                    });
                setAction(e.target.value as string)}}>
              {uniqueActions.map((env) => (
                <MenuItem key={env} value={env}>
                  {env}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          <Button
            color="primary"
            variant="contained"
            disabled={action === "" || service === ""}
            onClick={handleSubmit}
            sx={{
              height: "42px", // match MUI Select height
              textTransform: "none",
              paddingX: 4,
              fontWeight: "bold",
            }}>
            Run
          </Button>
        </Box>

        {/* <Box sx={{ display: "flex", justifyContent: "flex-end" }}>
          <Button
            color="primary"
            variant="contained"
            disabled={
              action.length === 0 && service.length === 0 ? true : false
            }
            // onClick={handleSubmit}
            sx={{
              textTransform: "none",
              paddingX: 4,
              paddingY: 1,
              fontWeight: "bold",
            }}>
            Run
          </Button>
        </Box> */}
        <Confirm
          isOpen={confirmMsg}
          title={title}
          content={content}
          onConfirm={handleConfirmBuild}
          onClose={() => setConfirmMsg(false)}
        />
      </Paper>
      <Box sx={{ display: "flex", justifyContent: "flex-end", pr: 4 }}>
        <Button
          color="primary"
          variant="contained"
          // onClick={handleSubmit}
          sx={{
            textTransform: "none",
            paddingX: 4,
            paddingY: 1,
            fontWeight: "bold",
          }}>
          Submit
        </Button>
      </Box>

      <Box px={4}>
        {/* <Typography variant="h6" gutterBottom>
          Activity Log
        </Typography> */}
        {/* <ActivityLog spec={"buildSpec"} /> */}
        {loading ? (
          <Box
            display="flex"
            justifyContent="center"
            alignItems="center"
            height={200}>
            <CircularProgress />
          </Box>
        ) : (
          <ActivityLog spec="buildSpec" />
        )}
      </Box>
      <SnackBarMessage
        onClose={() => setOpenSnackbar(false)}
        open={openSnackbar}
        message={statusMessage}
        type={statusMessageType}
      />
    </Box>
  );
}

export default BuildApplication;
