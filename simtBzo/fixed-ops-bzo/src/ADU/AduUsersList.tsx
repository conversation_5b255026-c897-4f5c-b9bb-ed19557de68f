import { Box, Button } from "@mui/material";
import React, { useEffect, useState } from "react";
import { AgGridReact } from "ag-grid-react";
import { Constants } from "../utils/constants";
import PersonAddIcon from "@mui/icons-material/PersonAdd";
import { useLocation, useNavigate } from "react-router-dom";
import SnackBarMessage from "../components/SnackBarMessage";
import { NotificationType } from "../types";
import AduUsersListGridDefs from "./AduUsersListGridDefs";
import { Confirm } from "react-admin";
import { deleteAduUser } from "../service/mutations";
import { traceSpan } from "../utils/OTTTracing";

const AduUsersList = () => {
  const {
    columnDefs,
    defaultColDef,
    onGridReady,
    allLoadList,
    openDeleteUser,
    setOpenDeleteUser,
    deletingUser,
    getAduUsersList,
  } = AduUsersListGridDefs();

  const navigate = useNavigate();
  const location = useLocation();
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [statusMessage, setStatusMessage] = useState("");
  const [statusMessageType, setStatusMessageType] =
    useState<NotificationType>("success");

  useEffect(() => {
    if (location.state?.status) {
      setStatusMessage(location.state?.status);
      setOpenSnackbar(true);
      setStatusMessageType("warning");
      window.history.replaceState({}, document.title);
    }
  }, [location.state]);

  const handleClose = () => {
    traceSpan(`click_deleteusercancel_button`, {
      event: `click_deleteusercancel_button`,
      pageUrl: window.location.pathname,
      timestamp: new Date().toISOString(),
      userId:localStorage.getItem('user') || ''
    });
    setOpenDeleteUser(false);
  };
  const addUser = () => {
    traceSpan(`click_adduser_button`, {
      event: `click_adduser_button`,
      pageUrl: window.location.pathname,
      timestamp: new Date().toISOString(),
      userId: localStorage.getItem("user") || "",
    });
    navigate("create");
  };
  const handleConfirm = () => {
    traceSpan(`click_deleteuserconfirm_button`, {
      event: `click_deleteuserconfirm_button`,
      pageUrl: window.location.pathname,
      timestamp: new Date().toISOString(),
      userId:localStorage.getItem('user') || ''
    });
    setDeleteLoading(true);
    deleteAduUser(deletingUser).then((res: any) => {
      if (res.status === "success") {
        getAduUsersList();
      } else {
        setStatusMessageType("error");
      }
      setDeleteLoading(false);
      setOpenDeleteUser(false);
      setOpenSnackbar(true);
      setStatusMessage(res.message);
    });
  };

  const onFilterChanged = (e: any) => {
    const filterValues = e.api.getFilterModel();
    Object.keys(filterValues).forEach((colId) => {
      traceSpan(`filter_grid_${colId}`, {
        event: `filter_grid_${colId}`,
        pageUrl: window.location.pathname,
        timestamp: new Date().toISOString(),
        userId: localStorage.getItem("user") || "",
        column: colId,
        filterValue: JSON.stringify(filterValues[colId]),
      });
    });
  };
  const onSortChanged = (params: any) => {
    // Get column states and filter only sorted columns
    const sortModel = params.columnApi
      .getColumnState()
      .filter((col: any) => col.sort != null)
      .map((col: any) => ({
        colId: col.colId,
        sort: col.sort,
      }));

    sortModel.forEach((sortItem: { colId: string; sort: "asc" | "desc" }) => {
      traceSpan(`sort_grid_${sortItem.colId}`, {
        event: `sort_grid_${sortItem.colId}`,
        pageUrl: window.location.pathname,
        timestamp: new Date().toISOString(),
        userId: localStorage.getItem("user") || "",
        column: sortItem.colId,
        direction: sortItem.sort,
      });
    });
  };

  return (
    <Box sx={{ paddingX: "10px", width: "100%", marginTop: "20px" }}>
      <Confirm
        isOpen={openDeleteUser}
        loading={deleteLoading}
        title="Delete ADU User"
        content={`The user will be removed from all realms. Do you want to continue?`}
        confirm="Continue"
        onConfirm={handleConfirm}
        onClose={handleClose}
      />
      <Box sx={{ width: "100%", display: "flex", justifyContent: "flex-end" }}>
        <Button
          variant="outlined"
          startIcon={<PersonAddIcon fontSize="small"></PersonAddIcon>}
          onClick={addUser}>
          Add User
        </Button>
      </Box>
      <Box sx={{ width: "100%", marginTop: "10px" }}>
        <div
          className={Constants.ag_grid_theme}
          style={{ height: "85vh", width: "100%" }}>
          <AgGridReact
            columnDefs={columnDefs}
            editType="fullRow"
            rowData={allLoadList}
            defaultColDef={defaultColDef}
            rowSelection="single"
            onGridReady={(params: any) => onGridReady(params)}
            singleClickEdit={true}
            suppressColumnVirtualisation={true}
            suppressChangeDetection={true}
            stopEditingWhenCellsLoseFocus={true}
            onFilterChanged={onFilterChanged}
            onSortChanged={onSortChanged}
          />
        </div>
      </Box>
      <SnackBarMessage
        onClose={() => setOpenSnackbar(false)}
        open={openSnackbar}
        message={statusMessage}
        type={statusMessageType}
      />
    </Box>
  );
};

export default AduUsersList;
