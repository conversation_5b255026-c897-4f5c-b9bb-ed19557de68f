import React from "react";
import DataFetchQueries from "../service/dataFetchQueries";
import { ColDef } from "ag-grid-community";
import moment from "moment";
import { Box, IconButton, Tooltip } from "@mui/material";
import { Delete } from "@mui/icons-material";
import { useTranslate } from "react-admin";
import { traceSpan } from "../utils/OTTTracing";

const AduUsersListGridDefs = () => {
  const { GetAduUsersList } = DataFetchQueries;
  const translate = useTranslate();
  const [allLoadList, setAllLoadList] = React.useState<any>([]);
  const [openDeleteUser, setOpenDeleteUser] = React.useState<boolean>(false);
  const [deletingUser, setDeletingUser] = React.useState<any>("");

  const getAduUsersList = () => {
    GetAduUsersList().then((res: any) => {
      setAllLoadList(res);
    });
  };

  const onGridReady = (params: any) => {
    params?.api?.showLoadingOverlay();
    getAduUsersList();
  };
  const SportRenderer = (props: any) => {
    return (
        <Box>
          <Tooltip title={translate("Delete User")}>
            <IconButton
              size="small"
              sx={{
                width: 9,
                height: 9,
              }}
              onClick={() => {
                traceSpan(`click_deleteuser_button`, {
                      event: `click_deleteuser_button`,
                      pageUrl: window.location.pathname,
                      timestamp: new Date().toISOString(),
                      userId:localStorage.getItem('user') || ''
                });
                console.log("props", props.data.userName);
                setDeletingUser(props.data.userName)
                setOpenDeleteUser(true)}}
            >
              <Delete />
            </IconButton>
          </Tooltip>
        </Box>
    );
  };

  const columnDefs: ColDef[] = [
    {
      headerName: "User Name",
      field: "userName",
      cellStyle: { textAlign: "left" },
      flex: 6,
    },
    {
      headerName: "First Name",
      field: "firstName",
      flex: 3,
      cellStyle: { textAlign: "left" },
    },
    {
      headerName: "Last Name",
      field: "lastname",
      flex: 3,
      cellStyle: { textAlign: "left" },
    },
    {
      headerName: "Created Date",
      field: "createdAt",
      flex: 3,
      valueFormatter: (params: any) =>
        params.value ? moment(params.value).format("MM/DD/YYYY") : params.value,
    },
    {
      headerName: "Role",
      field: "role",
      flex: 3,
      cellStyle: { textAlign: "left" },
    },
    {
      headerName: "Temporary Password",
      field: "passwords",
      flex: 3,
      cellStyle: { textAlign: "left" },
    },
    {
      suppressMenu: true,
      maxWidth: 60,
      cellRenderer: SportRenderer,
      suppressColumnsToolPanel: true,
      cellStyle: { border: "none",textAlign: "center" },
      suppressNavigable: true,
      sortable: false,
      filter: false,
      unSortIcon: false,
      pinned: "right",
      flex: 1,
    },
  ];

  const defaultColDef = React.useMemo(() => {
    return {
      cellStyle: { textAlign: "left" },
      resizable: true,
      suppressMenu: true,
      unSortIcon: true,
      enableValue: true,
      sortable: true,
      floatingFilter: true,
      filter: true,
      wrapHeaderText: true,
      autoHeaderHeight: true,
      wrapText: true,
      autoHeight: true,
      flex: 3,
    };
  }, []);
  return {
    columnDefs,
    defaultColDef,
    onGridReady,
    allLoadList,
    getAduUsersList,
    openDeleteUser,
    setOpenDeleteUser, deletingUser, setDeletingUser
  };
};

export default AduUsersListGridDefs;
