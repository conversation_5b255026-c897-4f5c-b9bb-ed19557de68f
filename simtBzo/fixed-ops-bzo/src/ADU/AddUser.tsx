import React, { useState } from "react";
import { useForm } from "react-hook-form";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Container,
  Typography,
  Box,
  Grid,
  MenuItem,
  CircularProgress,
  Alert,
} from "@mui/material";
import { addNewAduUser, checkAduUser } from "../service/mutations";
import { useNavigate } from "react-router-dom";
import SnackBarMessage from "../components/SnackBarMessage";
import { NotificationType } from "../types";
import { traceSpan } from "../utils/OTTTracing";

const MuiForm = () => {
  const {
    register,
    handleSubmit,
    setValue,
    clearErrors,
    formState: { errors },
  } = useForm();
  const navigate = useNavigate();
  const [loading, setLoading] = useState<boolean>(false);
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [statusMessage, setStatusMessage] = useState("");
  const [statusMessageType, setStatusMessageType] =
    useState<NotificationType>("error");

  const onSubmit = async (data: any) => {
    traceSpan(`click_addusersubmit_button`, {
          event: `click_addusersubmit_button`,
          pageUrl: window.location.pathname,
          timestamp: new Date().toISOString(),
          userId:localStorage.getItem('user') || ''
    });
    setLoading(true);
    await checkAduUser(data).then((res: any) => {
      if (res.status === "failed") {
        setOpenSnackbar(true);
        setStatusMessage(res.message);
        setStatusMessageType("error");
        setLoading(false);
      } else {
        addNewAduUser(data).then((response: any) => {});
        setLoading(false);
        navigate("/NeverEvents/aduUsers", {
          state: { status: "User creation is in progress" },
        });
      }
    });
  };

  return (
    <Container
      sx={{
        width: "100%",
        height: "70vh",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
      }}>
      <SnackBarMessage
        onClose={() => setOpenSnackbar(false)}
        open={openSnackbar}
        message={statusMessage}
        type={statusMessageType}
      />
      <Box
        sx={{
          border: "1px solid #d3d3d3",
          padding: 3,
          borderRadius: 1,
          boxShadow: 1,
          width: "50%",
          height: "fit-content",
          position: "relative",
          opacity: loading ? 0.7 : 1,
        }}>
        {loading && (
          <CircularProgress
            size={24}
            sx={{
              position: "absolute",
              top: "50%",
              left: "50%",
              marginTop: "-12px",
              marginLeft: "-12px",
            }}
          />
        )}
        <Typography
          variant="h5"
          gutterBottom
          fontWeight={"bold"}
          textAlign={"center"}>
          Add ADU User
        </Typography>
        <Alert
          sx={{ padding: "4px 8px", marginBottom: "8px" }}
          severity="warning">
          User creation may take a few minutes to process.
        </Alert>
        <form onSubmit={handleSubmit(onSubmit)}>
          <Grid container spacing={2} alignItems="center" marginBottom={2}>
            <Grid item xs={4}>
              <Typography fontSize={"14px"}>Email</Typography>
            </Grid>
            <Grid item xs={1}>
              <Typography textAlign={"end"} fontSize={"14px"}>
                :
              </Typography>
            </Grid>
            <Grid item xs={7}>
              <TextField
                fullWidth
                variant="outlined"
                {...register("userName", {
                  required: "Email is required",
                  pattern: {
                    value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                    message: "Enter a valid email address",
                  },
                })}
                error={!!errors.userName}
                helperText={
                  errors.userName?.message
                    ? String(errors.userName.message)
                    : ""
                }
                size="small"
                sx={{
                  "& .MuiInputBase-root": { height: 30 },
                  "& .MuiInputBase-input": { fontSize: "14px" },
                }}
                onBlur={(e) => {
                  traceSpan('input_email_text', {
                      event: 'input_email_text',
                      pageUrl: window.location.pathname,
                      timestamp: new Date().toISOString(),
                      userId: localStorage.getItem('user') || '',
                      value: e.target.value,
                  });
                 }}
              />
            </Grid>
          </Grid>
          <Grid container spacing={2} alignItems="center" marginBottom={2}>
            <Grid item xs={4}>
              <Typography fontSize={"14px"}>First Name</Typography>
            </Grid>
            <Grid item xs={1}>
              <Typography textAlign={"end"} fontSize={"14px"}>
                :
              </Typography>
            </Grid>
            <Grid item xs={7}>
              <TextField
                fullWidth
                variant="outlined"
                {...register("first", { required: "First name is required" })}
                error={!!errors.first}
                helperText={
                  errors.first?.message ? String(errors.first.message) : ""
                }
                sx={{
                  "& .MuiInputBase-root": { height: 30 },
                  "& .MuiInputBase-input": { fontSize: "14px" },
                }}
                onBlur={(e) => {
                    traceSpan('input_firstname_text', {
                        event: 'input_firstname_text',
                        pageUrl: window.location.pathname,
                        timestamp: new Date().toISOString(),
                        userId: localStorage.getItem('user') || '',
                        value: e.target.value,
                    });
                }}
              />
            </Grid>
          </Grid>
          <Grid container spacing={2} alignItems="center" marginBottom={2}>
            <Grid item xs={4}>
              <Typography fontSize={"14px"}>Last Name</Typography>
            </Grid>
            <Grid item xs={1}>
              <Typography textAlign={"end"} fontSize={"14px"}>
                :
              </Typography>
            </Grid>
            <Grid item xs={7}>
              <TextField
                fullWidth
                variant="outlined"
                {...register("last", { required: "Last name is required" })}
                error={!!errors.last}
                helperText={
                  errors.last?.message ? String(errors.last.message) : ""
                }
                sx={{
                  "& .MuiInputBase-root": { height: 30 },
                  "& .MuiInputBase-input": { fontSize: "14px" },
                }}
                onBlur={(e) => {
                    traceSpan('input_lastname_text', {
                        event: 'input_lastname_text',
                        pageUrl: window.location.pathname,
                        timestamp: new Date().toISOString(),
                        userId: localStorage.getItem('user') || '',
                        value: e.target.value,
                    });
                }}
              />
            </Grid>
          </Grid>
          <Grid container spacing={2} alignItems="center" marginBottom={2}>
            <Grid item xs={4}>
              <Typography fontSize={"14px"}>Role</Typography>
            </Grid>
            <Grid item xs={1}>
              <Typography textAlign={"end"} fontSize={"14px"}>
                :
              </Typography>
            </Grid>
            <Grid item xs={7}>
              <TextField
                fullWidth
                variant="outlined"
                select
                {...register("role", { required: "Role is required" })}
                error={!!errors.role}
                helperText={
                  errors.role?.message ? String(errors.role.message) : ""
                }
                onChange={(e) => {
                     traceSpan('role_dropdown_changed', {
                        event: 'role_dropdown_changed',
                        pageUrl: window.location.pathname,
                        timestamp: new Date().toISOString(),
                        userId:
                          localStorage.getItem('user') || '',
                            value:e.target.value  
                        });
                  setValue("role", e.target.value); // Force update role value
                  clearErrors("role"); // Clear error message immediately
                }}
                sx={{
                  "& .MuiInputBase-root": { height: 30 },
                  "& .MuiInputBase-input": { fontSize: "14px" },
                }}
                onFocus={(e) => {
                    traceSpan('role_dropdown_focused', {
                    event: 'role_dropdown_focused',
                    pageUrl: window.location.pathname,
                    timestamp: new Date().toISOString(),
                    userId: localStorage.getItem('user') || '',
                    value: e.target.value,
                    });
                }}
              >
                <MenuItem sx={{ fontSize: "12px" }} value="superadmin">
                  Superadmin
                </MenuItem>
                <MenuItem sx={{ fontSize: "12px" }} value="admin">
                  Admin
                </MenuItem>
                <MenuItem sx={{ fontSize: "12px" }} value="user">
                  User
                </MenuItem>
                <MenuItem sx={{ fontSize: "12px" }} value="client">
                  Client
                </MenuItem>
              </TextField>
            </Grid>
          </Grid>
          <Box sx={{ display: "flex", justifyContent: "space-between", mt: 2 }}>
            <Button
              variant="outlined"
              color="secondary"
              onClick={() => {
                traceSpan(`click_addusercancel_button`, {
                  event: `click_addusercancel_button`,
                  pageUrl: window.location.pathname,
                  timestamp: new Date().toISOString(),
                  userId:localStorage.getItem('user') || ''
                });
                navigate("/NeverEvents/aduUsers")}}
            >
              Cancel
            </Button>
            <Button type="submit" variant="contained" color="primary">
              Submit
            </Button>
          </Box>
        </form>
      </Box>
    </Container>
  );
};

export default MuiForm;
