export class PageRoutes {
  static root = "/";
  static statelessServiceBzoTenants = "/statelessServiceBzoTenants";
  static launchReports = "/LaunchReport";
  static neverEvents = "/NeverEvents";
  static changeLogs = "/changeLogs";
  static activityLog = "/ActivityLog";
  static dailyLoad = "/DailyLoad";
  static testResults = "/TestResults";
  static settings = "/settings";
  static allStores = "/statelessServiceBzoStoreDetails";
  static dmsBilling = "/DMSBilling";
  static telemetry = "/Telemetry";
  // static testResults = "/TestResults";
  static allTenantsDashboard = `${PageRoutes.statelessServiceBzoTenants}/dashboard/allTenants`;
  static onboarding = `${PageRoutes.statelessServiceBzoTenants}/dashboard/onboarding`;
  static nsQaValidation = `${PageRoutes.statelessServiceBzoTenants}/dashboard/nsQaValidation`;
  static review = `${PageRoutes.statelessServiceBzoTenants}/dashboard/review`;
  static readyToLaunch = `${PageRoutes.statelessServiceBzoTenants}/dashboard/readyToLaunch`;
  static launched = `${PageRoutes.statelessServiceBzoTenants}/dashboard/launched`;
  static cancelled = `${PageRoutes.statelessServiceBzoTenants}/dashboard/cancelled`;
  static testTenant = `${PageRoutes.statelessServiceBzoTenants}/dashboard/testTenant`;
  static allStoresDetails = `${PageRoutes.allStores}/AllDetails`;
  static storeList = `${PageRoutes.allStores}/storeList`;
  // Never Events
  static neverEventsEDI = `${PageRoutes.neverEvents}/EDI`;
  static neverEventsFOPC = `${PageRoutes.neverEvents}/FOPC`;
  static neverEventsMaintainance = `${PageRoutes.neverEvents}/maintainance`;
  static neverEventsUserRole = `${PageRoutes.neverEvents}/userRole`;
  static dailyDataAsOf = `${PageRoutes.neverEvents}/dailyDataAsOf`;
  static dailyLogins = `${PageRoutes.neverEvents}/dailyLogins`;
  static aduUsers = `${PageRoutes.neverEvents}/aduUsers`;
  static emailReport = `${PageRoutes.neverEvents}/EmailReport`;
  static ro13MonthReport = `${PageRoutes.neverEvents}/RO13MonthReport`;
  static missedReport = `${PageRoutes.neverEvents}/MissedReport`;

  // Test Results
  static regressionTest = `${PageRoutes.testResults}/regression`;
  static smokeTest = `${PageRoutes.testResults}/smoke`;
  // Settings
  static dbSettings = `${PageRoutes.settings}/dbSettings`;
  static buildApplication = `${PageRoutes.settings}/buildApplication`;
  static schemaConfig = `${PageRoutes.settings}/schemaConfiguration`;
  static fopcTelemetry = `${PageRoutes.telemetry}/fopcTelemetry`;
  static bzoTelemetry = `${PageRoutes.telemetry}/bzoTelemtry`;

  static statelessServiceBzoTenantsDynamicSegment = `${PageRoutes.statelessServiceBzoTenants}/*`;
  static statelessServiceBzoStoreDetails = "/statelessServiceBzoStoreDetails";
  static statelessServiceBzoStoreDetailsDynamicSegment = `${PageRoutes.statelessServiceBzoStoreDetails}/*`;
  static deals = "/deals";
  static getStoreDetailsRoute = (id: any) => {
    return `${PageRoutes.statelessServiceBzoStoreDetails}/${id}/show`;
  };
  static dmsMasters = "/dmsMasters";
  static createStoreMaster = "/statelessServiceBzoStoreDetails/create";
  static editTenantRoute = (id: any) => {
    return `${PageRoutes.statelessServiceBzoTenants}/${id}/edit`;
  };
  static tenantShowPageRoute = (id: any) => {
    return `${PageRoutes.statelessServiceBzoTenants}/${id}/show`;
  };
  static editStoreRoute = (id: any) => {
    return `${PageRoutes.statelessServiceBzoStoreDetails}/${id}/edit`;
  };

  static getRevenueSummaryRoute = (id: any) => {
    return `${PageRoutes.statelessServiceBzoStoreDetails}/${id}/show/RevenueSummaryDrilldown`;
  };
}
