.profileImage {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #512da8;
  font-size: 35px;
  color: #fff;
  text-align: center;
  line-height: 40px;
  margin: 20px 0;
}

.expandCollapse:hover {
  cursor: pointer;
}

.action-button {
  border: none;
  color: white;
  padding: 3px 12px;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  font-size: 16px;
  background-color: #203455;
  text-transform: none;
}

.action-button-disabled {
  background-color: grey;
}

.action-button.update {
  margin-right: 1px;
}

.ag-cell-green {
  color: green !important;
  font-weight: bold;
}

.action-button.cancel {
  margin-left: 1px;
}
.ag-cell-focus,
.ag-cell-no-focus {
  border: none !important;
}
/* This CSS is to not apply the border for the column having 'no-border' class */
.no-border.ag-cell:focus {
  border: none !important;
  outline: none;
}
.ag-header-cell-text {
  white-space: normal !important;
}
.ag-theme-alpine {
  --ag-font-size: 12px !important;
}
div.ag-theme-alpine div.ag-row {
  font-size: 12px !important;
}
.ag-theme-balham .ag-header-row {
  color: #003d6b !important;
}
/* .ag-theme-balham .ag-cell-value {
  color: black;
} */
.ag-header-cell input.ag-floating-filter-input {
  height: 4px !important; /* Set the desired height here */
}
.custom-header {
  background-color: rgb(171, 64, 0) !important; /* Header background color */
  color: white !important; /* Header text color */
  font-weight: bold !important;
}

.css-hip9hq-MuiPaper-root-MuiAppBar-root {
  /* background-color: #1976d2 !important; */
  /* color: #fff !important; */
}
.MuiFormHelperText-root {
  /* color: red !important; */
}
.MuiButton-root {
  font-size: 12px !important;
  font-family: "Roboto", "Montserrat", "Helvetica", "Arial", sans-serif;
}
/* Paytype page */
.input-container {
  position: relative;
}

.rag-red-outer {
  border: solid 1px !important;
  border-color: red !important;
}
input.datepicker {
  height: 35px;
  width: 180px;
  border: 1px solid #c0c0c0;
  border-radius: 4px;
  box-sizing: border-box;
  padding: 16px;
}
.fleet-grid input.datepicker {
  height: 35px;
  width: 100px;
  border: 1px solid #c0c0c0;
  border-radius: 4px;
  box-sizing: border-box;
  padding: 16px;
}
.labelpicker {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 16px;
  display: flex;
  align-items: center;
  pointer-events: none;
}
.fleet-picker {
  font-weight: lighter !important;
}
input.datepicker,
.labelpicker .textpicker {
  font-family: "Roboto", "Montserrat", "Helvetica", "Arial", sans-serif;
  font-size: 12px;
  font-weight: bold !important;
}
.fleet-grid input.datepicker,
.labelpicker .textpicker {
  font-family: "Roboto", "Montserrat", "Helvetica", "Arial", sans-serif;
  font-size: 12px;
  /* font-weight: 400 !important; */
}
.labelpicker .textpicker {
  transition: all 0.15s ease-out;
  color: #212121;
  font-family: "Roboto", "Montserrat", "Helvetica", "Arial", sans-serif;
  font-weight: 400;
}

input.datepicker:focus {
  outline: none;
  border: 2px solid rgb(0, 61, 107);
}
.daterangepicker .drp-calendar.left div {
  padding: 8px 0 165px 8px !important;
}
.daterangepicker .drp-calendar.single .calendar-table {
  height: 210px !important;
}
.daterangepicker .ranges ul {
  width: auto !important;
  font-weight: 700;
  font-size: 12px;
  font-family: "Roboto", "Montserrat", "Helvetica", "Arial", sans-serif;
}
input.datepicker:focus + .labelpicker .textpicker,
:not(input.datepicker[value=""]) + .labelpicker .textpicker {
  font-size: 11px;
  transform: translate(0, -150%);
  background-color: white;
  padding-left: 4px;
  padding-right: 4px;
  /* font-weight: bold !important; */
}

input.datepicker:focus + .labelpicker .textpicker {
  color: #003d6b;
}
.date-picker-button {
  background-color: white;
  border: solid 1px;
}
.daterangepicker {
  font-family: "Roboto", "Montserrat", "Helvetica", "Arial", sans-serif;
}

.rag-red-outer {
  border: solid 1px !important;
  border-color: red !important;
}

.ag-root-wrapper.ag-layout-normal {
  z-index: 0 !important;
}
.green-text {
  color: green !important;
  font-weight: bold;
}

/* .MuiFormHelperText-root{
  color: red !important;
} */
.htCore th {
  white-space: normal !important;
  word-wrap: break-word;
  overflow-wrap: break-word;
  /* text-align: center; */
  /* vertical-align: start; */
  /* line-height: 1.2; Adjust line height for multi-line headers */
  /* padding: 4px; */
}

.tagged-cell {
  background-color: #ffff00 !important;
  /* font-weight: bold; */
  /* color: black; */
}

.onhold-cell {
  background-color: #fad7a0 !important;
  /* font-weight: bold; */
  /* color: black; */
}

.onholdGrp-cell {
  background-color: #fad7a0 !important;
  /* background-color: #ffc078 !important; */
  /* font-weight: bold; */
  /* color: black; */
}
.launch-ready-cell{
  background-color: #a3e4d7 !important;
}

#hot {
  margin-bottom: 0px;
  min-width: "1600px";
  /* Reduce margin, handled by scrollbar */
}

.sidebar-open #hot {
  width: 84vw !important;
}

.sidebar-closed #hot {
  width: 94vw !important;
}

#customScrollbar {
  margin-left: 265px;
  overflow-x: auto;
}

.sidebar-open #customScrollbar {
  width: 66.5vw !important;
}

.sidebar-closed #customScrollbar {
  width: 76.5vw !important;
}

.handsontable td.htNoWrap {
  overflow: hidden !important;
  white-space: nowrap !important;
  text-overflow: ellipsis !important;
}

/*  */
#customScrollbar div {
  height: 5px;
  width: 100%;
}

/* Hide Handsontable horizontal scrollbar */
.wtHolder {
  /* overflow:hidden !important */
  /* overflow-x: hidden !important;  */
}

.center-header {
  position: relative;
  display: flex;
  justify-content: center;
  /* center horizontally */
  align-items: center;
  /* center vertically */
  padding: 8px;
  text-align: center;
  height: 100%;
  box-sizing: border-box;
}

.center-header .sort-icon {
  position: absolute;
  top: 4px;
  right: 4px;
}

#hot .htColumnHeader {
  position: relative !important;
}

.changeType {
  position: absolute !important;
  top: -6px !important;
  right: -4px !important;
  left: auto !important;
}

.htSelectEditor select {
  caret-color: transparent;
  /* Hide blinking cursor */
}

.dropdown-only {
  cursor: pointer;
}

.always-dropdown-cell {
  position: relative;
  padding-right: 20px;
  /* Reserve space for dropdown icon */
}

.always-dropdown-cell .dropdown-icon {
  position: absolute;
  right: 6px;
  top: 50%;
  transform: translateY(-50%);
  color: #666;
  font-size: 12px;
  pointer-events: none;
}

#hot .handsontable td,
#hot .handsontable th {
  font-size: 10px !important;
}

.pika-single {
  position: absolute !important;
  left: auto !important;
  right: 100% !important;
  /* Stick to left */
  top: 50% !important;
  /* Start from middle of container */
  transform: translateY(-50%) !important;
  /* Shift up by half height */
  margin-right: 4px;
}