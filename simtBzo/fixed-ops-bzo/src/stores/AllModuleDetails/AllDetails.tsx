import {
  Box,
  Button,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  Tooltip,
  useTheme,
} from "@mui/material";
import { AgGridReact } from "ag-grid-react";
import { useEffect } from "react";
import AllDetailsGridDefs from "./AllDetailsGridDefs";
import FileDownloadIcon from "@mui/icons-material/FileDownload";
import { Constants } from "../../utils/constants";
import { traceSpan } from "../../utils/OTTTracing";

const AllDetails = () => {
  const {
    onBtnExport,
    columnDefsLabor,
    defaultColDef,
    onGridReady,
    fetchAllTenantsList,
    tenant,
    allTenants,
    handleTenantFilter,
    store,
    handleStoreFilter,
    allStores,
    selectedModule,
    handleModuleFilter,
    filteredDetailsList,
    handleReset,
    columnDefsParts,
  } = AllDetailsGridDefs();
  const theme = useTheme();
  const onFilterChanged = (e: any) => {
      const filterValues = e.api.getFilterModel();
      Object.keys(filterValues).forEach((colId) => {
        traceSpan(`filter_grid_${colId}`, {
          event: `filter_grid_${colId}`,
          pageUrl: window.location.pathname,
          timestamp: new Date().toISOString(),
          userId: localStorage.getItem("user") || "",
          column: colId,
          filterValue: JSON.stringify(filterValues[colId])
        });
      });
    };
  const onSortChanged = (params: any) => {
    // Get column states and filter only sorted columns
    const sortModel = params.columnApi.getColumnState()
      .filter((col: any) => col.sort != null)
      .map((col: any) => ({
        colId: col.colId,
        sort: col.sort
      }));
  
    sortModel.forEach((sortItem: { colId: string; sort: 'asc' | 'desc' }) => {
      traceSpan(`sort_grid_${sortItem.colId}`, {
         event: `sort_grid_${sortItem.colId}`,
        pageUrl: window.location.pathname,
        timestamp: new Date().toISOString(),
        userId: localStorage.getItem("user") || "",
        column: sortItem.colId,
        direction: sortItem.sort
      });
    });
  };  
  useEffect(() => {
    fetchAllTenantsList();
  }, []);
  return (
    <Box sx={{ paddingX: "10px", mt: "15px", width: "100%" }}>
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "end",
          mb: 2,
        }}>
        <Box
          sx={{
            display: "flex",
            alignItems: "baseline",
            // mb: 2,
            width: "100%",
          }}>
          <FormControl
            variant="outlined"
            sx={{ width: "10%", margin: 0, mr: 2 }}>
            <InputLabel
              id="demo-simple-select-outlined-label"
              sx={{ mt: "6px", fontSize: "14px" }}>
              Module*
            </InputLabel>
            <Select
              variant="outlined"
              labelId="demo-simple-select-outlined-label"
              id="demo-simple-select-outlined"
              value={selectedModule}
              label="Module"
              onChange={handleModuleFilter}
              onFocus={() => {
                traceSpan('module_datepicker_focused', {
                    event: 'module_datepicker_focused',
                    pageUrl: window.location.pathname,
                    timestamp: new Date().toISOString(),
                    userId:
                      localStorage.getItem('user') || ''
                    });
              }}
              sx={{ height: "33px", marginTop: "9px", fontSize: "14px" }}>
              <MenuItem value={"Labor Grid"}>Labor Grid</MenuItem>
              <MenuItem value={"Parts Matrix"}>Parts Matrix</MenuItem>
            </Select>
          </FormControl>
          <FormControl
            variant="outlined"
            sx={{ width: "15%", margin: 0, mr: 2 }}>
            <InputLabel
              id="demo-simple-select-outlined-label"
              sx={{ mt: "6px", fontSize: "14px" }}>
              Tenant
            </InputLabel>
            <Select
              autoWidth
              variant="outlined"
              labelId="demo-simple-select-outlined-label"
              id="demo-simple-select-outlined"
              value={tenant}
              label="Tenant"
              onChange={(e: any) => handleTenantFilter(e, allTenants)}
              onFocus={() => {
                traceSpan('tenant_datepicker_focused', {
                    event: 'tenant_datepicker_focused',
                    pageUrl: window.location.pathname,
                    timestamp: new Date().toISOString(),
                    userId:
                      localStorage.getItem('user') || ''
                    });
              }}
              sx={{ height: "33px", marginTop: "9px", fontSize: "14px" }}>
              {allTenants &&
                allTenants?.map((item: any) => {
                  return (
                    <MenuItem
                      data-name={item.tenantName}
                      key={item.tenantId}
                      value={item.tenantId}>
                      {item.tenantName}
                    </MenuItem>
                  );
                })}
            </Select>
          </FormControl>
          <FormControl
            variant="outlined"
            disabled={tenant ? false : true}
            sx={{ width: "15%", margin: 0, mr: 2 }}>
            <InputLabel
              id="demo-simple-select-outlined-label"
              sx={{ mt: "6px", fontSize: "14px" }}>
              Store
            </InputLabel>
            <Tooltip title={tenant ? "" : "Choose a tenant to select a store"}>
              <Select
                variant="outlined"
                labelId="demo-simple-select-outlined-label"
                id="demo-simple-select-outlined"
                value={store}
                label="Store"
                onChange={handleStoreFilter}
                onFocus={() => {
                traceSpan('store_datepicker_focused', {
                    event: 'store_datepicker_focused',
                    pageUrl: window.location.pathname,
                    timestamp: new Date().toISOString(),
                    userId:
                      localStorage.getItem('user') || ''
                });
              }}
                sx={{ height: "33px", marginTop: "9px", fontSize: "14px" }}>
                {allStores &&
                  allStores?.map((item: any) => {
                    return (
                      <MenuItem key={item.storeId} value={item.storeId}>
                        {item.storeName}
                      </MenuItem>
                    );
                  })}
              </Select>
            </Tooltip>
          </FormControl>
          <Button
            variant="contained"
            disabled={tenant || store ? false : true}
            sx={{ textTransform: "none", padding: "4px 0px" }}
            onClick={handleReset}>
            Reset
          </Button>
        </Box>
        <Box sx={{ alignItems: "center", display: "flex", cursor: "pointer" }}>
          <Tooltip title="Export To Excel">
            <div>
              <FileDownloadIcon
                onClick={() => {
                  traceSpan(`download_excel_moduledetails`, {
                    event: `download_excel_moduledetails`,
                    pageUrl: window.location.pathname,
                    timestamp: new Date().toISOString(),
                    userId: localStorage.getItem("user") || "",
                  });
                  onBtnExport();
                }}
                style={{ color: theme.palette.primary.main }}
              />
            </div>
          </Tooltip>
        </Box>
      </Box>
      <div
        className={Constants.ag_grid_theme}
        style={{ height: "83vh", width: "83vw" }}>
        <AgGridReact
          columnDefs={
            selectedModule === "Labor Grid" ? columnDefsLabor : columnDefsParts
          }
          editType="fullRow"
          rowData={filteredDetailsList}
          defaultColDef={defaultColDef}
          rowSelection="single"
          onGridReady={(params: any) => onGridReady(params)}
          // rowHeight={30}
          singleClickEdit={true}
          suppressColumnVirtualisation={true}
          suppressChangeDetection={true}
          stopEditingWhenCellsLoseFocus={true}
          onFilterChanged={onFilterChanged}
          onSortChanged={onSortChanged}
        />
      </div>
    </Box>
  );
};

export default AllDetails;
