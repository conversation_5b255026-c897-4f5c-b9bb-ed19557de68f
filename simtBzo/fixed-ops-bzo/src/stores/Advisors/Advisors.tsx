import "@ag-grid-community/all-modules/dist/styles/ag-grid.css";
import "@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css";
import { AgGridReact } from "ag-grid-react";
import "ag-grid-community/styles/ag-grid.css";
import "ag-grid-community/styles/ag-theme-balham.css";
import "ag-grid-community/styles/ag-theme-material.css";
import { useEffect, useRef, useState } from "react";
import "../../style.css";
import {
  RecordContextProvider,
  useRecordContext,
  useTranslate,
} from "react-admin";
import { Constants } from "../../utils/constants";
import { Box } from "@mui/material";
import DataMutationQueries from "../../service/mutations";
import AdvGridDefs from "./AdvGridDefs";
import useServiceAdvisors from "../../CustomHooks/useServiceAdvisors";
import SnackBarMessage from "../../components/SnackBarMessage";
import { traceSpan } from "../../utils/OTTTracing";

const AdvisorsList = (props: any) => {
  const { getRowStyle, onRowEditingStarted } = useServiceAdvisors();
  const translate = useTranslate();
  const { storeData } = props;
  const { UpdateAdvisorDetails } = DataMutationQueries;
  const record: any = useRecordContext();
  const gridApiRef: any = useRef();
  let gridRef = useRef<any>();
  const gridColumnApiRef: any = useRef();
  const [rowData, setRowData] = useState();
  const [originalRowData, setOriginalRowData] = useState<any>(null);
  const [saveNewValue, setsaveNewValue] = useState(false);
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const { columnDefs, defaultColDef } = AdvGridDefs(storeData.storeLaunched);

  useEffect(() => {
    setRowData(record);
  }, [record]);

  const onGridReady = (params: any) => {
    gridApiRef.current = params.api;
    gridRef.current = params.api;
    gridColumnApiRef.current = params.columnApi;
  };
  const onCellClicked = (params: any) => {
    const target = params.event.target;
    let action = target.closest("[data-action]")?.dataset.action;
    // Handle click event for action cells
    if (params.column.colId === "action" && action) {
      if (action === Constants.actions.edit) {
        traceSpan(`click_edit_icon`, {
          event: `click_edit_icon`,
          pageUrl: window.location.pathname,
          timestamp: new Date().toISOString(),
          userId: localStorage.getItem('user') || '',
          value: params.node.rowIndex
        });
        const node = params.node;
        const data = node.data;
        params.api.startEditingCell({
          rowIndex: params.node.rowIndex,
          // gets the first columnKey
          colKey:
            gridColumnApiRef?.current?.getDisplayedCenterColumns()[0].colId,
        });
        setOriginalRowData({ ...data });
      }
      if (action === Constants.actions.update) {
        traceSpan(`click_save_icon`, {
          event: `click_save_icon`,
          pageUrl: window.location.pathname,
          timestamp: new Date().toISOString(),
          userId: localStorage.getItem('user') || '',
          value: params.node.rowIndex
        });
        setsaveNewValue(true);
        params.api.stopEditing(false);
      }

      if (action === Constants.actions.cancel) {
        traceSpan(`click_editcancel_icon`, {
          event: `click_editcancel_icon`,
          pageUrl: window.location.pathname,
          timestamp: new Date().toISOString(),
          userId: localStorage.getItem('user') || '',
          value: params.node.rowIndex
        });
        params.api.stopEditing(true);
      }
    }
  };

  const onRowEditingStopped = (params: any) => {
    
    const { api, node } = params;
    const editedData = node.data;
    const originalData: any = originalRowData;
    if (originalData) {    
      if (!saveNewValue) {
        node.setData(originalData);
      } else {
        const updatedData = { ...originalData, ...editedData };
        updatedData.nickname = updatedData.nickname?.replace(/\s+/g, ' ');
        updatedData.storeId =  storeData.storeId;
    
        node.setData(updatedData);
        (editedData.nickname != originalData.nickname ||
          updatedData.active != originalData.active) &&
          UpdateAdvisorDetails({
            ...updatedData,
            realm: storeData.realmName,
            storeId: storeData.storeId,
            tenantId: storeData.tenantId
          }).then((resp: any) => {
            if (resp) {
              setOpenSnackbar(true);
              props.onAdvisorsDetailsUpdated();
            }
          });
      }
    }
    setsaveNewValue(false);
  };

   const onFilterChanged = (e: any) => {
        const filterValues = e.api.getFilterModel();
        Object.keys(filterValues).forEach((colId) => {
          traceSpan(`filter_grid_${colId}`, {
            event: `filter_grid_${colId}`,
          pageUrl: window.location.pathname,
          timestamp: new Date().toISOString(),
          userId: localStorage.getItem("user") || "",
          column: colId,
          filterValue: JSON.stringify(filterValues[colId])
          });
        });
      };
    const onSortChanged = (params: any) => {
      // Get column states and filter only sorted columns
      const sortModel = params.columnApi.getColumnState()
        .filter((col: any) => col.sort != null)
        .map((col: any) => ({
          colId: col.colId,
          sort: col.sort
        }));
    
      sortModel.forEach((sortItem: { colId: string; sort: 'asc' | 'desc' }) => {
        traceSpan(`sort_grid_${sortItem.colId}`, {
        event: `sort_grid_${sortItem.colId}`,
        pageUrl: window.location.pathname,
        timestamp: new Date().toISOString(),
        userId: localStorage.getItem('user') || '',
        column: sortItem.colId,
        direction: sortItem.sort
        });
      });
    };

  return (
    <>
    <div className={Constants.ag_grid_theme}style={{ height: "70vh", width: "50%" }}>
      <AgGridReact
        onRowEditingStopped={onRowEditingStopped}
        onRowEditingStarted={onRowEditingStarted}
        onCellClicked={onCellClicked}
        editType="fullRow"
        suppressClickEdit={true}
        suppressColumnVirtualisation={true}
        columnDefs={columnDefs}
        suppressChangeDetection={true}
        onGridReady={onGridReady}
        rowData={rowData}
        getRowStyle={getRowStyle}
        //suppressCellSelection={true}
        suppressRowClickSelection={true}
        defaultColDef={defaultColDef}
        tooltipShowDelay={0}
        onFilterChanged={onFilterChanged}
        onSortChanged={onSortChanged}
      />
      
    </div>
    <SnackBarMessage
    onClose={() => setOpenSnackbar(false)}
    open={openSnackbar}
    message={translate("SUCCESS_MESSAGES.UPDATE_MESSAGE", {
      entityName: "Advisor details",
    })}
  />
 </>
  );
};

const Advisors = () => {
  const record: any = useRecordContext();
  const { fetchAdvisors, advisorsData } = useServiceAdvisors(record);

  useEffect(() => {
    fetchAdvisors();
  }, []);
  return (
    <Box sx={{ marginTop: 5 }}>
      <RecordContextProvider value={advisorsData}>
        <AdvisorsList
          storeData={record}
          onAdvisorsDetailsUpdated={fetchAdvisors}
        ></AdvisorsList>
      </RecordContextProvider>
    </Box>
  );
};

export default Advisors;
