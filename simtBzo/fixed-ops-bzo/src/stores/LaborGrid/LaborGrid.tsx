import {
  Box,
  Button,
  Grid,
  Paper,
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Typography,
  Tooltip,
  CircularProgress,
  Backdrop,
  useTheme,
} from "@mui/material";
import * as React from "react";
import { useState, useRef, useEffect } from "react";
import { AgGridReact } from "ag-grid-react";
import { useRecordContext, useTranslate } from "react-admin";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import EditIcon from "@mui/icons-material/Edit";
import SaveIcon from "@mui/icons-material/Save";
import * as XLSX from "xlsx";
import GridDefinitions from "./GridDefinitions";
import useLaborGrid from "../../CustomHooks/useLaborGrid";
import NewGridOrMatrix from "../../components/NewGridOrMatrix";
import DeleteButton from "../../components/DeleteButton";
import CancelIcon from "@mui/icons-material/Cancel";
import DatePicker from "react-datepicker";
import { exportMultipleSheetsAsExcel } from "ag-grid-enterprise";
import FileDownloadIcon from "@mui/icons-material/FileDownload";
import CustomDatePickerInput from "../../components/CustomDatePickerInput";
import SnackBarMessage from "../../components/SnackBarMessage";
import CustomLinearProgress from "../../components/CustomLinearProgress";
import Loading from "../../components/Loading";
import moment from "moment";
import { UpdateSubmitStatus } from "../../service/mutations";
import { Constants } from "../../utils/constants";
import GridNameModal from "./GridNameModal";
import { log } from "console";
import MultipleSnackbars from "../../components/MultipleSnackbars";
import { traceSpan } from "../../utils/OTTTracing";
const LaborGrid = (props: any) => {
  const { getStoreQuery } = props;
  const theme = useTheme();
  const {
    onRowEditingStopped,
    setTempData,
    tempData,
    getLaborGridList,
    setOpenSnackbar,
    setEnableSave,
    setIncompleteError,
    setInvalidLength,
    handleSubmitLaborGrid,
    setAddGrid,
    setSaveClickedFlag,
    setInvalidError,
    setInstallDate,
    setRowData,
    setGridType,
    gridType,
    invalidError,
    invalidLength,
    statusMessage,
    openSnackbar,
    rowData,
    addGrid,
    gridTypeList,
    saveClickedFlag,
    enableSave,
    incompleteError,
    installDate,
    listLoading,
    installDateValue,
    setInstallDateValue,
    setStatusMessage,
    updateDate,
    setUpdateDate,
    statusMessageType,
    setStatusMessageType,
    getGridTypesList,
    defaultGridType,
    hasDefaultType,
    gridTypeChoice,
    btnEnable,
    saveDefaultGridType,
    setUpdatedDefaultGridType,
    updatedDefaultGridType,
    setOpenGridTypeModal,
    openGridTypeModal,
    setGridTypeChanged,
    gridTypeChanged,
    newDefaultGridType,
    setNewDefaultGridType,
    reassignGridType,
    openDelete,
    setOpenDelete,
    setReassignGridType,
    isSuccessful,
    noOfRows,
    setNoOfRows,
    snackbarMessages,
    setSnackbarMessages,
    submitLoader,
    setSubmitLoader,
  } = useLaborGrid();
  const { columnDefs, defaultColDef, gridData, LaborGridCols } =
    GridDefinitions(saveClickedFlag, noOfRows);
  const record: any = useRecordContext();
  const translate = useTranslate();
  const gridRef = useRef<any>();
  const [initialRowData, setInitialRowData] = useState<any>();
  const [expandedAccordionIndex, setExpandedAccordionIndex] = useState(null);
  const [submitLoading, setSubmitLoading] = useState<boolean>(false);
  const [gridTypeError, setGridTypeError] = useState<boolean>(false);
  const [gridHeight, setGridHeight] = useState<number>(405);
  const [agGridApi, setAgGridApi] = useState<any>();
  useEffect(() => {
    getLaborGridList({
      tenantId: record.tenantId,
      realm: record.realmName,
      storeId: record.storeId,
      callType: "Grid_Type",
    });
  }, []);

  const onGridReady = (params: any) => {
    gridRef.current = params.api;
    console.log("agGridApI>>>>>>>>>>>>>", gridData, params.api);
    setAgGridApi(params.api);
    setInitialRowData(gridData);
    setTempData(gridData);
    !rowData &&
      setTimeout(() => {
        let rowIndex = 0;
        gridRef.current.setFocusedCell(rowIndex, "col0");
        gridRef.current.startEditingCell({
          rowIndex,
          colKey: "col0",
        });
      }, 100);
    setGridHeight(noOfRows * 25 + 40);
  };

  const getGridWrapper = (action?: string, RowData?: any) => {
    return (
      <>
        <div
          className={Constants.ag_grid_theme}
          style={{
            height: gridHeight,
            width: 1200,
            minHeight: 100,
          }}>
          <AgGridReact
            columnDefs={columnDefs}
            editType="fullRow"
            onGridReady={onGridReady}
            rowData={action === "add" ? initialRowData : RowData}
            defaultColDef={defaultColDef}
            rowHeight={25}
            rowSelection="single"
            animateRows={true}
            // @ts-ignore
            ref={gridRef}
            singleClickEdit={true}
            suppressColumnVirtualisation={true}
            suppressChangeDetection={true}
            onRowEditingStopped={onRowEditingStopped}
            stopEditingWhenCellsLoseFocus={true}
            onRowEditingStarted={() => setEnableSave(true)}
            suppressClickEdit={enableSave ? false : true}
            //suppressRowClickSelection={enableSave ? false : true}
          />
        </div>
      </>
    );
  };

  useEffect(() => {
    getGridTypesList(record);
  }, []);

  const handleRadioChange = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setExpandedAccordionIndex(null);
    setAddGrid((event.target as HTMLInputElement).value);
    setInstallDateValue("");
    setUpdateDate(false);
    setNoOfRows(16);
    if (event.target.value === "grid") {
      setSaveClickedFlag(false);
      setRowData(null);
      setIncompleteError(false);
      setInvalidLength(false);
      setInvalidError(false);
    } else if (event.target.value === "upload") {
    } else if (event.target.value === "fixed_rate") {
    }
  };

  const formatCellValueDescription = (gridTypeData: any) => {
    return gridTypeData.gridType.length > 15
      ? gridTypeData.gridType.substring(0, 15) + `...`
      : gridTypeData.gridType;
  };

  const handleSubmit = async () => {
    setSubmitLoading(true);
    try {
      const result = await UpdateSubmitStatus(
        record.tenantId,
        record.storeId,
        "labor_grid"
      );
      if (result?.results[0]?.status === 1) {
        const statusMessage = translate(result?.results[0]?.msg);
        setStatusMessage(statusMessage);
        setOpenSnackbar(true);
        getStoreQuery();
        setStatusMessageType(Constants.statusType.success);
      }
    } catch (error) {
      const statusMessage = translate("ERROR_MESSAGES.SOMETHING_WENT_WRONG");
      setStatusMessage(statusMessage);
      setOpenSnackbar(true);
      setStatusMessageType(Constants.statusType.error);
    } finally {
      setSubmitLoading(false);
    }
  };

  const addOrRemoveGridRow = (action: string, rowCount: number) => {
    let dummy;
    if (action === "add") {
      traceSpan(`click_addrow_button`, {
        event: `click_addrow_button`,
        pageUrl: window.location.pathname,
        timestamp: new Date().toISOString(),
        userId: localStorage.getItem("user") || "",
      });
      dummy = [...tempData, { hours: rowCount - 1, ...LaborGridCols }];
      setInitialRowData(dummy);
      setTempData(dummy);
      setRowData(dummy);
    } else if (action === "remove") {
      traceSpan(`click_removerow_button`, {
        event: `click_removerow_button`,
        pageUrl: window.location.pathname,
        timestamp: new Date().toISOString(),
        userId: localStorage.getItem("user") || "",
      });
      dummy = tempData.filter((item: any) => item.hours !== noOfRows - 1);
      setInitialRowData(dummy);
      setTempData(dummy);
      setRowData(dummy);
    }
    setGridHeight(rowCount * 25 + 40);
  };

  const cancelGridAddition = () => {
    traceSpan(`click_cancelnewlaborgrid_button`, {
      event: `click_cancelnewlaborgrid_button`,
      pageUrl: window.location.pathname,
      timestamp: new Date().toISOString(),
      userId: localStorage.getItem("user") || "",
    });
    setAddGrid("");
    setExpandedAccordionIndex(null);
    setInstallDateValue("");
    setUpdateDate(false);
    setNoOfRows(16);
    setRowData(null);
  };

  const handleExport = (api: any, fileName: string) => {
    api.exportDataAsCsv({ fileName: `${fileName}.csv` });
  };

  const onBtnExport = async (index: any) => {
    const fileName = `${record.storeName}_LaborGrid_${gridTypeList[index].gridType}_${gridTypeList[index].gridFor}_${gridTypeList[index].storeInstallDate}`;
    try {
      // Import the direct API function to get the result
      const DataFetchQueries = await import("../../service/dataFetchQueries");

      const gridData = await DataFetchQueries.default.GetLaborGridList({
        realm: record.realmName,
        storeId: record.storeId,
        callType: "Grid",
        gridType: gridTypeList[index].gridType,
        gridFor: gridTypeList[index].gridFor,
        createdDate: gridTypeList[index].createdDate,
        tenantId: record.tenantId,
      });

      // Header mapping: originalKey → display name
      const headerMap: Record<string, string> = {
        hours: "",
        col0: "0",
        col1: "0.1",
        col2: "0.2",
        col3: "0.3",
        col4: "0.4",
        col5: "0.5",
        col6: "0.6",
        col7: "0.7",
        col8: "0.8",
        col9: "0.9",
      };

      // Desired column order
      const orderedHeaders = [
        "",
        "0",
        "0.1",
        "0.2",
        "0.3",
        "0.4",
        "0.5",
        "0.6",
        "0.7",
        "0.8",
        "0.9",
      ];

      // Currency formatter
      const formatCurrency = (num: number) =>
        `$${num.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ",")}`;

      // Numerical pattern for validation (based on your Constants)
      const numericalRegex = /^-?\d+(\.\d+)?$/;

      // Apply mapping + formatting
      const mappedData = (gridData || []).map((row: any) => {
        const newRow: any = {};

        Object.keys(headerMap).forEach((originalKey) => {
          const displayKey = headerMap[originalKey];
          const value = row[originalKey];

          // Apply formatting logic
          if (originalKey.startsWith("col")) {
            if (value && numericalRegex.test(value)) {
              // Convert string like "0.48" to number 0.48
              newRow[displayKey] = parseFloat(value);
            } else {
              newRow[displayKey] = value === "" ? 0 : value;
            }
          } else {
            newRow[displayKey] = value;
          }
        });

        return newRow;
      });

      // Create worksheet with ordered headers
      const worksheet = XLSX.utils.json_to_sheet(mappedData, {
        header: orderedHeaders,
      });
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, "Labor Grid");
      XLSX.writeFile(workbook, `${fileName}.csv`, { bookType: "csv" });
    } catch (error) {
      console.error("Error fetching or exporting data:", error);
      alert("Error occurred while exporting data. Please try again.");
    }
  };
  return (
    <Box sx={{ margin: "8px" }}>
      <Box
        sx={{
          display: "flex",
          justifyContent: "flex-end",
          padding: "0 18px",
        }}>
        <Button
          size="small"
          variant="outlined"
          color="primary"
          sx={{
            textTransform: "none",
          }}
          disabled={!btnEnable}
          onClick={() => {
            setUpdatedDefaultGridType("");
            setOpenGridTypeModal(true);
          }}>
          All Model Grid Names
        </Button>
        <GridNameModal
          openGridTypeModal={openGridTypeModal}
          setOpenGridTypeModal={setOpenGridTypeModal}
          setGridTypeChanged={setGridTypeChanged}
          setUpdatedDefaultGridType={setUpdatedDefaultGridType}
          hasDefaultType={hasDefaultType}
          gridTypeChoice={gridTypeChoice}
          updatedDefaultGridType={updatedDefaultGridType}
          defaultGridType={defaultGridType}
          getGridTypesList={getGridTypesList}
          setStatusMessage={setStatusMessage}
          setOpenSnackbar={setOpenSnackbar}
          setStatusMessageType={setStatusMessageType}
          gridTypeChanged={gridTypeChanged}
          saveDefaultGridType={saveDefaultGridType}
          record={record}
          getLaborGridList={getLaborGridList}
          openSnackbar={openSnackbar}
          statusMessage={statusMessage}
          statusMessageType={statusMessageType}
        />
        {!record.isLaborGridSubmitted && (
          <Button
            disabled={gridTypeList.length === 0}
            size="small"
            variant="contained"
            color="primary"
            onClick={handleSubmit}
            sx={{ ml: 1 }}>
            {submitLoading ? <CircularProgress size={22} /> : "Submit"}
          </Button>
        )}
      </Box>
      <NewGridOrMatrix
        cancelGridAddition={cancelGridAddition}
        getGridTypesList={getGridTypesList}
        defaultGridType={defaultGridType}
        hasDefaultType={hasDefaultType}
        gridTypeChoice={gridTypeChoice}
        addGrid={addGrid}
        handleRadioChange={handleRadioChange}
        incompleteError={incompleteError}
        invalidError={invalidError}
        invalidLength={invalidLength}
        getGridWrapper={getGridWrapper}
        handleSubmitLaborGrid={handleSubmitLaborGrid}
        setAddGrid={setAddGrid}
        component="laborGrid"
        installDateValue={installDateValue}
        setInstallDateValue={setInstallDateValue}
        updateDate={updateDate}
        setUpdateDate={setUpdateDate}
        setStatusMessage={setStatusMessage}
        setOpenSnackbar={setOpenSnackbar}
        setStatusMessageType={setStatusMessageType}
        setNoOfRows={setNoOfRows}
        noOfRows={noOfRows}
        addOrRemoveGridRow={addOrRemoveGridRow}
        submitLoader={submitLoader}
        setSubmitLoader={setSubmitLoader}
      />
      {/* } */}
      {listLoading ? (
        <Loading size="small" />
      ) : gridTypeList.length === 0 ? (
        <Box
          sx={{
            display: "flex",
            margin: "30px 0px",
            justifyContent: "center",
          }}>
          <Typography
            sx={{
              fontSize: 16,
              color: "#003d6b",
            }}>
            No data to show
          </Typography>
        </Box>
      ) : (
        <Paper sx={{ margin: "16px" }}>
          {gridTypeList &&
            gridTypeList.map((gridTypeData: any, index: any) => (
              <Accordion
                key={index}
                expanded={index === expandedAccordionIndex}
                style={{ marginBottom: 16 }}
                onChange={(e: React.SyntheticEvent, expanded: boolean) => {
                  setAddGrid("");
                  setRowData(null);
                  setEnableSave(false);
                  setIncompleteError(false);
                  setInvalidLength(false);
                  setInvalidError(false);
                  const isExpanded = expanded;
                  traceSpan(`labourgrid_row_expansion`, {
                    event: `labourgrid_row_expansion`,
                    pageUrl: window.location.pathname,
                    timestamp: new Date().toISOString(),
                    userId: localStorage.getItem("user") || "",
                    action: isExpanded ? "expand" : "collapse",
                    value: index,
                  });
                  if (isExpanded) {
                    setSaveClickedFlag(false);
                    setExpandedAccordionIndex(index);
                    setInstallDate(new Date(gridTypeData.storeInstallDate));
                    setGridType(gridTypeData.gridType);
                    getLaborGridList({
                      tenantId: record.tenantId,
                      realm: record.realmName,
                      storeId: record.storeId,
                      callType: "Grid",
                      gridType: gridTypeData.gridType,
                      gridFor: gridTypeData.gridFor,
                      createdDate: gridTypeData.createdDate,
                    });
                  } else {
                    setExpandedAccordionIndex(null);
                  }
                }}>
                <AccordionSummary
                  expandIcon={<ExpandMoreIcon />}
                  sx={{ display: "flex" }}
                  onClick={(event) => {
                    event.stopPropagation();
                  }}>
                  <Box sx={{ flex: 4 }}>
                    <Box sx={{ display: "flex", flexDirection: "column" }}>
                      <Box sx={{ display: "flex", flexDirection: "row" }}>
                        <Typography
                          sx={{
                            width: "6rem",
                            fontSize: 14,
                            color: "#003d6b",
                            fontWeight: "bold",
                          }}>
                          Grid Period
                        </Typography>
                        <Typography
                          sx={{
                            fontSize: 14,
                            color: "#003d6b",
                            fontWeight: "bold",
                          }}>
                          {gridTypeData.gridOrder == 1
                            ? ": Current"
                            : `: Prior ${gridTypeData.gridOrder - 1}`}
                        </Typography>
                      </Box>
                      <Box sx={{ display: "flex", flexDirection: "row" }}>
                        <Typography
                          sx={{
                            width: "6rem",
                            color: "#003d6b",
                            fontWeight: "bold",
                            fontSize: 14,
                          }}>
                          Grid Name
                        </Typography>
                        <Tooltip title={gridTypeData.gridType}>
                          <Typography
                            sx={{
                              fontSize: 14,
                              color: "#003d6b",
                              fontWeight: "bold",
                            }}>
                            : {formatCellValueDescription(gridTypeData)}
                          </Typography>
                        </Tooltip>
                        {/* )} */}
                      </Box>
                      <Box sx={{ display: "flex", flexDirection: "row" }}>
                        <Typography
                          sx={{
                            width: "6rem",
                            fontSize: 14,
                            color: "#003d6b",
                            fontWeight: "bold",
                          }}>
                          Grid Type
                        </Typography>
                        <Typography
                          sx={{
                            fontSize: 14,
                            color: "#003d6b",
                            fontWeight: "bold",
                          }}>
                          :{" "}
                          {gridTypeData.gridFor.charAt(0).toUpperCase() +
                            gridTypeData.gridFor.slice(1)}
                        </Typography>
                      </Box>
                    </Box>
                  </Box>
                  <Box sx={{ flex: 13 }}>
                    <Box sx={{ display: "flex", flexDirection: "column" }}>
                      <Box sx={{ display: "flex", flexDirection: "row" }}>
                        <Typography
                          sx={{
                            width: "13rem",
                            fontSize: 14,
                            color: "#003d6b",
                            fontWeight: "bold",
                          }}>
                          Store Install Date
                        </Typography>
                        {/* {enableSave && index === expandedAccordionIndex ? (
                          <Box
                            sx={{
                              fontSize: 14,
                              color: "#003d6b",
                              fontWeight: "bold",
                            }}
                            onClick={(event: any) => {
                              event.preventDefault();
                              event.stopPropagation();
                            }}>
                            {": "}
                            <DatePicker
                              dateFormat="MM-dd-yyyy"
                              selected={installDate}
                              onChange={(date: any) => {
                                setInstallDate(date);
                              }}
                              customInput={<CustomDatePickerInput />}
                            />
                          </Box>
                        ) : ( */}
                        <Typography
                          sx={{
                            fontSize: 14,
                            color: "#003d6b",
                            fontWeight: "bold",
                          }}>
                          {": "}
                          {moment(gridTypeData.storeInstallDate).format(
                            "MM/DD/YYYY"
                          )}
                        </Typography>
                        {/* )} */}
                      </Box>
                      {/* <Box sx={{ display: "flex", flexDirection: "row" }}>
                        <Typography
                          sx={{
                            width: "13rem",
                            color: "#003d6b",
                            fontWeight: "bold",
                            fontSize: 14,
                          }}>
                          FOPC Calculated Date From
                        </Typography>
                        <Typography
                          sx={{
                            fontSize: 14,
                            color: "#003d6b",
                            fontWeight: "bold",
                          }}>
                          :{" "}
                          {moment(gridTypeData.createdDate).format(
                            "MM/DD/YYYY"
                          )}{" "}
                          {/* (This may be different from store install date if no
                          prior pricing provided). */}
                      {/* </Typography> */}
                      {/* </Box> */}
                    </Box>
                  </Box>
                  <Box
                    sx={{
                      flex: 0.5,
                      display: "flex",
                      justifyContent: "right",
                      alignItems: "center",
                    }}>
                    <DeleteButton
                      isSuccessful={isSuccessful}
                      openDelete={openDelete}
                      setOpenDelete={setOpenDelete}
                      handleDelete={handleSubmitLaborGrid}
                      setReassignGridType={setReassignGridType}
                      reassignGridType={reassignGridType}
                      newDefaultGridType={newDefaultGridType}
                      gridTypeData={{
                        ...gridTypeData,
                        storeId: record.storeId,
                        realmName: record.realmName,
                        tenantId: record.tenantId,
                      }}
                      setNewDefaultGridType={setNewDefaultGridType}
                      getLaborGridList={getLaborGridList}
                      record={record}
                      gridTypeChoice={gridTypeChoice}
                      gridTypeList={gridTypeList}
                      setExpandedAccordionIndex={setExpandedAccordionIndex}
                    />

                    <Box>
                      <Tooltip title="Export To Excel">
                        <Box
                          onClick={(e) => {
                            traceSpan(`download_excel_labourgrid`, {
                              event: `download_excel_labourgrid`,
                              pageUrl: window.location.pathname,
                              timestamp: new Date().toISOString(),
                              userId: localStorage.getItem("user") || "",
                            });
                            e.stopPropagation(); // Prevent accordion toggle
                            e.preventDefault();
                            onBtnExport(index);
                          }}
                          sx={{
                            cursor: "pointer",
                            display: "flex",
                            alignItems: "center",
                          }}>
                          <FileDownloadIcon sx={{ color: "#6e6e6e" }} />
                        </Box>
                      </Tooltip>
                    </Box>
                  </Box>
                </AccordionSummary>
                {rowData && index === expandedAccordionIndex ? (
                  <AccordionDetails>
                    <Grid container display={"contents"}>
                      <>
                        <Box sx={{ flex: 1 }}>
                          <Box sx={{ display: "flex", flexDirection: "row" }}>
                            <Button
                              onClick={(params: any) => {
                                traceSpan(`click_editgrid_button`, {
                                  event: `click_editgrid_button`,
                                  pageUrl: window.location.pathname,
                                  timestamp: new Date().toISOString(),
                                  userId: localStorage.getItem("user") || "",
                                });
                                setSaveClickedFlag(true);
                                let rowIndex = 0;
                                gridRef.current.setFocusedCell(0, "col0");
                                gridRef.current.startEditingCell({
                                  rowIndex,
                                  colKey: "col0",
                                });
                                setTempData(rowData);
                              }}
                              disabled={enableSave}
                              color="primary"
                              variant="contained"
                              size="small"
                              startIcon={<EditIcon />}
                              sx={{ m: "15px 0", width: "7rem" }}>
                              Edit Grid
                            </Button>
                            <Button
                              disabled={enableSave ? false : true}
                              onClick={() => {
                                traceSpan(`click_updategrid_button`, {
                                  event: `click_updategrid_button`,
                                  pageUrl: window.location.pathname,
                                  timestamp: new Date().toISOString(),
                                  userId: localStorage.getItem("user") || "",
                                });
                                const modifiedGridObject = {
                                  ...gridTypeData,
                                  storeInstallDate: installDate,
                                };
                                !gridTypeError &&
                                  handleSubmitLaborGrid(
                                    {
                                      ...modifiedGridObject,
                                      storeId: record.storeId,
                                      realmName: record.realmName,
                                      edit: true,
                                      tenantId: record.tenantId,
                                    },
                                    "update"
                                  );
                              }}
                              color="primary"
                              variant="contained"
                              size="small"
                              startIcon={<SaveIcon />}
                              sx={{ m: "15px 5px", width: "7rem" }}>
                              Update
                            </Button>
                            <Button
                              disabled={enableSave ? false : true}
                              onClick={() => {
                                traceSpan(`click_canceleditgrid_button`, {
                                  event: `click_canceleditgrid_button`,
                                  pageUrl: window.location.pathname,
                                  timestamp: new Date().toISOString(),
                                  userId: localStorage.getItem("user") || "",
                                });
                                setEnableSave(false);

                                setExpandedAccordionIndex(null);
                              }}
                              color="primary"
                              variant="contained"
                              size="small"
                              startIcon={<CancelIcon />}
                              sx={{ m: "15px 5px", width: "7rem" }}>
                              CANCEL
                            </Button>
                          </Box>
                        </Box>
                        {/* )} */}
                        <Grid item xs={8}>
                          <Typography
                            fontSize={"small"}
                            alignContent={"left"}
                            marginBottom={"5px"}
                            color={"red"}>
                            {incompleteError &&
                              "*Please completely fill in the grid. "}
                            {invalidError && "*Enter numeric values only."}{" "}
                            {invalidLength &&
                              "* Value should contain only five digits with up to two decimal places, e.g. 12345.67"}
                          </Typography>

                          {getGridWrapper("view", rowData)}
                          {enableSave && (
                            <Box display="flex" justifyContent="flex-end">
                              <Button
                                sx={{
                                  textTransform: "none",
                                  fontSize: "12px",
                                }}
                                onClick={() => {
                                  setNoOfRows((prev: number) => {
                                    const updatedRows = prev + 1;
                                    addOrRemoveGridRow("add", updatedRows);
                                    return updatedRows;
                                  });
                                }}>
                                + Add new row
                              </Button>
                              <Button
                                sx={{
                                  textTransform: "none",
                                  fontSize: "12px",
                                }}
                                disabled={noOfRows <= 16}
                                onClick={() => {
                                  setNoOfRows((prev: number) => {
                                    const updatedRows = prev - 1;
                                    addOrRemoveGridRow("remove", updatedRows);
                                    return updatedRows;
                                  });
                                }}>
                                - Remove last row
                              </Button>
                            </Box>
                          )}
                        </Grid>
                      </>
                    </Grid>
                  </AccordionDetails>
                ) : !rowData && index === expandedAccordionIndex ? (
                  <Box
                    sx={{
                      display: "flex",
                      justifyContent: "center",
                    }}>
                    <CustomLinearProgress />
                  </Box>
                ) : (
                  <></>
                )}
              </Accordion>
            ))}
        </Paper>
      )}
      {submitLoader && (
        <Backdrop
          open={submitLoader}
          style={{
            zIndex: 9999,
            color: "#fff",
            position: "absolute",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
          }}>
          <CircularProgress color="inherit" />
        </Backdrop>
      )}
      <SnackBarMessage
        onClose={() => setOpenSnackbar(false)}
        open={openSnackbar}
        message={statusMessage}
        type={statusMessageType}
      />
      {snackbarMessages?.length > 0 && (
        <MultipleSnackbars
          messages={snackbarMessages}
          setSnackbarMessages={setSnackbarMessages}
        />
      )}
    </Box>
  );
};

export default LaborGrid;
