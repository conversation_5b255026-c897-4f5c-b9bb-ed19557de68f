import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>po<PERSON>,
} from "@mui/material";
import * as React from "react";
import { useEffect, useMemo, useRef, useState } from "react";
import { StoreInfo } from "./StoreInfo";
import { useRecordContext, useTranslate } from "react-admin";
import TimezoneSelect from "react-timezone-select";
import { AgGridReact } from "ag-grid-react";
import DataMutationQueries from "../service/mutations";
import SnackBarMessage from "../components/SnackBarMessage";
import StoreQueries from "../service/DataFetchQueries/storeQueries";
import { Constants } from "../utils/constants";
import { traceSpan } from "../utils/OTTTracing";

export const StoreSettings = () => {
  let gridRef = useRef<any>(null);
  const translate = useTranslate();
  const gridApiRef: any = useRef();
  const gridColumnApiRef: any = useRef();
  const record = useRecordContext();
  const [selectedTimezone, setSelectedTimezone] = useState<any>([]);
  const [timezonePrev, setTimezonePrev] = useState<any>();
  const [timezone, setTimezone] = useState<any>();
  const [nickName, setNickName] = useState<string>("");
  const [rowData, setRowData] = useState<any>([]);
  const [isEdit, setIsEdit] = useState<boolean>(false);
  const [selectedData, setSelectedData] = useState<any>();
  const [rawGridApi, setRawGridApi] = useState<any>();
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const { GetStoreNickName } = StoreQueries;
  const { GetSetStoreSettings } = DataMutationQueries;
  const extractValues = (mappings: any) => Object.keys(mappings);
  const DayTypeMapping = {
    1: "Full Day",
    0.5: "Half Day",
    0.25: "Quarter Day",
    0.75: "Three Quarter Day",
  };

  const DayMap: any = {
    Sunday: 1,
    Monday: 2,
    Tuesday: 3,
    Wednesday: 4,
    Thursday: 5,
    Friday: 6,
    Saturday: 7,
  };

  const DayType = extractValues(DayTypeMapping);
  const lookupValue = (mappings: any, key: any) => mappings[key];

  const defaultColDef = useMemo(() => {
    return {
      enableValue: true,
      enableFilter: false,
      suppressMovable: true,
    };
  }, []);

  const columnDefs: any = [
    {
      field: "",
      flex: 1,
      suppressCellFlash: true,
      lockPosition: true,
      chartDataType: "category",
      headerCheckboxSelection: true,
      headerCheckboxSelectionFilteredOnly: true,
      checkboxSelection: true,
      suppressMenu: true,
      cellStyle: function () {
        return {
          cursor: "pointer",
        };
      },
    },
    {
      headerName: "Working Day",
      chartDataType: "series",
      field: "keyname",
      editable: false,
      flex: 3,
      suppressMovable: true,
      cellClass: "textAlign",
      suppressMenu: true,
      unSortIcon: true,
      suppressFilter: true,
      cellStyle() {
        return {
          textAlign: "left",
          border: " 0px white",
          fontSize: "14px",
        };
      },
    },
    {
      headerName: "Select Type",
      field: "keyvalue",
      flex: 4,
      editable: true,
      suppressMenu: true,
      singleClickEdit: true,
      cellEditor: "agSelectCellEditor",
      cellEditorParams: {
        values: DayType,
      },
      valueFormatter: (params: any) => {
        return lookupValue(DayTypeMapping, params.value);
      },
      filter: "agSetColumnFilter",
      refData: DayTypeMapping,
      unSortIcon: true,
      suppressFilter: true,
      cellStyle() {
        return {
          border: " 0px white",
          fontSize: "14px",
          textAlign: "left",
        };
      },
    },
    {
      field: "active",
      width: 36,
      suppressMenu: true,
      hide: true,
      cellStyle: function () {
        return {
          cursor: "pointer",
        };
      },
    },
  ];

  const onSelectionChanged = (e: any) => {
    var rowCount = gridApiRef.current.getSelectedRows();
    if (JSON.stringify(rowCount) !== JSON.stringify(selectedData)) {
      setIsEdit(true);
    }
  };
  const onCellValueChanged = (e: any) => {
    var rowCount = gridApiRef.current.getSelectedRows();
    if (e.node.selected === true) {
      setIsEdit(true);
    }
  };

  const getStoreNickName = () => {
    return GetStoreNickName(record.storeId, record.tenantId)
      .then((res) => {
        setNickName(res&&res.storeNickname);
      })
      .catch((error) => {
        console.error("Error fetching store nickname:", error);
      });
  };
  const onGridReady = async (params: any) => {
    params.api.closeToolPanel();
    gridApiRef.current = params.api;
    gridRef.current = params.api;
    gridColumnApiRef.current = params.columnApi;
     
  try {
    await getStoreNickName();
    await getAgGridData();
    setSelectedData(gridApiRef.current.getSelectedRows());
  } catch (error) {
    console.error("Error during grid setup:", error);
  }
  };

  const getActiveWorkingDays = () => {
    gridApiRef.current.forEachNode((node: any) => {
      node.setSelected(!!node.data && node.data.active > 0);
    });
  };

  const getAgGridData = () => {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        resolve("Data is ready");
      }, 1000);
      GetSetStoreSettings({
        action: "get",
        pGoal: null,
        settingtype: "timezone",
        storeId: record.storeId,
        realm: record.realmName,
        storeNickName: nickName,
        tenantId: record.tenantId
      })
      .then((res) => {
        const timezone = res && res[0] && res[0].keyvalue;
        if (timezone) {
          setSelectedTimezone(timezone.split("(")[0]);
        setTimezonePrev(timezone.split("(")[0]);
        setTimezone(timezone.split("(")[0]);
        }
        
      })
      .catch((error) => {
        console.error("Error fetching timezone settings:", error);
        reject(error);
      });
      
      GetSetStoreSettings({
        action: "get",
        pGoal: null,
        settingtype: "working_days",
        storeId: record.storeId,
        realm: record.realmName,
        storeNickName: nickName,
        tenantId: record.tenantId
      })
      .then((res) => {
        const workingDaysData = res;
        workingDaysData.sort((a: any, b: any) => {
          return DayMap[a.keyname] - DayMap[b.keyname];
        });
        const nonWorkingDays = workingDaysData.map((x: any) =>
          x.keyvalue === "0" ? { ...x, keyvalue: "1" } : x
        );
        setRowData(nonWorkingDays);
      })
      .catch((error) => {
        console.error("Error fetching working days settings:", error);
        reject(error);
      });
    });
  };

  const onChangeTimeZone = (e: any) => {
    traceSpan('timezone_dropdown_changed', {
        event: 'timezone_dropdown_changed',
        pageUrl: window.location.pathname,
        timestamp: new Date().toISOString(),
        userId:
        localStorage.getItem('user') || '',
        value:e.target.value  
     });
    setTimezone(e.value + "" + e.label);
    setSelectedTimezone(e.value);
  };

  const getDifference = (array1: any, array2: any) => {
    return array1.filter((object1: any) => {
      return !array2.some((object2: any) => {
        return object1.keyname === object2.keyname;
      });
    });
  };
  
  const handleStoreSettingsSave = async (e: any) => {
    traceSpan(`click_saveall_button`, {
      event: `click_saveall_button`,
      pageUrl: window.location.pathname,
      timestamp: new Date().toISOString(),
      userId:localStorage.getItem('user') || ''
    });
    let rowDataFiltered = [];
    let selectedRows = gridApiRef.current.getSelectedRows();
  
    selectedRows = selectedRows.map((x: any) =>
      x.active === 0 ? { ...x, active: 1 } : x
    );
  
    let filteredRow = getDifference(rowData, selectedRows);
    rowDataFiltered = filteredRow.map((value: any) => ({
      keyname: value.keyname,
      keyvalue: "0",
      active: 0,
    }));
  
    if (
      isEdit === true ||
      JSON.stringify(timezonePrev) !== JSON.stringify(selectedTimezone)
    ) {
      if (selectedRows.length > 0 && timezone !== undefined) {
        try {
          await GetSetStoreSettings({
            action: "set",
            pGoal: null,
            settingtype: "timezone",
            timezone: timezone,
            storeId: record.storeId,
            realm: record.realmName,
            storeNickName: nickName,
            tenantId: record.tenantId
          });
  
          await GetSetStoreSettings({
            action: "set",
            pGoal: JSON.stringify(selectedRows.concat(rowDataFiltered)),
            settingtype: "working_days",
            storeId: record.storeId,
            realm: record.realmName,
            storeNickName: nickName,
            tenantId: record.tenantId
          }).then(async (res) => {
            if (res[0].keyname) {
              await getStoreNickName();
              await getAgGridData();
              getActiveWorkingDays();
              if (res[0].keyname === "Success") {
                setOpenSnackbar(true);
              }
              setIsEdit(false);
            }
          });
        } catch (error) {
          console.error("Error saving store settings:", error);
        }
      }
    }
  };
  const handleNickName = (e: React.BaseSyntheticEvent) => {
    setNickName(e.target.value);
  };
  return (
    <Box>
      <StoreInfo />
      <Box>
        <Box
          display={"flex"}
          sx={{ width: "25%", justifyContent: "space-between" }}
        >
          <Typography sx={{ mt: 3, ml: 2, fontWeight: "bold" }}>
            Store Settings
          </Typography>
        </Box>
        <Box width={"25%"} sx={{ fontSize: "14px" }}>
          <Typography
            variant="subtitle2"
            sx={{ mt: 1, ml: 2, fontWeight: "bold" }}
          >
            Select Timezone
          </Typography>
          <Box sx={{ paddingX: "20px" }}>
            <TimezoneSelect
              // isDisabled={record.storeLaunched === 'Completed'}
              className="select-wrapperselect-others"
              value={selectedTimezone}
              placeholder="Select"
              timezones={{
                "Pacific/Honolulu": "Hawaii",
                "America/Juneau": "Alaska",
                "America/Los_Angeles": "Pacific Time",
                "America/Boise": "Mountain Time",
                "America/Chicago": "Central Time",
                "America/Detroit": "Eastern Time",
              }}
              onChange={onChangeTimeZone}
              onFocus={() => {
                traceSpan('timezone_dropdown_focused', {
                  event: 'timezone_dropdown_focused',
                  pageUrl: window.location.pathname,
                  timestamp: new Date().toISOString(),
                  userId:
                  localStorage.getItem('user') || ''
                });
              }}
            ></TimezoneSelect>
          </Box>
        </Box>
        <Box width={"25%"}>
          <Typography
            variant="subtitle2"
            sx={{ mt: 1, ml: 2, fontWeight: "bold" }}
          >
            Select Working Days
          </Typography>
          <Box
            sx={{
              paddingX: "20px",
              display: "flex",
            }}
          >
            <Box
              id="data-tab-goal-storeset"
              style={{
                width: "100%",
                display: "block",
              }}
            >
              <div
                className={Constants.ag_grid_theme}
                style={{ height: 250, width: "100%" }}
              >
                <AgGridReact
                  className={Constants.ag_grid_theme}
                  ref={gridRef}
                  rowSelection="multiple"
                  columnDefs={columnDefs}
                  defaultColDef={defaultColDef}
                  onGridReady={onGridReady}
                  rowData={rowData}
                  // rowHeight={30}
                  detailRowAutoHeight={true}
                  //excelStyles={excelStyles}
                  suppressRowClickSelection={true}
                  tooltipShowDelay={0}
                  suppressHorizontalScroll={true}
                  //floatingFilter={false}
                  animateRows={true}
                  enableCharts={true}
                  onSelectionChanged={onSelectionChanged}
                  onCellValueChanged={onCellValueChanged}
                  //onRowSelected={onRowSelected}
                  onFirstDataRendered={(params: any) => {
                    getActiveWorkingDays();
                  }}
                />
              </div>
              <SnackBarMessage
                onClose={() => setOpenSnackbar(false)}
                open={openSnackbar}
                message={translate("SUCCESS_MESSAGES.UPDATE_MESSAGE", {
                  entityName: "Store Settings",
                })}
              />
            </Box>
          </Box>
        </Box>
        <Box width={"25%"}>
          <Typography
            variant="subtitle2"
            sx={{ mt: 1, ml: 2, fontWeight: "bold" }}
          >
            Store Nickname
          </Typography>
          <Box sx={{ paddingX: "20px", width: "100%",fontSize: "12px"  }}>
            <TextField
            InputProps={{
              style:{
                fontSize: "12px"
              }
            }}
              sx={{ width: "100%", fontSize: "12px" }}
              id="outlined-basic"
              variant="outlined"
              onChange={handleNickName}
              defaultValue={nickName}
              value={nickName}
              onBlur={() => {
                traceSpan('input_storenickname_text', {
                   event: 'input_storenickname_text',
                   pageUrl: window.location.pathname,
                   timestamp: new Date().toISOString(),
                   userId:
                  localStorage.getItem('user') || ''
                });
              }}
            />
          </Box>
        </Box>
        <Tooltip title="Save All">
          <Button
            variant="contained"
            style={{
              marginLeft: "20px",
              marginTop: "8px",
              padding: "4px 10px",
              fontSize: "11px",
            }}
            onClick={handleStoreSettingsSave}
            // hide for store launched status
            // disabled={record.storeLaunched === 'Completed'}
          >
            Save All
          </Button>
        </Tooltip>
      </Box>
    </Box>
  );
};
