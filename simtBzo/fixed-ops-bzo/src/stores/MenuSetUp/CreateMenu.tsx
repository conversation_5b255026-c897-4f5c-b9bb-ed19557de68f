import React from "react";
import { Formik, Form, Field, ErrorMessage } from "formik";
import * as Yup from "yup";
import styled from "styled-components";
import {
  <PERSON>ton,
  CardContent,
  Checkbox,
  Grid,
  TextField,
  Typography,
} from "@mui/material";

import { traceSpan } from "../../utils/OTTTracing";

interface FormValues {
  menuName: any;
  mileageInterval: any;
  milesBefore: any;
  milesAfter: any;
  maximumMiles: any;
  defaultMenu: boolean;
}
const FormErrorMsg = styled.div`
  color: red;
  font-size: 12px;
  text-overflow: ellipsis;
  max-width: 100%;
`;

export default function CreateMenu(props: any) {
  const { AddMenu, allMenu, setAccordionCollapsed, GetMenuNames } = props;

  const validationSchema = Yup.object({
    menuName: Yup.string()
      .required("This is required!")
      .matches(
        /^[a-zA-Z0-9 ]*$/,
        "Menu name must not contain special characters"
      )
      .test({
        name: "uniqueMenuName",
        message: "Menu name must be unique",
        test: function (value: any) {
          const lowerCaseValue = value.toLowerCase();
          return !allMenu
            .map((item: any) => item.menuName.toLowerCase())
            .includes(lowerCaseValue);
        },
      })
      .test({
        name: "noTrailingSpace",
        message: "Menu name cannot end with a space",
        test: function (value: any) {
          return value && value[value.length - 1] !== " ";
        },
      }),
    mileageInterval: Yup.number()
      .typeError("Mileage Interval must be a number")
      .required("This is required!")
      .min(1, "Mileage Interval must be greater than 0")
      .max(9999999999, "Value should not exceed 10 digits"),
    maximumMiles: Yup.number()
      .typeError("Maximum Miles must be a number")
      .required("This is required!")
      .max(500000, "Maximum Miles should be less than 5,00,000"),
    milesBefore: Yup.number()
      .typeError("Miles Before must be a number")
      .required("This is required!")
      .max(9999999999, "Value should not exceed 10 digits")
      .test({
        name: "lessThanHalf",
        exclusive: false,
        message:
          "Miles Before should be less than half of the Mileage Interval.",
        test: function (value: any) {
          const mileageInterval = this.parent.mileageInterval;
          return parseFloat(value) < parseFloat(mileageInterval) / 2;
        },
      }),
    milesAfter: Yup.number()
      .typeError("Miles After must be a number")
      .required("This is required!")
      .max(9999999999, "Value should not exceed 10 digits")
      .test({
        name: "lessThanHalf",
        exclusive: false,
        message:
          "Miles After should be less than half of the Mileage Interval.",
        test: function (value: any) {
          const mileageInterval = this.parent.mileageInterval;
          return parseFloat(value) < parseFloat(mileageInterval) / 2;
        },
      }),
  });
  const handleSubmit = async (
    values: FormValues,
    {
      setSubmitting,
      resetForm,
    }: { setSubmitting: (isSubmitting: boolean) => void; resetForm: () => void }
  ) => {
    traceSpan(`click_createmenu_button`, {
      event: `click_createmenu_button`,
      pageUrl: window.location.pathname,
      timestamp: new Date().toISOString(),
      userId:localStorage.getItem('user') || ''
    });
    setSubmitting(true);
    setAccordionCollapsed(true);
    try {
      await AddMenu(values);
      console.log(JSON.stringify(values, null, 2));
      resetForm();
    } catch (error) {
      console.error("Failed to submit form:", error);
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <>
      <span style={{ display: "flex", width: "100%" }}>
        <Typography
          style={{
            marginLeft: "44%",
            marginTop: 5,
            display: "flex",
            width: "77%",
            fontWeight: "bold",
          }}
        >
          Create Menu
        </Typography>
      </span>

      <CardContent style={{ maxHeight: "500px", overflowY: "auto" }}>
        <Formik
          initialValues={{
            menuName: "",
            mileageInterval: "",
            milesBefore: "",
            milesAfter: "",
            maximumMiles: "",
            defaultMenu: allMenu.length == 0 ? true : false,
          }}
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
        >
          {({ isSubmitting, values, setFieldValue }) => (
            <Form>
              <Grid container spacing={1}>
                <Grid item xs={12} sm={4}>
                  <Typography
                    variant="body1"
                    style={{
                      fontSize: "13px",
                      padding: "15px 0px 0px 10px",
                      color: "rgb(0, 61, 107)",
                    }}
                  >
                    Menu Name <span style={{ color: "red" }}>*</span>
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <Field
                    name="menuName"
                    as={TextField}
                    variant="outlined"
                    size="small"
                    sx={{
                      "& .css-1n4twyu-MuiInputBase-input-MuiOutlinedInput-input":
                        {
                          height: "1em",
                        },
                    }}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                      let value = e.target.value;
                      // Replace multiple spaces with a single space
                      value = value.replace(/\s{2,}/g, " ");
                      // Trim leading spaces
                      if (value[0] === " ") {
                        value = value.substring(1);
                      }
                      setFieldValue("menuName", value);
                    }}
                    onBlur={() => {
                       traceSpan('input_menuname_text', {
                         event: 'input_menuname_text',
                         pageUrl: window.location.pathname,
                         timestamp: new Date().toISOString(),
                         userId:
                         localStorage.getItem('user') || ''
                       });
                    }}
                  />
                  <ErrorMessage name="menuName" component={FormErrorMsg} />
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Typography
                    variant="body1"
                    style={{
                      fontSize: "13px",
                      padding: "15px 0px 0px 10px",
                      color: "rgb(0, 61, 107)",
                    }}
                  >
                    Mileage Interval <span style={{ color: "red" }}>*</span>
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <Field
                    name="mileageInterval"
                    as={TextField}
                    variant="outlined"
                    size="small"
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                      let value = e.target.value.replace(/\D/g, "");
                      if (value.length > 1 && value[0] === "0") {
                        value = value.substring(1);
                      } else if (value === "0") {
                        value = "";
                      }
                      setFieldValue("mileageInterval", value);
                    }}
                    sx={{
                      "& .css-1n4twyu-MuiInputBase-input-MuiOutlinedInput-input":
                        {
                          height: "1em",
                        },
                    }}
                    onBlur={() => {
                       traceSpan('input_mileageinterval_text', {
                         event: 'input_mileageinterval_text',
                         pageUrl: window.location.pathname,
                         timestamp: new Date().toISOString(),
                         userId:
                         localStorage.getItem('user') || ''
                       });
                    }}
                  />

                  <ErrorMessage
                    name="mileageInterval"
                    component={FormErrorMsg}
                  />
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Typography
                    variant="body1"
                    style={{
                      fontSize: "13px",
                      padding: "15px 0px 0px 10px",
                      color: "rgb(0, 61, 107)",
                    }}
                  >
                    Miles Before <span style={{ color: "red" }}>*</span>
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <Field
                    name="milesBefore"
                    as={TextField}
                    variant="outlined"
                    size="small"
                    sx={{
                      "& .css-1n4twyu-MuiInputBase-input-MuiOutlinedInput-input":
                        {
                          height: "1em",
                        },
                    }}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                      let value = e.target.value.replace(/\D/g, "");
                      if (value.length > 1 && value[0] === "0") {
                        value = value.substring(1);
                      } else if (value === "0") {
                        value = "";
                      }
                      setFieldValue("milesBefore", value);
                    }}
                    onBlur={() => {
                       traceSpan('input_milesbefore_text', {
                         event: 'input_milesbefore_text',
                         pageUrl: window.location.pathname,
                         timestamp: new Date().toISOString(),
                         userId:
                         localStorage.getItem('user') || ''
                       });
                    }}
                  />
                  <ErrorMessage name="milesBefore" component={FormErrorMsg} />
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Typography
                    variant="body1"
                    style={{
                      fontSize: "13px",
                      padding: "15px 0px 0px 10px",
                      color: "rgb(0, 61, 107)",
                    }}
                  >
                    Miles After <span style={{ color: "red" }}>*</span>
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <Field
                    name="milesAfter"
                    as={TextField}
                    variant="outlined"
                    size="small"
                    sx={{
                      "& .css-1n4twyu-MuiInputBase-input-MuiOutlinedInput-input":
                        {
                          height: "1em",
                        },
                    }}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                      let value = e.target.value.replace(/\D/g, "");
                      if (value.length > 1 && value[0] === "0") {
                        value = value.substring(1);
                      } else if (value === "0") {
                        value = "";
                      }
                      setFieldValue("milesAfter", value);
                    }}
                    onBlur={() => {
                       traceSpan('input_milesafter_text', {
                         event: 'input_milesafter_text',
                         pageUrl: window.location.pathname,
                         timestamp: new Date().toISOString(),
                         userId:
                         localStorage.getItem('user') || ''
                       });
                    }}
                  />
                  <ErrorMessage name="milesAfter" component={FormErrorMsg} />
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Typography
                    variant="body1"
                    style={{
                      fontSize: "13px",
                      padding: "15px 0px 0px 10px",
                      color: "rgb(0, 61, 107)",
                    }}
                  >
                    Maximum Miles <span style={{ color: "red" }}>*</span>
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <Field
                    name="maximumMiles"
                    as={TextField}
                    variant="outlined"
                    size="small"
                    sx={{
                      "& .css-1n4twyu-MuiInputBase-input-MuiOutlinedInput-input":
                        {
                          height: "1em",
                        },
                    }}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                      let value = e.target.value.replace(/\D/g, "");
                      if (value.length > 1 && value[0] === "0") {
                        value = value.substring(1);
                      } else if (value === "0") {
                        value = "";
                      }
                      setFieldValue("maximumMiles", value);
                    }}
                    onBlur={() => {
                       traceSpan('input_maximummiles_text', {
                         event: 'input_maximummiles_text',
                         pageUrl: window.location.pathname,
                         timestamp: new Date().toISOString(),
                         userId:
                         localStorage.getItem('user') || ''
                       });
                    }}
                  />
                  <ErrorMessage name="maximumMiles" component={FormErrorMsg} />
                </Grid>
                <Grid item xs={12} sm={4}>
                  <Typography
                    variant="body1"
                    style={{
                      fontSize: "13px",
                      padding: "10px 0px 0px 10px",
                      color: "rgb(0, 61, 107)",
                    }}
                  >
                    Default Menu
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                    <Field
                      name="defaultMenu"
                      type="checkbox"
                      as={Checkbox}
                      style={{ cursor: "pointer", padding: "10px 0px 0px 10px" }}
                      size="small"
                      disabled={allMenu.length == 0 ? true : false}
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                        setFieldValue("defaultMenu", e.target.checked);
                        traceSpan('click_defaultmenu_checkbox', {
                          event: 'click_defaultmenu_checkbox',
                          pageUrl: window.location.pathname,
                          timestamp: new Date().toISOString(),
                          userId: localStorage.getItem('user') || '',
                          checked: e.target.checked
                        });
                      }}
                    />
                </Grid>

                <Grid item xs={12} sm={4}></Grid>
                <Grid item xs={12} sm={8}>
                  <Button
                    type="submit"
                    variant="contained"
                    color="primary"
                    disabled={isSubmitting}
                    size="small"
                  >
                    Create
                  </Button>
                </Grid>
              </Grid>
            </Form>
          )}
        </Formik>
      </CardContent>
    </>
  );
}
