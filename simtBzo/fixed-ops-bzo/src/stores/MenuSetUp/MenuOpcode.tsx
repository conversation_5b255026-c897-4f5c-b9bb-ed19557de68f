import * as React from "react";
import {
  List,
  ListItem,
  ListItemText,
  Typography,
  Dialog,
  Paper,
  Tooltip,
  ListItemButton,
} from "@mui/material";
import clsx from "clsx";
import { makeStyles } from "@material-ui/styles";
import { useEffect } from "react";
import CloseIcon from "@material-ui/icons/Close";
import IconButton from "@mui/material/IconButton";
import SwapHorizIcon from "@material-ui/icons/SwapHoriz";
import InfoOutlinedIcon from "@material-ui/icons/InfoOutlined";
var lodash = require("lodash");
const useStyles = makeStyles((theme) => ({
  mainLabel: {
    marginLeft: -20,
    display: "flex",
    color: "rgb(0, 61, 107)",
    fontSize: 12,
  },
  roleMappingContent: {
    width: "100%",
    margin: "auto",
  },
  titleLabel: {
    display: "flex",
    color: "rgb(0, 61, 107)",
  },
  card: {
    width: "98%",
    marginLeft: "20px",
    marginTop: "28px",
    height: "400px",
  },
  label: {
    color: "rgb(0, 61, 107)",
    fontSize: 14,
    marginTop: "16px",
  },
  txt: {
    width: "400px",
  },
  rmdata: {
    width: "39%",
    float: "left",
    margin: "0 1%",
  },
  rmdataCenter: {
    width: "17%",
    float: "left",
    border: "none",
  },
  rmdatadescCenter: {
    minHeight: "150px",
    marginTop: "78px",
    textAlign: "center",
  },
  rmdatadesc: {
    border: "1px solid #a7a7a7",
    minHeight: "150px",
  },
  rolelabel: {
    padding: "8px 0",
    // color: 'rgb(0, 61, 107)',
    color: "#001837",
    fontWeight: 500,
  },
  rmbutton: {
    marginTop: "10px",
  },
  addremovebtn: {
    width: "100px",
    cursor: "pointer",
  },
  selected: {
    backgroundColor: "#ddeaf4 !important",
    "&:focus": {
      backgroundColor: "#ddeaf4 ",
    },
  },
  dialog: {
    position: "absolute",
    top: 50,
  },
  listItemText: {
    color: "#212121",
  },
}));

const MenuOpcode = (props: any) => {
  const {
    filteredOpcode,
    leftOpcode,
    rightOpcode,
    setRightOpcode,
    setLeftOpcode,
  } = props;
  const [opcode, setOpcode] = React.useState([]);
  const [selectedIndex, setSelectedIndex] = React.useState([]);
  const [leftSelected, setLeftSelected] = React.useState<any>([]);
  const [rightSelected, setRightSelected] = React.useState<any>([]);
  const [selectedItem, setSelectedItem] = React.useState<any>(null);
  const [openAlert, setOpenAlert] = React.useState(false);
  const [alertMsg, setAlertMsg] = React.useState("");
  const [alertType, setAlertType] = React.useState("");
  const [userName, setUserName] = React.useState("");
  const classes = useStyles();
  const handleListItemClick = (
    event: React.MouseEvent<HTMLDivElement, MouseEvent>,
    index: number,
    item: any,
    title: string
  ) => {
    let newSelected;
    if (title === "Choices") {
      newSelected = [...leftSelected];
      const selectedIndexs = newSelected.findIndex(
        (selectedItem) => selectedItem.opcode === item.opcode
      );
      if (selectedIndexs === -1) {
        newSelected.push(item);
      } else {
        newSelected.splice(selectedIndexs, 1);
      }
      setLeftSelected(newSelected);
      setRightSelected([]);
    } else {
      newSelected = [...rightSelected];
      const selectedIndexs = newSelected.findIndex(
        (selectedItem) => selectedItem.opcode === item.opcode
      );
      if (selectedIndexs === -1) {
        newSelected.push(item);
      } else {
        newSelected.splice(selectedIndexs, 1);
      }
      setRightSelected(newSelected);
      setLeftSelected([]);
    }
  };

  const handleCheckedRight = () => {
    var isExist = rightOpcode.some((element: any) =>
      leftSelected.includes(element)
    );

    var filtered = leftOpcode.filter(
      (item: any) => !leftSelected.includes(item)
    );
    if (!isExist) {
      setRightOpcode(rightOpcode.concat(leftSelected));
      setLeftOpcode(filtered);
      setLeftSelected([]);
      setSelectedIndex([]);
    }
  };

  const handleCheckedLeft = () => {
    var isExist = leftOpcode.some((element: any) =>
      rightSelected.includes(element)
    );
    var filtered = rightOpcode.filter(
      (item: any) => !rightSelected.includes(item)
    );
    if (!isExist) {
      setLeftOpcode(leftOpcode.concat(rightSelected));
      setRightOpcode(filtered);
      setRightSelected([]);
      setSelectedIndex([]);
    }
  };

  const customList = (title: any, items: any) => (
    <Paper style={{ height: "300px", overflow: "auto" }}>
      <List
        dense
        component="div"
        role="list"
        style={{ paddingTop: 1, paddingBottom: 1 }}
      >
        {items.map((value: any, index: any) => {
          const labelId = `transfer-list-all-item-${value.opcode}-label`;
          return (
            <ListItemButton 
              key={value.opcode}
              onClick={(event: any) =>
                handleListItemClick(event, index, value, title)
              }
              selected={selectedIndex.some(
                (item: any) => item.opcode == value.opcode
              )}
              className={
                (title === "Choices" &&
                  leftSelected.some(
                    (item: any) => item.opcode === value.opcode
                  )) ||
                (title === "Chosen" &&
                  rightSelected.some(
                    (item: any) => item.opcode === value.opcode
                  ))
                  ? classes.selected
                  : ""
              }
              id={"itemList" + title}
              style={{ cursor: "pointer" }}
            >
              <ListItemText
                id={labelId}
                primary={value.opcode}
                classes={{ primary: classes.listItemText }}
              />
              <Tooltip title={value.opcodedescription}>
                <InfoOutlinedIcon
                  style={{
                    width: "12px",
                    height: "12px",
                    gap: "5 !important",
                  }}
                />
              </Tooltip>
            </ListItemButton>
          );
        })}
      </List>
    </Paper>
  );
  return (
    <>
      <div style={{ width: "100%" }}>
        <div className={classes.rmdata}>
          <div className="rmdata-title">
            <Typography
              className={clsx(classes.rolelabel)}
              style={{ fontSize: "14px" }}
            >
              Select Menu Opcode<span style={{ color: "red" }}>*</span>
              {props.checkOpcode == true && (
                <span
                  style={{
                    color: "red",
                    fontSize: 11,
                    letterSpacing: "0.33px",
                    fontWeight: 400,
                  }}
                >
                  Please select an opcode
                </span>
              )}
            </Typography>
          </div>
          <div className={classes.rmdatadesc}>
            {customList("Choices", leftOpcode)}
          </div>
        </div>
        <div className={classes.rmdataCenter}>
          <div className={classes.rmdatadescCenter}>
            <Typography
              className={clsx(classes.rolelabel)}
              style={{ fontSize: "12px" }}
            >
              Add/Remove
            </Typography>
            {leftSelected.length > 0 && (
              <SwapHorizIcon
                onClick={handleCheckedRight}
                style={{ cursor: "pointer" }}
              />
            )}
            {rightSelected.length > 0 && (
              <SwapHorizIcon
                onClick={handleCheckedLeft}
                style={{ cursor: "pointer" }}
              />
            )}

            {leftSelected?.length == 0 && rightSelected.length == 0 && (
              <Tooltip title="Please select an opcode">
                <SwapHorizIcon style={{ cursor: "not-allowed" }} />
              </Tooltip>
            )}
          </div>
        </div>
        <div className={classes.rmdata}>
          <div className="rmdata-title">
            {" "}
            <Typography
              className={clsx(classes.rolelabel)}
              style={{ fontSize: "14px" }}
            >
              Selected Opcode
            </Typography>
          </div>
          <div className={classes.rmdatadesc}>
            {customList("Chosen", rightOpcode)}
          </div>
        </div>
      </div>
    </>
  );
};
export default MenuOpcode;
