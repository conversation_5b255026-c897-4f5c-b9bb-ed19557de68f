import React, { useRef, useState, useEffect } from "react";
import Box from "@mui/material/Box";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogContentText from "@mui/material/DialogContentText";
import { Button, DialogTitle } from "@mui/material";
import { traceSpan } from "../../utils/OTTTracing";
const DeleteDialog = (props: any) => {

  const {
    openDelInt,
    openPopup,
    menuName,
    handlePopupClose,
    handleDeleteMenu,
    confirmDeleteInt,
    selectedTab,
  } = props;
  const [openDialog, setOpenDialog] = useState(openPopup);
  const [openInDialog, setOpenInDialog] = useState(openDelInt);
  const [selected, setSelected] = useState("Both");
  var displaymsg: string;

  useEffect(() => {
    setOpenDialog(openPopup);
    setOpenInDialog(openDelInt);
  }, [openPopup, openDelInt]);

  const handleClose = () => {
    setOpenDialog(false);
    setOpenInDialog(false);
    handlePopupClose();
  };

  if (menuName && selectedTab != "inner") {
    displaymsg =
      "Are you sure you want to permanently delete the selected menu and its corresponding model mapping?";
  } else {
    displaymsg =
      "Are you sure, you want to permanently delete the selected Interval?";
  }

  const handleDelete = () => {
    // handleSelected(selected);
    if (selectedTab == "inner") {
      setOpenInDialog(false);
      confirmDeleteInt();
    } else {
      setOpenDialog(false);
      handleDeleteMenu();
    }
  };

  return (
    <div>
      <Dialog
        open={selectedTab == "inner" ? openInDialog : openDialog}
        onClose={handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle id="alert-dialog-title">
          {selectedTab == "inner" ? "Remove Interval?" : "Remove Menu?"}
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="alert-dialog-description">
            {displaymsg}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose}>Cancel</Button>
          <Button onClick={handleDelete} autoFocus>
            Ok
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
}

export default DeleteDialog;
