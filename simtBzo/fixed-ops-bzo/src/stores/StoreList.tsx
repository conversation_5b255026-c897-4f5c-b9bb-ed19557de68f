import React from "react";
import { Box, Button, FormControlLabel, Radio, RadioGroup, Tooltip, useTheme } from "@mui/material";
import { AgGridReact } from "ag-grid-react";
import StoreGridDef from "./StoreGridDef";
import FileDownloadIcon from "@mui/icons-material/FileDownload";
import { useNavigate } from "react-router-dom";
import { Constants } from "../utils/constants";
import useAllStoreList from "../CustomHooks/useAllStoreList";
import { traceSpan } from "../utils/OTTTracing";

const StoreList = () => {
  const { columnDefs, defaultColDef,  onBtnExport, setApi } =
    StoreGridDef();
    const {selectedStore, handleRadioChange, onGridReady, allStoresList} = useAllStoreList();
  const theme = useTheme();
  const navigate = useNavigate();
  const realmRole = JSON.parse(localStorage.getItem("role") || "");
  const handleGridReady = (params: { api: any; }) => {
  setApi(params.api);  
  onGridReady(params);
}

const onFilterChanged = (e: any) => {
    const filterValues = e.api.getFilterModel();
    Object.keys(filterValues).forEach((colId) => {
      traceSpan(`allstore_filter_grid_${colId}`, {
        event: `filter_grid_${colId}`,
          pageUrl: window.location.pathname,
          timestamp: new Date().toISOString(),
          userId: localStorage.getItem("user") || "",
          column: colId,
          filterValue: JSON.stringify(filterValues[colId])
      });
    });
  };

  const onSortChanged = (params: any) => {
      // Get column states and filter only sorted columns
      const sortModel = params.columnApi.getColumnState()
        .filter((col: any) => col.sort != null)
        .map((col: any) => ({
          colId: col.colId,
          sort: col.sort
        }));
    
      sortModel.forEach((sortItem: { colId: string; sort: 'asc' | 'desc' }) => {
        traceSpan(`sort_grid_${sortItem.colId}`, {
           event: `sort_grid_${sortItem.colId}`,
          pageUrl: window.location.pathname,
          timestamp: new Date().toISOString(),
          userId: localStorage.getItem("user") || "",
          column: sortItem.colId,
          direction: sortItem.sort
        });
      });
    };

  return (
    <Box sx={{ paddingX: "10px", mt: "15px", width: "100%" }}>
      <Box sx={{ display: "flex", justifyContent: "space-between" }}>
      <RadioGroup
  sx={{ fontWeight: "bold" }}
  row
  aria-labelledby="demo-row-radio-buttons-group-label"
  name="row-radio-buttons-group"
  value={selectedStore}
  onChange={(event) => {
    traceSpan(`click_onboardingorlaunched_radiobutton`, {
      event: `click_onboardingorlaunched_radiobutton`,
      pageUrl: window.location.pathname,
      timestamp: new Date().toISOString(),
      userId: localStorage.getItem('user') || '',
      value: event.target.value
    });
    handleRadioChange(event);
  }}
>
        <FormControlLabel
          value={"onboarding"}
          control={
            <Radio
              size="small"
              sx={{
                color: "#003d6b",
                "&.Mui-checked": {
                  color: "#003d6b",
                },
              }}
            />
          }
          label={"Currently Onboarding"}
        />
        <FormControlLabel
          value="launched"
          control={
            <Radio
              size="small"
              sx={{
                color: "#003d6b",
                "&.Mui-checked": {
                  color: "#003d6b",
                },
              }}
            />
          }
          label="Launched"
          sx={{fontSize: "12px"}}
        />
      </RadioGroup>

        <div
          style={{
            display: "flex",
            justifyContent: "flex-end",
            alignItems: "center",
          }}
        >
          {/* {realmRole === "superadmin" && */}
          <Button
            sx={{ textTransform: "none", mr: 2 }}
            onClick={() =>{
              traceSpan(`click_allmoduledetail_button`, {
                    event: `click_allmoduledetail_button`,
                    pageUrl: window.location.pathname,
                    timestamp: new Date().toISOString(),
                    userId:localStorage.getItem('user') || ''
                  });
              navigate("/statelessServiceBzoStoreDetails/AllDetails")
            }
          }
          >
            All module details
          </Button>
          {/* } */}
          <Tooltip title="Export To Excel">
            <div>
              <FileDownloadIcon
  onClick={() => {
    traceSpan(`download_excel_allstoredetails`, {
      event: `download_excel_allstoredetails`,
      pageUrl: window.location.pathname,
      timestamp: new Date().toISOString(),
      userId: localStorage.getItem('user') || ''
    });
    onBtnExport();
  }}
  style={{ color: theme.palette.primary.main, cursor: 'pointer' }}
/>
            </div>
          </Tooltip>
        </div>
      </Box>

      <Box sx={{ width: "100%", marginTop: "15px" }}>
        <div
          className={Constants.ag_grid_theme}
          style={{ height: "85vh",marginTop: 2, maxWidth: '100vw' }}
        >
          <AgGridReact
            columnDefs={columnDefs}
            rowData={allStoresList}
            defaultColDef={defaultColDef}
            onGridReady={handleGridReady}
            pagination={true}
            onFilterChanged={onFilterChanged}
            onSortChanged={onSortChanged}
          />
        </div>
      </Box>
    </Box>
  );
};

export default StoreList;
