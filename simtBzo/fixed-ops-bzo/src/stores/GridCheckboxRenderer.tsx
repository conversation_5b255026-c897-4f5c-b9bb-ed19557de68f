import React from "react";


/*This page not using Now*/ 

const CustomCheckbox = (props: any, params: any) => {
  const checkedHandler = (event: any) => {
    const checked = event.target.checked;
    const colId = props.column.colId;

    if (props.column.colId === "gridExcluded") {
      props.node.setDataValue(colId, checked === true ? 1 : 0);
      if (checked === true) {
        props.node.setDataValue("laborFixedRate", 0);
        props.node.setDataValue("partsFixedRate", 0);
        props.context.componentParent.onLaborFixedDateChanged(
          props.data.laborFixedratedate,
          null
        );
        props.context.componentParent.onPartsFixedDateChanged(
          props.data.partsFixedratedate,
          null
        );
        props.context.componentParent.onLaborFixedRateChanged(
          props.data.laborFixedratevalue,
          "0"
        );
        props.context.componentParent.onPartsFixedRateChanged(
          props.data.partsFixedratevalue,
          "N/A"
        );
      }

      props.api.refreshCells({
        columns: ["laborFixedRate", "partsFixedRate"],
        rowNodes: [props.node],
        force: true,
      });
    }
  };

  return props.colDef.field === "gridExcluded" ? (
    <input
      type="checkbox"
      className="grid-excluded"
      disabled={
        props.context.componentParent.editedRowId === null
          ? true
          : props.context.componentParent.editedRowId !== props.rowIndex
          ? true
          : false
      }
      onClick={checkedHandler}
      checked={props.value === 1 ? true : false}
    />
  ) : (
    ""
  );
};

export default CustomCheckbox;
