import "ag-grid-community/styles/ag-grid.css";
import "ag-grid-community/styles/ag-theme-alpine.css";
import { useState, ChangeEvent } from "react";
import { Box, Tab, Tabs } from "@mui/material";
import { TabPanel } from "../../Layout/TabPanel";
import { useRecordContext } from "react-admin";
import MappingGrid from "./GridMapping/MappingGrid";
import MenuMappingGrid from "./MenuMapping/MenuMappingGrid";
import { traceSpan } from "../../utils/OTTTracing";

const Modal = () => {
  const record = useRecordContext();
  const [tabValue, setTabValue] = useState(0);
  const handleTabChange = (event: ChangeEvent<{}>, newValue: number) => {
    const target = event.target as HTMLElement;
    const clickedTab = target.closest('[role="tab"]') as HTMLElement;
    const tabLabel = clickedTab?.textContent || clickedTab?.innerText || '';
    traceSpan(`click_${tabLabel}_tab`, {
              event: `click_${tabLabel}_tab`,
              pageUrl: window.location.pathname,
              timestamp: new Date().toISOString(),
              userId:localStorage.getItem('user') || ''
            });
    setTabValue(newValue);
  };

  return (
    <Box>
      <Box>
        <Tabs
          value={tabValue}
          indicatorColor="primary"
          textColor="primary"
          onChange={handleTabChange}
        >
          <Tab label={"Menu-Model Mapping"} />
          <Tab label={"Grid-Model Mapping"} />
        </Tabs>
      </Box>
      <TabPanel value={tabValue} index={0}>
        <MenuMappingGrid mappingType="menu" />
      </TabPanel>
      <TabPanel value={tabValue} index={1}>
        <MappingGrid mappingType="gridType" />
      </TabPanel>
    </Box>
  );
};

export default Modal;
