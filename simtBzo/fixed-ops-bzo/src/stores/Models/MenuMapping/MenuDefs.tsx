import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { Constants } from "../../../utils/constants";
import { Tooltip } from "@mui/material";
import DeleteIcon from "@material-ui/icons/DeleteOutline";
import { loadMenuModelData } from "../../../service/dataFetchQueries";
import { useRecordContext, useTranslate } from "react-admin";
import { updateModels } from "../../../service/mutations";

const MenuDefs = (saveClickedFlag?: boolean) => {
  const [leftRowData, setLeftRowData] = useState<any>();
  const [rightRowData, setRightRowData] = useState<any>();
  const record: any = useRecordContext();
  console.log("rrrrr----", record);
  const [leftApi, setLeftApi] = useState<any>(null);
  const [leftColumnApi, setLeftColumnApi] = useState<any>(null);
  const [rightApi, setRightApi] = useState<any>(null);
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [statusMessageType, setStatusMessageType] = useState<any>("");
  const [statusMessage, setStatusMessage] = useState<any>("");
  const translate = useTranslate();
  const gridRef = useRef<any>(null);
  const rightColumnDefs: any = [
    {
      headerName: "Menu Name",
      field: "menuName",
      rowGroup: true,
      hide: true,
    },
    {
      headerName: "Make",
      field: "make",
      rowGroup: true,
      hide: true,
      cellRenderer: (params: any) => (params.value ? params.value : ""),
    },
    {
      headerName: "Model",
      field: "modelName",
      cellRenderer: (params: any) => (params.value ? params.value : ""),
      hide: true,
    },
    {
      headerName: "Actions",
      cellRendererFramework: (params: any) => {
        const make = params?.node?.data?.make;
        return !params.node.group && make !== null && make !== "" ? (
          <span style={{ display: "flex", justifyContent: "space-evenly" }}>
            <Tooltip title="Delete">
              <DeleteIcon
                htmlColor="rgb(0, 61, 107)"
                id={"edit" + params.rowIndex}
                style={{ width: 18, left: "8", top: "70%", cursor: "pointer" }}
                onClick={() => resetModel(params)}></DeleteIcon>
            </Tooltip>
          </span>
        ) : (
          params?.node?.field == "make" && (
            <span style={{ display: "flex", justifyContent: "space-evenly" }}>
              <Tooltip title="Delete">
                <DeleteIcon
                  htmlColor="rgb(0, 61, 107)"
                  id={"edit" + params.rowIndex}
                  style={{
                    width: 18,
                    left: "8",
                    top: "70%",
                    cursor: "pointer",
                  }}
                  onClick={() => resetMakes(params)}></DeleteIcon>
              </Tooltip>
            </span>
          )
        );
      },
      suppressMenu: true,
      maxWidth: 90,
      suppressColumnsToolPanel: true,
      cellStyle: { border: "none" },
      suppressNavigable: true,
      sortable: false,
      filter: false,
      unSortIcon: false,
      pinned: "right",
    },
  ];

  const leftColumnDefs: any = [
    {
      headerName: "Make",
      field: "make",
      rowGroup: true,
      hide: true,
      filter: false,
      suppressMoveWithinParent: true,
    },
    {
      headerName: "Model",
      field: "modelName",
      hide: true,
      filter: false,
      suppressMoveWithinParent: true,
    },
  ];

  const defaultColDefRight = useMemo(() => {
    return {
      flex: 1,
      minWidth: 100,
    };
  }, []);

  useEffect(() => {
    GetMenuModelData();
  }, []);

  const onGridReady = (params: any, side: any) => {
    if (side === 0) {
      setLeftApi(params.api);
      setLeftColumnApi(params.columnApi);
    }
    if (side === 1) {
      setRightApi(params.api);
    }
  };

  const GetMenuModelData = async () => {
    try {
      const res = await loadMenuModelData(record.tenantId, record.storeId);
      const modelsWithNoMenuName = res.filter(
        (item: any) => item.menuName == null
      );
      const modelsWithMenuName = res.filter(
        (item: any) => item.menuName != null
      );
      setLeftRowData(modelsWithNoMenuName);
      setRightRowData(modelsWithMenuName);
    } catch (error) {
      const statusMessage = translate("ERROR_MESSAGES.SOMETHING_WENT_WRONG");
      setStatusMessageType(Constants.statusType.error);
      setStatusMessage(statusMessage);
      setOpenSnackbar(true);
      console.log(error);
      throw error;
    }
  };

  const defaultColDefLeft = useMemo(() => {
    return {
      flex: 1,
      minWidth: 100,
    };
  }, []);

  const autoGroupColumnDef = useMemo(() => {
    return {
      headerName: "Model",
      field: "modelName",
      rowDrag: (params: any) => (params.data ? true : false),
      cellRenderer: "agGroupCellRenderer",
      cellRendererParams: {
        suppressCount: true,
        getRowNodeId: (params: any) => (params.data ? params.data.model : null),
        checkbox: true,
      },
    };
  }, []);

  const autoGroupColumnDefright = useMemo(() => {
    return {
      headerName: "Menu",
      field: "modelName",
      cellRenderer: "agGroupCellRenderer",
      suppressMenu: true,
      suppressColumnsToolPanel: true,
      sortable: false,
      filter: false,
      unSortIcon: false,
      cellRendererParams: {
        suppressCount: true,
      },
    };
  }, []);

  const handleSelectionChanged = (params: any, gridId: any) => {
    const selectedNodes = leftApi.getSelectedNodes();
    const uniqueMakes = new Set();
    selectedNodes.forEach((node: any) => {
      if (node?.data?.make) {
        uniqueMakes.add(node.data.make);
      }
    });
    uniqueMakes.forEach((make) => {
      leftApi.forEachNode((node: any) => {
        if (node?.group && node?.field === "make" && node?.key === make) {
          if (
            selectedNodes.includes(node) ||
            hasSelectedChildren(node, selectedNodes)
          ) {
            leftApi.setRowNodeExpanded(node, true);
          }
        }
      });
    });
  };

  const hasSelectedChildren = (parentNode: any, selectedNodes: any) => {
    let hasSelectedChild = false;
    leftApi.forEachNode((childNode: any) => {
      if (
        childNode?.parent === parentNode &&
        selectedNodes.includes(childNode)
      ) {
        hasSelectedChild = true;
      }
    });
    return hasSelectedChild;
  };

  const onDragStop = useCallback(
    (params: any) => {
      const { nodes, overNode } = params;
      const Menu = overNode?.key || overNode?.parent?.key;
      if (Menu && overNode?.field == "menuName") {
        const pValData = nodes.map((node: any) =>
          generatePValData(node, Menu, Constants.menuMappingGqlCallType.map)
        );
        params?.api?.showLoadingOverlay();
        updateModel(pValData, false);
      } else {
        if (params && params.overNode == null) {
          setStatusMessageType(Constants.statusType.warning);
          const statusMessage = translate(
            "WARNING_MESSAGES.MENU_NOT_AVAILABLE"
          );
          setStatusMessage(statusMessage);
          setOpenSnackbar(true);
        } else {
          setStatusMessageType(Constants.statusType.warning);
          const statusMessage = translate(
            "WARNING_MESSAGES.ASSIGN_PROPER_MENU"
          );
          setStatusMessage(statusMessage);
          setOpenSnackbar(true);
        }
      }
    },
    [leftApi]
  );

  useEffect(() => {
    if (!leftApi || !rightApi) {
      return;
    }
    const dropZoneParams = rightApi.getRowDropZoneParams({ onDragStop });
    leftApi.removeRowDropZone(dropZoneParams);
    leftApi.addRowDropZone(dropZoneParams);
  }, [leftApi, rightApi, onDragStop]);

  const showLoadingOverlays = () => {
    rightApi?.showLoadingOverlay();
    leftApi?.showLoadingOverlay();
  };

  const hideLoadingOverlays = (props?: any) => {
    rightApi?.hideOverlay();
    leftApi?.hideOverlay();
    props?.api?.hideOverlay();
  };

  const generatePayloadForReset = (params: any, p_process: string) => {
    const generatedJSON: any = {};
    generatedJSON.menuname = params.data?.menuName || params.node.parent.key;
    generatedJSON.make = params.data?.make || params.node.key;
    generatedJSON.model = params.data?.modelName || "";
    generatedJSON.p_process = p_process;
    return JSON.stringify([generatedJSON]);
  };

  const generatePValData = (node: any, menuName: any, map: any) => ({
    p_process: map,
    menuname: menuName,
    model: node.data && node.data.modelName,
    make: node.data && node.data.make,
  });

  const resetModel = async (params: any, props?: any) => {
    const pValue = generatePayloadForReset(
      params,
      Constants.menuMappingGqlCallType.unmapModel
    );
    try {
      showLoadingOverlays();
      const result = await updateModels(
        pValue,
        record.realmName,
        record.storeId,
        record.tenantId
      );
      if (result.string === Constants.success) {
        GetMenuModelData();
        setOpenSnackbar(true);
        const statusMessage = translate("SUCCESS_MESSAGES.MODEL_UNMAPPED");
        setStatusMessageType(Constants.statusType.success);
        setStatusMessage(statusMessage);
      }
    } catch (error) {
      hideLoadingOverlays(props);
      setOpenSnackbar(true);
      const statusMessage = translate("ERROR_MESSAGES.SOMETHING_WENT_WRONG");
      setStatusMessageType(Constants.statusType.error);
      setStatusMessage(statusMessage);
      setOpenSnackbar(true);
      console.log(error);
    }
  };

  const resetMakes = async (params: any, props?: any) => {
    const pValue = generatePayloadForReset(
      params,
      Constants.menuMappingGqlCallType.unmapMake
    );
    try {
      showLoadingOverlays();
      const result = await updateModels(
        pValue,
        record.realmName,
        record.storeId,
        record.tenantId
      );
      if (result.string === Constants.success) {
        GetMenuModelData();
        setOpenSnackbar(true);
        const statusMessage = translate("SUCCESS_MESSAGES.MAKE_UNMAPPED");
        setStatusMessageType(Constants.statusType.success);
        setStatusMessage(statusMessage);
      }
    } catch (error) {
      hideLoadingOverlays(props);
      setOpenSnackbar(true);
      const statusMessage = translate("ERROR_MESSAGES.SOMETHING_WENT_WRONG");
      setStatusMessageType(Constants.statusType.error);
      setStatusMessage(statusMessage);
      setOpenSnackbar(true);
      console.log(error);
    }
  };

  const updateModel = async (pValue: any, props?: any) => {
    try {
      showLoadingOverlays();
      const result = await updateModels(
        JSON.stringify(pValue),
        record.realmName,
        record.storeId,
        record.tenantId
      );
      if (result.string === Constants.success) {
        GetMenuModelData();
        const statusMessage = translate("SUCCESS_MESSAGES.UPDATE_MESSAGE", {
          entityName: pValue[0]?.menuname,
        });

        setStatusMessageType(Constants.statusType.success);
        setStatusMessage(statusMessage);
        setOpenSnackbar(true);
      }
    } catch (error) {
      hideLoadingOverlays(props);
      const statusMessage = translate("ERROR_MESSAGES.SOMETHING_WENT_WRONG");
      setStatusMessageType(Constants.statusType.error);
      setStatusMessage(statusMessage);
      setOpenSnackbar(true);
      console.log(error);
    }
  };

  return {
    leftRowData,
    defaultColDefLeft,
    leftColumnDefs,
    rightRowData,
    rightColumnDefs,
    defaultColDefRight,
    autoGroupColumnDef,
    autoGroupColumnDefright,
    gridRef,
    openSnackbar,
    statusMessage,
    statusMessageType,
    onGridReady,
    handleSelectionChanged,
    setOpenSnackbar,
  };
};

export default MenuDefs;
