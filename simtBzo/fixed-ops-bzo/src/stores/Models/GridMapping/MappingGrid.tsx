import React, { useState, useEffect } from "react";
import Grid from "@material-ui/core/Grid";
import { AgGridReact } from "ag-grid-react";
import ModelsDefs from "./ModelsDefs";
import SnackBarMessage from "../../../components/SnackBarMessage";
import { useRecordContext } from "react-admin";
import { Constants } from "../../../utils/constants";
import {
  Box,
  Button,
  FormControl,
  FormControlLabel,
  InputLabel,
  MenuItem,
  Radio,
  RadioGroup,
  Select,
  SelectChangeEvent,
  Typography,
} from "@mui/material";
import { traceSpan } from "../../../utils/OTTTracing";

const MappingGrid = (mappingType: any) => {
  const record: any = useRecordContext();
  const {
    defaultColDefLeft,
    leftColumns,
    rightColumns,
    defaultColDefRight,
    autoGroupColumnDef,
    autoGroupColumnDefright,
    onGridReady,
    fetchGridModels,
    openSnackbar,
    statusMessage,
    leftApi,
    rightApi,
    onDragStop,
    leftRowData,
    rightRowData,
    setOpenSnackbar,
    handleSelectionChanged,
    statusMessageType,
    operation,
    handleRadioChange,
    gridTypesDropdown,
    selectedItems,
    moveGridType,
    setMoveGridType,
    selectedGrid,
    MoveGridType,
    handleSubmit,
    statusFlag,
    setStatusflag,
  } = ModelsDefs(record);
  const [loading, setLoading] = useState(true);

  const handleRowExpansion = (event: any) => {
    const rowData = event.data;
    const isExpanded = event.expanded;
    traceSpan(`menu_row_` + isExpanded ? "expansion" : "collapse", {
      event: `menu_row_` + isExpanded ? "expansion" : "collapse",
      pageUrl: window.location.pathname,
      timestamp: new Date().toISOString(),
      userId: localStorage.getItem("user") || "",
      action: isExpanded ? "expand" : "collapse",
      rowIndex: event.rowIndex,
    });
  };

  // Fetch grid types
  useEffect(() => {
    setLoading(true);
    fetchGridModels();
  }, []);

  //-------------------------------To expand the models when a whole make is selected----------------------------//

  // Function to check if any child nodes of the given parent node are selected

  useEffect(() => {
    if (!leftApi || !rightApi) {
      return;
    }
    const dropZoneParams = rightApi.getRowDropZoneParams({ onDragStop });
    leftApi.removeRowDropZone(dropZoneParams);
    leftApi.addRowDropZone(dropZoneParams);
  }, [leftApi, rightApi, onDragStop]);

  //-------------Ag Grid------------------//
  const getGridWrapper = (id: number) => (
    <div
      className={Constants.ag_grid_theme}
      style={{ height: "65vh", width: "20vw", position: "relative" }}>
      <AgGridReact
        defaultColDef={id === 0 ? defaultColDefLeft : defaultColDefRight}
        rowData={id === 0 ? leftRowData : rightRowData}
        columnDefs={id === 0 ? leftColumns : rightColumns}
        autoGroupColumnDef={
          id === 0 ? autoGroupColumnDef : autoGroupColumnDefright
        }
        onSelectionChanged={(params) => handleSelectionChanged(params, id)}
        onGridReady={(params) => onGridReady(params, id)}
        groupAllowUnbalanced
        rowSelection="multiple"
        rowDragMultiRow={id === 0}
        suppressRowClickSelection={true}
        rowDragEntireRow
        groupSelectsChildren
        getRowHeight={(params) => {
          if (id === 1) {
            if (params.node.level === 0) return 50;
            else return 28;
          }
          return undefined;
        }}
        getRowStyle={(params) => {
          if (id === 1) {
            if (params.node.level === 0) {
              return {
                paddingTop: "10px",
                boxSizing: "border-box",
                backgroundColor: "#f5f5f5",
                color: "#000",
              };
            } else {
              return {
                paddingTop: "0px",
                boxSizing: "border-box",
                backgroundColor: "#fff",
                color: "#000",
              };
            }
          }
          return undefined;
        }}
        onRowGroupOpened={handleRowExpansion}
      />

      {/* ✅ Primary download button */}
      {(id === 0 || id === 1) && (
        <Button
          style={{ top: 8, float: "right" }}
          variant="contained"
          color="primary"
          size="small"
          onClick={() => {
            if (id === 0 && leftApi) {
              leftApi.exportDataAsExcel({ fileName: "ModelGrid.xlsx" });
            } else if (id === 1 && rightApi) {
              rightApi.exportDataAsExcel({ fileName: "GridType.xlsx" });
            }
          }}>
          Download
        </Button>
      )}
    </div>
  );
  const handleChange = (event: SelectChangeEvent) => {
    setMoveGridType(event.target.value);
  };
  return (
    <Box sx={{ position: "relative", padding: "16px" }}>
      <Box sx={{ position: "absolute", top: 8, right: 8 }}>
        <Button
          color="primary"
          variant="contained"
          disabled={
            !statusFlag &&
            leftRowData &&
            leftRowData.length === 0 &&
            rightRowData &&
            rightRowData.length >= 0
              ? false
              : true
          }
          sx={{ fontSize: "12px", padding: "4px 12px" }}
          onClick={handleSubmit}>
          Submit
        </Button>
      </Box>

      <Box
        sx={{
          display: "flex",
          flexDirection: "row",
          alignItems: "flex-start",
          gap: 2,
        }}>
        <Box>{getGridWrapper(0)}</Box>

        <Box>{getGridWrapper(1)}</Box>
        {selectedGrid === "right" && selectedItems.length !== 0 && (
          <Box
            sx={{
              display: "flex",
              bgcolor: "background.paper",
              border: "1px solid rgb(200, 200, 200)",
              p: "16px 24px",
              height: "fit-content",
              flexDirection: "column",
              ml: 2,
              width: "20vw",
            }}>
            <Typography
              id="modal-modal-title"
              variant="h6"
              color="#003d6b"
              sx={{
                fontSize: "12px",
                fontWeight: "bold",
                marginBottom: "10px",
              }}>
              Move / Delete
            </Typography>
            <RadioGroup
              sx={{
                fontWeight: "bold",
                display: "flex",
                flexDirection: "row",
                gap: 1,
              }}
              row
              aria-labelledby="demo-row-radio-buttons-group-label"
              name="row-radio-buttons-group"
              value={operation}
              onChange={handleRadioChange}>
              <Typography
                variant="body2"
                sx={{
                  fontSize: "12px",
                  fontWeight: "bold",
                  color: "rgb(90, 90, 90)",
                  mr: 2,
                }}>
                Action:{" "}
              </Typography>
              <FormControlLabel
                control={
                  <Radio
                    sx={{
                      "& .MuiSvgIcon-root": {
                        fontSize: 14,
                      },
                      p: 0.25,
                    }}
                  />
                }
                label={
                  <Typography variant="body2" sx={{ fontSize: "12px" }}>
                    Move
                  </Typography>
                }
                value="move"
              />
              <FormControlLabel
                control={
                  <Radio
                    sx={{
                      "& .MuiSvgIcon-root": {
                        fontSize: 14,
                      },
                      p: 0.25,
                    }}
                  />
                }
                label={
                  <Typography variant="body2" sx={{ fontSize: "12px" }}>
                    Delete
                  </Typography>
                }
                value="delete"
              />
            </RadioGroup>
            <Box
              sx={{
                fontWeight: "bold",
                display: "flex",
                flexDirection: "row",
                gap: 1,
                alignItems: "baseline",
                justifyContent: "space-between",
                mt: "8px",
                opacity: operation === "move" ? 1 : 0.5,
              }}>
              <Typography
                variant="body2"
                sx={{
                  fontSize: "12px",
                  fontWeight: "bold",
                  color: "rgb(90, 90, 90)",
                  mt: 1,
                }}>
                Select grid type:{" "}
              </Typography>
              <FormControl
                sx={{ width: "60%", margin: 0 }}
                size="small"
                variant="outlined">
                <InputLabel
                  id="demo-simple-select-small-outlined-label"
                  sx={{ mt: "6px", fontSize: "14px", width: "fit-content" }}>
                  Grid Type
                </InputLabel>
                <Select
                  variant="outlined"
                  labelId="demo-simple-select-small-outlined-label"
                  id="demo-simple-select-small-outlined"
                  value={moveGridType}
                  label="Age"
                  onChange={handleChange}
                  sx={{
                    height: "26px",
                    marginTop: "12px",
                    fontSize: "12px",
                    backgroundColor:
                      operation !== "move" ? "rgb(234, 234, 234)" : "",
                  }}
                  disabled={operation !== "move"}>
                  {gridTypesDropdown.map((item: any) => {
                    return (
                      item.gridCount != 0 && (
                        <MenuItem sx={{ fontSize: "12px" }} value={item.value}>
                          {" "}
                          {item.value}{" "}
                          {item.isDefaultGridType && (
                            <span
                              style={{
                                fontStyle: "italic",
                                opacity: 0.7,
                                marginLeft: 4,
                              }}>
                              {` (Default)`}
                            </span>
                          )}
                        </MenuItem>
                      )
                    );
                  })}
                </Select>
              </FormControl>
            </Box>

            <Box sx={{ display: "flex", justifyContent: "flex-end", mt: 1 }}>
              <Button
                color="primary"
                variant="contained"
                disabled={!operation || (operation === "move" && !moveGridType)}
                sx={{
                  width: "fit-content",
                  padding: "4px 12px",
                  minWidth: 0,
                  fontSize: "12px",
                }}
                onClick={MoveGridType}>
                Save
              </Button>
            </Box>
          </Box>
        )}
      </Box>

      <SnackBarMessage
        onClose={() => setOpenSnackbar(false)}
        open={openSnackbar}
        message={statusMessage}
        type={statusMessageType}
      />
    </Box>
  );
};

export default MappingGrid;
