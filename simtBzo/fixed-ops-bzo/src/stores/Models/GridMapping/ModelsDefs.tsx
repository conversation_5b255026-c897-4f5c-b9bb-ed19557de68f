import { ColDef } from "ag-grid-community";
import { useCallback, useMemo, useRef, useState } from "react";
import { Tooltip } from "@mui/material";
import DeleteIcon from "@material-ui/icons/DeleteOutline";
import {
  GetLaborMissesGridTypes,
  GetLaborMissesModels,
  insertGridModelMapping,
} from "../../../service/mutations";
import LaborGridQueries from "../../../service/LaborGrid/LaborGridQueries";
import StoreMutations from "../../../service/Mutations/storeMutations";
const ModelsDefs = (record: any) => {
  const [leftApi, setLeftApi] = useState<any>();
  const [rightApi, setRightApi] = useState<any>(null);
  const [rowData, setRowData] = useState<any>([]);
  const [gridTypes, setGridTypes] = useState<any>([]);
  const [gridTypesDefaultList, setGridTypesDefaultList] = useState<any>([]);
  const [gridTypesDropdown, setGridTypesDropdown] = useState<any>([]);
  const [leftRowData, setLeftRowData] = useState<any>();
  const [rightRowData, setRightRowData] = useState<any>();
  const [statusMessage, setStatusMessage] = useState<any>("");
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [statusMessageType, setStatusMessageType] = useState<any>("");
  const [selectedItems, setSelectedItems] = useState<any[]>([]);
  const [operation, setOperation] = useState<string>("");
  const [moveGridType, setMoveGridType] = useState("");
  const [selectedGrid, setSelectedGrid] = useState("");
  const [rightDeselectFlag, setRightDeselectFlag] = useState(false);
  const [leftDeselectFlag, setLeftDeselectFlag] = useState(false);
  const [statusFlag, setStatusflag] = useState(false);
  const { GetGridTypeMaster } = LaborGridQueries;
  const [uniqueMakes, setUniqueMakes] = useState<Set<string>>(new Set());
  const { UpdateSubmissionStatus } = StoreMutations;

  // Ag-Grid definitions
  const leftColumns: ColDef[] = [
    { field: "make", rowGroup: true, hide: true, headerName: "Make" },
    {
      field: "model",
      hide: true,
      headerName: "Model",
    },
  ];
  const rightColumns: ColDef[] = [
    {
      headerName: "Grid Type",
      field: "gridType",
      rowGroup: true,
      hide: true,
    },
    {
      headerName: "Make",
      field: "make",
      rowGroup: true,
      hide: true,
    },
    { headerName: "Model", field: "model", hide: true },
  ];
  const onGridReady = useCallback((params: any, side: any) => {
    if (side === 0) {
      setLeftApi(params.api);
    }
    if (side === 1) {
      setRightApi(params.api);
    }
  }, []);

  //Fetch and set grid types and models to respective states
  const fetchGridModels = async () => {
    const LaborGridTypes = await GetLaborMissesGridTypes(record).then((res) => {
      return res;
    });
    const LaborMissesModels = await GetLaborMissesModels(record).then((res) => {
      return res;
    });
    const GridTypeWithDefaultList = await GetGridTypeMaster(
      record.storeId,
      record.tenantId
    ).then((res) => {
      return res;
    });
    Promise.all([
      LaborGridTypes,
      LaborMissesModels,
      GridTypeWithDefaultList,
    ]).then(([gridTypeChoices, makeModels, gridTypesList]) => {
      setRowData(makeModels);
      setGridTypes(gridTypeChoices);
      const gridTypesListSorted: any = gridTypesList.sort((a: any, b: any) => {
        if (a.value < b.value) return -1;
        if (a.value > b.value) return 1;
        return 0;
      });

      setStatusflag(LaborMissesModels[0].gridModelMapping);
      setGridTypesDefaultList(gridTypesListSorted);
      setGridTypesDropdown(gridTypesListSorted);
      const tempGrid = LaborMissesModels.filter(
        (item: any) => item.categorized == 1
      );
      const tempNullGrid = LaborMissesModels.filter(
        (item: any) => item.categorized != 1
      );

      // Check if any category is missing in the rightRowData
      const gridTypesInRightTable = tempGrid.map((item: any) => item.gridType);
      const gridTypesFromAPI = gridTypeChoices.map(
        (item: any) => item.gridType
      );
      const missingGridTypes = [...gridTypesFromAPI].filter(
        (category) => !gridTypesInRightTable.includes(category)
      );
      // Create dummy rows for the missing gridTypes and add them to rightRowData
      const dummyRows = missingGridTypes.map((gridType) => ({
        gridType: gridType,
        make: null,
        model: "No data available",
        __typename: "LaborMissesGridType",
      }));
      const updatedRightRowData: any = [...tempGrid, ...dummyRows].sort(
        (a, b) => {
          if (a.gridType < b.gridType) return -1;
          if (a.gridType > b.gridType) return 1;
          return 0;
        }
      );
      setLeftRowData(tempNullGrid);
      setRightRowData(updatedRightRowData);
    });
  };

  const onDragStop = useCallback(
    (params: any) => {
      const { overNode } = params;
      const gridType = overNode?.key || overNode?.parent?.key;
      if (gridType && overNode?.field === "gridType") {
        const pValData = selectedItems.map((item) =>
          generatePValData(item, gridType, "map")
        );
        const pValue = JSON.stringify(pValData);
        params?.api?.showLoadingOverlay();
        updateModelMapping(pValue, false, params);
      } else {
        setStatusMessageType("warning");
        const statusMessage =
          overNode == null
            ? "Grid Type is not Available"
            : "Please Assign to Proper Grid Type";
        setStatusMessage(statusMessage);
        setOpenSnackbar(true);
      }
    },
    [selectedItems] // Depend on selectedItems state
  );

  const updateModelMapping = async (
    pValData: any,
    resetFlag: boolean,
    props?: any,
    action?: string
  ) => {
    try {
      rightApi?.showLoadingOverlay();
      action !== "update" && leftApi?.showLoadingOverlay();
      const result = await insertGridModelMapping(pValData, record);
      if (result.string === "Success") {
        setStatusMessageType("success");
        fetchGridModels();
        const statusMessage = resetFlag
          ? "Unmapping successful"
          : pValData.length > 1
          ? "Mappping successful"
          : "mapped 1 make";
        setStatusMessage(statusMessage);
        setOpenSnackbar(true);
      } else {
        setStatusMessageType("error");
        const statusMessage = result.string;
        setStatusMessage(statusMessage);
        setOpenSnackbar(true);
        rightApi?.hideOverlay();
        leftApi?.hideOverlay();
        props?.api?.hideOverlay();
      }
      setSelectedItems([]);
      setOperation("");
      setMoveGridType("");
    } catch (error) {
      rightApi?.hideOverlay();
      leftApi?.hideOverlay();
      props?.api?.hideOverlay();
      setStatusMessageType("error");
      const statusMessage = "Something went wrong";
      setStatusMessage(statusMessage);
      setOpenSnackbar(true);
      console.log(error);
      setOperation("");
      setMoveGridType("");
    }
  };

  const generatePValData = (item: any, gridType: any, map: any) => ({
    p_process: map,
    gridtype: map === "unmap" ? item.gridType : gridType,
    model: item.model,
    make: item.make,
  });

  const generatePayloadForReset = (params: any, p_process: string) => {
    const generatedJSON: any = {};
    generatedJSON.gridtype = params.data?.gridType || params.node.parent.key;
    generatedJSON.make = params.data?.make || params.node.key;
    generatedJSON.model = params.data?.model || "";
    generatedJSON.p_process = p_process;
    return JSON.stringify([generatedJSON]);
  };

  const resetModel = async (params: any) => {
    const pValue: any = generatePayloadForReset(params, "unmap");
    params?.api?.showLoadingOverlay();
    updateModelMapping(pValue, true, params);
  };

  const handleSelectionChanged = (params: any, gridId: any) => {
    // if (gridId !== 0) return; // Only track selection for the left grid

    //If the function is called after deselection of any grid, break it from here and reset the flags
    if (
      (gridId === 0 && leftDeselectFlag) ||
      (gridId === 1 && rightDeselectFlag)
    ) {
      setRightDeselectFlag(false);
      setLeftDeselectFlag(false);
      return;
    }
    gridId === 0 ? setSelectedGrid("left") : setSelectedGrid("right");
    // Determine which grid API to use
    const gridApi = gridId === 0 ? leftApi : rightApi;

    const selectedNodes =
      gridId === 0 ? leftApi.getSelectedNodes() : rightApi.getSelectedNodes();
    const selectedData = selectedNodes.map((node: any) => ({
      make: node.data?.make,
      model: node.data?.model,
      gridType: node.data?.gridType,
    }));

    // Filter out gridTypes present in nodes
    const selectedGridTypeNodes = selectedData.map(
      (item: any) => item.gridType
    );
    const filteredGridTypes = gridTypesDefaultList.filter(
      (item: any) => !selectedGridTypeNodes.includes(item.value)
    );
    setGridTypesDropdown(filteredGridTypes);

    setSelectedItems(selectedData);
    if (selectedData.length === 0) {
      setOperation("");
      setMoveGridType("");
    }

    // Expand row groups for each unique 'make' value when selected
    const tempUniqueMakes: Set<string> = new Set(
      selectedNodes.map((node: any) => node.data?.make).filter(Boolean)
    ); // A temporary set to show all selected makes
    const difference =
      uniqueMakes &&
      new Set(
        Array.from(tempUniqueMakes).filter((item) => !uniqueMakes.has(item))
      ); // To get the last selected make
    tempUniqueMakes.forEach((make: any) => {
      // For each selected makes
      gridApi.forEachNode((node: any) => {
        // Get node for each make
        if (
          node.field === "make" &&
          node.key === make &&
          difference?.has(make)
        ) {
          gridApi.setRowNodeExpanded(node, true); // Expand the now selected make
        }
      });
    });
    setUniqueMakes(tempUniqueMakes);
    if (gridId === 0) {
      setRightDeselectFlag(true);
      setLeftDeselectFlag(false);
      rightApi.deselectAll(true);
    } else {
      setRightDeselectFlag(false);
      setLeftDeselectFlag(true);
      leftApi.deselectAll(true);
    }
  };

  const defaultColDefRight = useMemo(() => {
    return {
      flex: 1,
      minWidth: 100,
    };
  }, []);
  const defaultColDefLeft = useMemo(() => {
    return {
      flex: 1,
      minWidth: 100,
    };
  }, []);
  const autoGroupColumnDef = useMemo(() => {
    return {
      headerName: "Model",
      field: "model",
      rowDrag: (params: any) => (params.data ? true : false),
      cellRenderer: "agGroupCellRenderer",
      cellRendererParams: {
        suppressCount: true,
        getRowNodeId: (params: any) => (params.data ? params.data.model : null),
        checkbox: true,
      },
      headerCheckboxSelection: leftRowData?.length > 0, // Show checkbox only if grid has data
    };
  }, [leftRowData]);

  const autoGroupColumnDefright = useMemo(() => {
    return {
      headerName: "Grid Type",
      field: "model",
      cellRenderer: "agGroupCellRenderer",
      cellRendererParams: {
        suppressCount: true,
        getRowNodeId: (params: any) => (params.data ? params.data.model : null),
        checkbox: (params: any) =>
          statusFlag && leftRowData && leftRowData.length === 0
            ? false
            : params.node.field === "gridType" ||
              (params.node.field !== "gridType" &&
                params.value === "No data available")
            ? false
            : true,
      },
    };
  }, [statusFlag, leftRowData]);

  const MoveGridType = () => {
    if (operation === "move") {
      const pValData = selectedItems.map((item: any) =>
        generatePValData(item, moveGridType, "update")
      );
      const pValue = JSON.stringify(pValData);
      rightApi.showLoadingOverlay();
      updateModelMapping(pValue, false, "", "update");
    } else if (operation === "delete") {
      const pValData = selectedItems.map((item: any) =>
        generatePValData(item, moveGridType, "unmap")
      );
      const pValue = JSON.stringify(pValData);
      rightApi.showLoadingOverlay();
      updateModelMapping(pValue, true);
    }
  };

  const handleRadioChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    event.target.value === "delete" && setMoveGridType("");
    setOperation(event.target.value);
  };
  const handleSubmit = () => {
    handleSubmitStatus(record.storeId, record.tenantId, "model_mapping");
  };

  const handleSubmitStatus = (
    inStoreId: string,
    inTenantId: string,
    inType: string
  ) => {
    UpdateSubmissionStatus(inStoreId, inTenantId, inType).then((res) => {
      console.log("response>>>>>>>", res, inStoreId, inTenantId, inType);
      setOpenSnackbar(true);
      setStatusMessage(res.msg);
      setStatusMessageType("success");
      setStatusflag(true);
    });
  };

  return {
    defaultColDefLeft,
    leftColumns,
    rightColumns,
    defaultColDefRight,
    autoGroupColumnDef,
    autoGroupColumnDefright,
    onGridReady,
    fetchGridModels,
    openSnackbar,
    statusMessage,
    leftApi,
    rightApi,
    onDragStop,
    leftRowData,
    rightRowData,
    handleSelectionChanged,
    setOpenSnackbar,
    statusMessageType,
    gridTypes,
    gridTypesDefaultList,
    operation,
    setOperation,
    handleRadioChange,
    selectedItems,
    moveGridType,
    setMoveGridType,
    gridTypesDropdown,
    selectedGrid,
    setSelectedGrid,
    MoveGridType,
    handleSubmit,
    statusFlag,
    setStatusflag,
  };
};

export default ModelsDefs;
