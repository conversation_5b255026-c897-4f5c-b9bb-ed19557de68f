import { But<PERSON>, Cir<PERSON>Progress, I<PERSON><PERSON><PERSON>on, Toolt<PERSON> } from "@mui/material";
import { useState } from "react";
import { Confirm, useRecordContext } from "react-admin";
import DeleteIcon from "@mui/icons-material/Delete";
import { traceSpan } from "../../utils/OTTTracing";
const DeletePartsButton: React.FC<any> = (props) => {
  const {
    createOrUpdatePartsMatrix,
    RowData,
    removeLastRow,
    removeRow,
    fetchPartsMatrixRowDetails,
    rowIndex
  } = props;

  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const record: any = useRecordContext();

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    traceSpan(`click_${rowIndex===''?'partsmatrixrowremove' : 'partsmatrixdelete_button'}`, {
      event: `click_${rowIndex===''?'partsmatrixrowremove' : 'partsmatrixdelete_button'}`,
      pageUrl: window.location.pathname,
      timestamp: new Date().toISOString(),
      userId: localStorage.getItem('user') || '',
      value: rowIndex
    });
    event.preventDefault();
    event.stopPropagation();
    setOpen(true);
    if(!removeRow){
      fetchPartsMatrixRowDetails(RowData);
    }
  };

  const handleDialogClose = () => {
    traceSpan(`click_partsmatrixdeletecancel_button`, {
      event: `click_partsmatrixdeletecancel_button`,
      pageUrl: window.location.pathname,
      timestamp: new Date().toISOString(),
      userId: localStorage.getItem('user') || '',
      value: rowIndex
    });
    setOpen(false);}

  const handleConfirm = () => {
    traceSpan(`click_partsmatrixdeleteconfirm_button`, {
      event: `click_partsmatrixdeleteconfirm_button`,
      pageUrl: window.location.pathname,
      timestamp: new Date().toISOString(),
      userId: localStorage.getItem('user') || '',
      value: rowIndex
    });
    setLoading(true);
    const values = {
      installation_date: RowData.storeInstallDate,
      pCreatedDate: RowData.createdDate,
      matrixtype: RowData.matrixType,
      source: RowData.prtsource,
      storeId: record.storeId,
      partsFor: RowData.partsFor,
    };
    const newMatrix = {
      tableData: RowData,
      formData: { ...values },
    };
    createOrUpdatePartsMatrix(newMatrix, "delete").finally(() => {
      setLoading(false);
      setOpen(false);
    });
  };
  const handleremoveRowConfirm = () => {
    removeLastRow();
    setOpen(false);
  };
  return (
    <>
      {!removeRow ? (
        <Tooltip title="Delete">
          <IconButton aria-label="delete" onClick={handleClick}>
            {loading ? <CircularProgress size={24} /> : <DeleteIcon />}
          </IconButton>
        </Tooltip>
      ) : (
        <Button
          style={{
            textTransform: "none",
            fontSize: "12px",
          }}
          onClick={handleClick}
          disabled={RowData?.length <= 1}
        >
          Remove last row
        </Button>
      )}
      <Confirm
        isOpen={open}
        loading={loading}
        title={removeRow ? `Remove Last Row` : `Delete Matrix`}
        content={
          removeRow ? "DIALOG_BOX.ROW_DELETE" : "DIALOG_BOX.MATRIX_DELETE"
        }
        onConfirm={removeRow ? handleremoveRowConfirm : handleConfirm}
        onClose={handleDialogClose}
      />
    </>
  );
};
export default DeletePartsButton;
