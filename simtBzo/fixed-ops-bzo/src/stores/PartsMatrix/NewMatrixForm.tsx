import {
  Box,
  Button,
  Checkbox,
  FormControlLabel,
  IconButton,
  Typography,
} from "@mui/material";
import { makeStyles } from "@material-ui/core/styles";
import {
  FileField,
  FileInput,
  Form,
  FormDataConsumer,
  SaveButton,
  SelectArrayInput,
  SelectInput,
  TextInput,
  Toolbar,
  required,
  useNotify,
  useRecordContext,
} from "react-admin";
import CancelIcon from "@mui/icons-material/Cancel";
import { useEffect, useState } from "react";
import AddMatrixType from "../../components/AddMatrixType";
import AddIcon from "@mui/icons-material/Add";
import RemoveIcon from "@mui/icons-material/Remove";
import { DatePicker, DatePickerProps } from "antd";
import { Constants } from "../../utils/constants";
import dayjs from "dayjs";
import StoreQueries from "../../service/DataFetchQueries/storeQueries";
import { traceSpan } from "../../utils/OTTTracing";

const useStyles = makeStyles((theme: any) => ({
  gridForm: {
    width: "100%",
  },
  inputComp: {
    marginRight: "15px",
    "& .css-1j6tlrj-MuiInputBase-root-MuiFilledInput-root, .css-emoao9-MuiInputBase-root-MuiFilledInput-root-MuiSelect-root, .RaFileInput-dropZone, .css-1m2l69r-MuiInputBase-root-MuiFilledInput-root ":
      {
        backgroundColor: "white",
      },
  },
  toolbarCenter: {
    backgroundColor: "white",
    justifyContent: "center",
  },
  toolbarLeft: {
    backgroundColor: "#eaf1f6",
    justifyContent: "left",
    padding: 0,
  },
}));

const NewMatrixForm = (props: any) => {
  const notify = useNotify();
  const {
    handleSubmitPartsMatrix,
    setAddGrid,
    addGrid,
    component,
    matrixSourceList,
    matrixTypeList,
    fetchPartsMatrixRowData,
    installDateValue,
    setInstallDateValue,
    updateDate,
    setUpdateDate,
    action,
    enableSave,
    cancel,
    rowData,
    setStatusMessage,
    setOpenSnackbar,
    setStatusMessageType,
  } = props;
  const record: any = useRecordContext();
  const { GetOpcodesListQuery } = StoreQueries;
  const [showCustomSourceInput, setShowCustomSourceInput] = useState(false);
  const [customSourceInput, setCustomSourceInput] = useState();
  const [sourceInput, setSourceInput] = useState();
  const [matrixTypeInput, setMatrixTypeInput] = useState();
  const [partsFor, setPartsFor] = useState();
  const [opcode, setOpcode] = useState();
  const [opcodeListChoice, setOpcodeListChoice] = useState<any>([]);
  const [invalidDateMsg, setInvalidDateMsg] = useState("");
  const [checked, setChecked] = useState(false);
  const [fileState, setFileState] = useState(null);

  const classes = useStyles();
  const handleFileChange = (event: any) => {
    if (event) {
      const fileObject: any = {
        rawFile: event,
        src: URL.createObjectURL(event),
        title: event.name,
      };
      setFileState(fileObject);
    }

    if (!event) {
      notify("Invalid file format. Please select a CSV file", {
        type: "error",
        autoHideDuration: 5000,
      });
    }
  };
  useEffect(() => {
    GetOpcodesListQuery(record.tenantId, record.storeId).then((res: any) => {
      // if (res.length !== 0) {
      const opcodeChoice: any[] = res.map((item: any) => ({
        id: item.opcode,
        name: item.opcode,
      }));
      setOpcodeListChoice(opcodeChoice);
      // }
    });
  }, []);
  const validateInput = (value: any) => {
    if (!value) {
      return undefined; // Field is not required
    }

    // Check if value is already in the source list
    const values = value.split(",").map((v: any) => v.trim());
    const isAlreadyInList = values.some((val: any) =>
      matrixSourceList.some((item: any) => item.name === val)
    );
    if (isAlreadyInList) {
      return "Please select from the source list. Item is already in the list.";
    }

    // Check for spaces
    if (value.includes(" ")) {
      return "No spaces allowed";
    }

    // Check for duplicate items (case-insensitive)
    const items = value.split(",");
    const uniqueItems = new Set(items.map((item: any) => item.toLowerCase()));
    if (items.length !== uniqueItems.size) {
      return "No duplicate items allowed";
    }

    // Check for valid format (numbers, alphabets, and commas only)
    const isValid = /^[0-9a-zA-Z,]*$/.test(value);
    if (!isValid) {
      return "Invalid format. Only numbers, alphabets, and commas are allowed";
    }
    return undefined;
  };

  const onDateChange: DatePickerProps["onChange"] = (date, dateString) => {
    traceSpan("select_partmatrixstoreinstalldate_datepicker", {
      event: "select_partmatrixstoreinstalldate_datepicker",
      pageUrl: window.location.pathname,
      timestamp: new Date().toISOString(),
      userId: localStorage.getItem("user") || "",
      value: dateString,
    });

    setInstallDateValue(date);
    setUpdateDate(true);
    Number(dayjs(date).format("YYYY")) > 2100 ||
    Number(dayjs(date).format("YYYY")) < 2000
      ? setInvalidDateMsg("Enter a valid date")
      : setInvalidDateMsg("");
  };

  const handleSubmit = (values: any) => {
    traceSpan(`click_newmatrixsubmit_button`, {
      event: `click_newmatrixsubmit_button`,
      pageUrl: window.location.pathname,
      timestamp: new Date().toISOString(),
      userId: localStorage.getItem("user") || "",
    });
    const submitValue =
      component === "partsMatrix" && values?.partsFor === "opcode"
        ? { ...values, source: ["All"] }
        : values;
    installDateValue &&
      !invalidDateMsg &&
      handleSubmitPartsMatrix(submitValue, addGrid);
  };

  const handleAllSourcesSelection = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setChecked(event.target.checked);
  };
  const compareSourceArrays = (arr1: any[], arr2: any[]): boolean => {
    if (arr1.length !== arr2.length) return false;
    const sortedArr1 = [...arr1].sort();
    const sortedArr2 = [...arr2].sort();
    for (let i = 0; i < sortedArr1.length; i++) {
      if (sortedArr1[i] !== sortedArr2[i]) return false;
    }
    return true;
  };

  const handleSourceSelectInput = (e: any) => {
    let allSourceList = matrixSourceList.map((item: any) => item.name);
    let isAllSelected = compareSourceArrays(e.target.value, allSourceList);
    setChecked(isAllSelected);
    setSourceInput(e.target.value);
  };

  return (
    <Box
      width={
        addGrid === "matrix"
          ? 350
          : component === "partsMatrix" && addGrid == "upload"
          ? "80%"
          : "inherit"
      }
      height={"auto"}
      display={"flex"}
      margin={"4px 24px"}>
      <Form
        onSubmit={handleSubmit}
        className={classes.gridForm}
        defaultValues={
          checked
            ? {
                opcode: opcode && opcode,
                csvFile: fileState && fileState,
                partsFor: partsFor && partsFor,
                matrixtype: matrixTypeInput && matrixTypeInput,
                source: matrixSourceList.map((item: any) => item.name),
                customsource: customSourceInput && customSourceInput,
              }
            : !checked && action !== Constants.actions.edit
            ? {
                opcode: opcode && opcode,
                csvFile: fileState && fileState,
                partsFor: partsFor && partsFor,
                matrixtype: matrixTypeInput && matrixTypeInput,
                source: sourceInput && sourceInput,
                customsource: customSourceInput && customSourceInput,
              }
            : action === Constants.actions.edit
            ? {
                source: rowData[0].prtsource,
              }
            : {}
        }>
        <Box>
          <Box
            display={"flex"}
            flexDirection={addGrid === "matrix" ? "column" : "row"}
            alignItems={addGrid === "matrix" ? "" : "center"}
            width={"100%"}>
            <div
              style={{
                display: "flex",
                alignItems: "flex-start",
                flexDirection: addGrid === "matrix" ? "column" : "row",
              }}>
              {action !== "edit" && (
                <SelectInput
                  helperText={addGrid === "matrix" && false}
                  sx={{
                    mr: 2,
                    width: addGrid === "matrix" ? "100%" : 250,
                    mt: "17px",
                  }}
                  source="partsFor"
                  label="Matrix Type"
                  fullWidth
                  choices={[
                    { id: "opcode", name: "Opcode" },
                    { id: "source", name: "Source Matrix" },
                  ]}
                  validate={required()}
                  onChange={(e) => {
                    traceSpan("matrixtype_dropdown_changed", {
                      event: "matrixtype_dropdown_changed",
                      pageUrl: window.location.pathname,
                      timestamp: new Date().toISOString(),
                      userId: localStorage.getItem("user") || "",
                      value: e.target.value,
                    });
                    setPartsFor(e.target.value);
                  }}
                />
              )}
              {action !== "edit" && (
                <Box
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    width: addGrid === "matrix" ? "100%" : 250,
                  }}>
                  <Typography
                    sx={{
                      color:
                        (!installDateValue && updateDate) || invalidDateMsg
                          ? "#d32f2f"
                          : "grey",
                      fontSize: "12px",
                    }}>
                    Store Install Date*
                  </Typography>
                  <DatePicker
                    size={"large"}
                    onChange={onDateChange}
                    format={"MM/DD/YYYY"}
                    value={installDateValue}
                    variant="filled"
                    style={{
                      width: "100%",
                      border: "none",
                      borderBottom:
                        (!installDateValue && updateDate) || invalidDateMsg
                          ? "1px solid #d32f2f"
                          : "1px solid #958a8a",
                      borderRadius: 0,
                      padding: "11px",
                      marginTop: "0px",
                      marginBottom:
                        (addGrid === "upload" &&
                          !installDateValue &&
                          updateDate) ||
                        invalidDateMsg
                          ? "0px"
                          : addGrid === "upload"
                          ? "18px"
                          : "4px",
                    }}
                  />
                  {!installDateValue && updateDate && (
                    <p
                      style={{
                        fontSize: 12,
                        color: "#d32f2f",
                        marginLeft: "4px",
                        margin: "4px 16px",
                      }}>
                      Required
                    </p>
                  )}
                  {invalidDateMsg && (
                    <p
                      style={{
                        fontSize: 12,
                        color: "#d32f2f",
                        marginLeft: "4px",
                        margin: addGrid === "upload" ? "0px" : "4px 16px",
                      }}>
                      {invalidDateMsg}
                    </p>
                  )}
                </Box>
              )}
            </div>
          </Box>
          <Box
            display={"flex"}
            flexDirection={addGrid === "matrix" ? "column" : "row"}
            alignItems={addGrid === "matrix" ? "" : "center"}
            width={"100%"}>
            <div
              style={{
                display: "flex",
                alignItems: "flex-start",
                flexDirection: addGrid === "matrix" ? "column" : "row",
              }}>
              <FormDataConsumer<{ partsFor: string }>>
                {({ formData, ...rest }) =>
                  formData.partsFor && formData.partsFor === "opcode" ? (
                    <SelectInput
                      source="opcode"
                      label="Opcodes"
                      sx={{
                        width: addGrid === "matrix" ? "100%" : 250,
                        mr: 2,
                      }}
                      fullWidth
                      choices={opcodeListChoice}
                      validate={required()}
                      onChange={(e) => {
                        traceSpan("opcodes_dropdown_changed", {
                          event: "opcodes_dropdown_changed",
                          pageUrl: window.location.pathname,
                          timestamp: new Date().toISOString(),
                          userId: localStorage.getItem("user") || "",
                          value: e.target.value,
                        });
                        setOpcode(e.target.value);
                      }}
                    />
                  ) : formData?.partsFor === "source" ? (
                    <Box
                      sx={{
                        display: "flex",
                        width: addGrid === "matrix" ? "100%" : 250,
                        mr: 2,
                        flexDirection: "row",
                      }}>
                      <SelectInput
                        sx={{ mb: 0 }}
                        helperText={addGrid === "matrix" && false}
                        source="matrixtype"
                        label="Matrix Name"
                        fullWidth
                        choices={matrixTypeList}
                        validate={required()}
                        onChange={(e) => {
                          setMatrixTypeInput(e.target.value);
                        }}
                      />
                      <AddMatrixType
                        fetchPartsMatrixRowData={fetchPartsMatrixRowData}
                        setStatusMessage={setStatusMessage}
                        setOpenSnackbar={setOpenSnackbar}
                        setStatusMessageType={setStatusMessageType}
                      />
                    </Box>
                  ) : (
                    <></>
                  )
                }
              </FormDataConsumer>
              <FormDataConsumer<{ partsFor: string }>>
                {({ formData, ...rest }) =>
                  ((formData.partsFor && formData.partsFor === "source") ||
                    (action === "edit" && rowData[0].partsFor !== "opcode")) &&
                  record.dms !== "Reynolds" &&
                  record.dms !== "Tekion" && (
                    <Box
                      sx={{
                        width: addGrid === "matrix" ? "100%" : 250,
                        display: "flex",
                      }}>
                      <Box sx={{ width: "100%" }}>
                        <SelectArrayInput
                          helperText={false}
                          style={{
                            maxHeight: 80,
                            overflowX: "auto",
                            whiteSpace: "nowrap",
                          }}
                          source="source"
                          fullWidth
                          onChange={handleSourceSelectInput}
                          choices={matrixSourceList}
                          defaultValue={checked && matrixSourceList}
                          validate={(value, allValues) => {
                            if (
                              record.dms !== "Reynolds" &&
                              record.dms !== "Tekion" &&
                              (!allValues?.customsource ||
                                allValues.customsource === "") &&
                              (!value || Object.keys(value).length === 0)
                            ) {
                              return "Required";
                            }
                            return undefined;
                          }}
                        />
                      </Box>
                      <IconButton
                        style={{ height: "fit-content", marginTop: "14px" }}
                        onClick={() =>
                          setShowCustomSourceInput((prev) => !prev)
                        }>
                        {showCustomSourceInput ? <RemoveIcon /> : <AddIcon />}
                      </IconButton>
                    </Box>
                  )
                }
              </FormDataConsumer>
              {showCustomSourceInput && (
                <div
                  style={{
                    display: "flex",
                    alignItems: "flex-start",
                  }}>
                  <TextInput
                    source="customsource"
                    label="Add Custom Source"
                    autoFocus
                    margin="dense"
                    id="customsource"
                    name="customsource"
                    variant="standard"
                    validate={[validateInput]}
                    resettable={true}
                    onChange={(e) => {
                      e?.target?.value && setCustomSourceInput(e.target.value);
                    }}
                  />
                </div>
              )}
            </div>
          </Box>
          <Box
            sx={{
              display: "flex",
              width: 520,
            }}>
            {/*------------------------File Upload input---------------------*/}
            {addGrid === "upload" && (
              <FileInput
                validate={[required()]}
                source="csvFile"
                label={"Upload csv file"}
                accept=".csv, text/csv" // Restrict file selection to CSV files
                onChange={handleFileChange}>
                <FileField source="src" title="title" />
              </FileInput>
            )}
          </Box>
        </Box>
        <Toolbar
          className={
            addGrid === "grid" || addGrid === "matrix"
              ? classes.toolbarCenter
              : classes.toolbarLeft
          }>
          {action === Constants.actions.edit && component === "partsMatrix" ? (
            <>
              <SaveButton label="UPDATE" alwaysEnable />

              <Button
                disabled={enableSave ? false : true}
                onClick={() => {
                  cancel();
                }}
                color="primary"
                variant="contained"
                startIcon={<CancelIcon />}
                sx={{ m: 2 }}>
                CANCEL
              </Button>
            </>
          ) : (
            <>
              <SaveButton
                label="Submit"
                onClick={() => setUpdateDate(true)}
                alwaysEnable
              />
              <Button
                color="primary"
                variant="contained"
                startIcon={<CancelIcon />}
                sx={{ m: 2 }}
                onClick={() => {
                  traceSpan(`click_newmatrixcancel_button`, {
                    event: `click_newmatrixcancel_button`,
                    pageUrl: window.location.pathname,
                    timestamp: new Date().toISOString(),
                    userId: localStorage.getItem("user") || "",
                  });
                  setAddGrid("");
                }}>
                CANCEL
              </Button>
            </>
          )}
        </Toolbar>
      </Form>
    </Box>
  );
};

export default NewMatrixForm;
