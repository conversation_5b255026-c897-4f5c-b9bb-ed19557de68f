import React from "react";
import {
  Box,
  Modal,
  IconButton,
  FormControl,
  FormLabel,
  Typography,
} from "@mui/material";
import HighlightOffIcon from "@material-ui/icons/HighlightOff";
import CircleIcon from "@mui/icons-material/Circle";
import AddMatrixType from "../../components/AddMatrixType";
import { Constants } from "../../utils/constants";

const MatrixNameModal = (props: any) => {
  const {
    setOpenMatrixNameModal,
    openMatrixNameModal,
    setMatrixNameChanged,
    matrixType,
    getMatrixTypesList,
    setStatusMessage,
    setOpenSnackbar,
    setStatusMessageType,
    fetchPartsMatrixRowData,
  } = props;

  return (
    <Modal
      open={openMatrixNameModal}
      onClose={() => setOpenMatrixNameModal(false)}
      aria-labelledby="modal-modal-title"
      aria-describedby="modal-modal-description"
    >
      <Box
        sx={{
          display: "flex",
          position: "absolute" as "absolute",
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)",
          width: "20%",
          bgcolor: "background.paper",
          border: "1px solid #6a6a6a",
          boxShadow: 24,
          p: "16px 24px",
          borderRadius: "10px",
        }}
      >
        <FormControl sx={{ width: "100%", m: 0 }}>
          <FormLabel id="demo-controlled-radio-buttons-group">
            <Box sx={{ display: "flex" }}>
              <Typography
                id="modal-modal-title"
                variant="h6"
                color="#003d6b"
                sx={{ fontSize: "14px", fontWeight: "bold" }}
              >
                All Source Matrix Names
              </Typography>
              <IconButton
                onClick={() => {
                  setMatrixNameChanged(false);
                  setOpenMatrixNameModal(false);
                }}
                sx={{
                  position: "absolute",
                  //   top: "5px",
                  right: "5px",
                  color: "#003d6b",
                  padding: 0,
                  opacity: 0.5,
                  //   marginBottom: "2px"
                  //   marginTop: "3px",
                }}
              >
                <HighlightOffIcon style={{ fontSize: 20, color: "#003d6b" }} />
              </IconButton>
            </Box>
          </FormLabel>
          {matrixType.length > 0 ? (
            matrixType.map((matrixName: any) => {
              return (
                <Box
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    flexWrap: "wrap",
                    justifyContent: "space-between",
                  }}
                >
                  <Typography
                    sx={{
                      mt: "8px",
                      display: "flex",
                      alignItems: "center",
                      fontSize: "12px",
                    }}
                  >
                    <CircleIcon
                      sx={{
                        fontSize: "6px !important",
                        color: "#003d6b",
                        mr: "8px",
                      }}
                    ></CircleIcon>
                    {matrixName.name}
                  </Typography>
                  <AddMatrixType
                    getMatrixTypesList={getMatrixTypesList}
                    fetchPartsMatrixRowData={fetchPartsMatrixRowData}
                    isEdit={true}
                    matrixName={matrixName.name}
                    setStatusMessage={setStatusMessage}
                    setOpenSnackbar={setOpenSnackbar}
                    setStatusMessageType={setStatusMessageType}
                  />
                </Box>
              );
            })
          ) : (
            // if there is no grid type created yet
            <>
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "center",
                  mt: 2,
                  height: "100px",
                  border: `1px solid ${Constants.colors.greyBorder}`,
                  borderRadius: "5px",
                  alignItems: "center",
                }}
              >
                <Typography
                  sx={{ color: Constants.colors.greyText, fontSize: "12px" }}
                >
                  No matrix names to list.{" "}
                </Typography>
              </Box>
              <AddMatrixType
                getMatrixTypesList={getMatrixTypesList}
                fetchPartsMatrixRowData={fetchPartsMatrixRowData}
                isMatrixModal={true}
                isEdit={false}
                setStatusMessage={setStatusMessage}
                setOpenSnackbar={setOpenSnackbar}
                setStatusMessageType={setStatusMessageType}
              />
            </>
          )}
        </FormControl>
      </Box>
    </Modal>
  );
};

export default MatrixNameModal;
