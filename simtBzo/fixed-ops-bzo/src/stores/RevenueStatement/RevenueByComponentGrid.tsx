import React, { useEffect, useState } from "react";
import { makeStyles, withStyles } from "@material-ui/styles";
import RevenueStatementGrid from "./RevenueStatementGrid";
import SummaryDataGrid from "./SummaryDataGrid";
import SummaryDataHeader from "./SummaryDataHeader";
import {
  getRevenueSummaryLaborRevenueByComponent,
  getRevenueSummaryPartsRevenueByComponent,
} from "../../service/dataFetchQueries";
import { TableCell, TableRow } from "@mui/material";

const StyledTableRow = withStyles((theme) => ({
  root: {
    "&:nth-of-type(odd)": {
      //color: '#ccc !important'
    },
  },
}))(TableRow);

const StyledTableCellSubHeader = withStyles((theme) => ({
  head: {
    border: "1px solid #100101 !important",
    lineHeight: "1.0rem",
    backgroundColor: "rgb(209, 209, 209)",
    fontWeight: "bold",
    padding: 10,
  },
  body: {
    fontSize: 12,
    padding: 10,
    backgroundColor: "rgb(209, 209, 209)",
    fontWeight: "bold",
  },
}))(TableCell);

const StyledTableCell = withStyles((theme) => ({
  head: {
    color: "#100101 !important",
    border: "1px solid #ccc !important",
    lineHeight: "0.2rem",
    padding: 10,
  },
  body: {
    fontSize: 12,
    padding: 10,
    border: "1px solid #ccc !important",
  },
}))(TableCell);

const RevenueByComponentGrid: React.FC<any> = ({
  currMonth,
  optMonth,
  department,
  componentType,
}) => {
  const [summaryDataAll, setSummaryDataAll] = useState<any[]>([]);
  const [summaryData, setSummaryData] = useState<any[]>([]);
  const [chartedSubTotalData, setChartedSubTotalData] = useState<any[]>([]);
  const [summaryDataParts, setSummaryDataParts] = useState<any[]>([]);
  
  useEffect(() => {
    const fetchData = async () => {
      const result = await getRevenueSummaryLaborRevenueByComponent();
      if (result) {
        const roData = result;

        const noFiltersData = roData.filter(
          (o: any) =>
            o.filterBy === componentType &&
            o.heading.toLowerCase() !== "customer labor revenue" &&
            o.heading.trim().toLowerCase() !== "effective labor rate inputs" &&
            o.heading.trim().toLowerCase() !== "labor gross profit inputs" &&
            o.heading.toLowerCase() !== "labor gross profit per hour inputs"
        );

        const subTotalsData = roData.filter(
          (o: any) =>
            o.filterBy === componentType &&
            (o.heading.toLowerCase() === "customer labor revenue" ||
              o.heading.trim().toLowerCase() ===
                "effective labor rate inputs" ||
              o.heading.trim().toLowerCase() === "labor gross profit inputs" ||
              o.heading.toLowerCase() === "labor gross profit per hour inputs")
        );

        setChartedSubTotalData(subTotalsData);
        setSummaryData(noFiltersData);
      }
    };

    fetchData();
  }, [componentType, optMonth, department, currMonth]);

  useEffect(() => {
    const fetchData = async () => {
      const result = await getRevenueSummaryPartsRevenueByComponent();

      if (result) {
        setSummaryDataParts(result);
      }
    };

    fetchData();
  }, [optMonth, department]);

  return (
    <>
      {summaryData.length > 0 && (
        <SummaryDataGrid
          summaryData={summaryData}
          isChartSubTotal={false}
          type="all"
        />
      )}
      <StyledTableRow>
        <StyledTableCell colSpan={5}>&nbsp;</StyledTableCell>
        <StyledTableCellSubHeader>Charted Sub-Totals</StyledTableCellSubHeader>
        <StyledTableCell colSpan={5}>&nbsp;</StyledTableCell>
      </StyledTableRow>
      {chartedSubTotalData.length > 0 && (
        <SummaryDataGrid
          summaryData={chartedSubTotalData}
          isChartSubTotal={true}
          type="all"
        />
      )}
      <StyledTableRow>
        <StyledTableCell colSpan={5}>&nbsp;</StyledTableCell>
        <StyledTableCellSubHeader>&nbsp;</StyledTableCellSubHeader>
        <StyledTableCell colSpan={5}>&nbsp;</StyledTableCell>
      </StyledTableRow>
      <SummaryDataHeader
        type="PartsRevenueByComponent"
        //changeRevenueComponentType={changeRevenueComponentType}
      />
      {summaryDataParts.length > 0 && (
        <SummaryDataGrid
          summaryData={summaryDataParts}
          isChartSubTotal={false}
          type="all"
        />
      )}
    </>
  );
};

export default RevenueByComponentGrid;
