import React, { useEffect, useState } from "react";
import { withStyles } from "@material-ui/styles";
import SummaryDataGrid from "./SummaryDataGrid";
import SummaryDataHeader from "./SummaryDataHeader";
import {
  getRevenueSummaryLaborRevenueByCategory,
  getRevenueSummaryPartsRevenueByCategory,
} from "../../service/dataFetchQueries";
import { TableCell, TableRow } from "@mui/material";

const StyledTableRow = withStyles(() => ({
  root: {
    "&:nth-of-type(odd)": {
      backgroundColor: "#f9f9f9",
    },
    padding: 10,
  },
}))(TableRow);

const StyledTableCellHeader = withStyles(() => ({
  head: {
    color: "#fff",
    border: "1px solid #100101",
    lineHeight: "1.0rem",
    backgroundColor: "#c2185b",
  },
  body: {
    fontSize: 14,
  },
}))(TableCell);

const StyledTableCellSubHeader = withStyles(() => ({
  head: {
    border: "1px solid #100101",
    lineHeight: "1.0rem",
    backgroundColor: "rgb(209, 209, 209)",
    fontWeight: "bold",
    padding: 10,
  },
  body: {
    fontSize: 12,
    padding: 10,
    backgroundColor: "rgb(209, 209, 209)",
    fontWeight: "bold",
  },
}))(TableCell);

const StyledTableCell = withStyles(() => ({
  head: {
    color: "#100101",
    border: "1px solid #ccc",
    lineHeight: "0.2rem",
    padding: 10,
  },
  body: {
    fontSize: 12,
    padding: 10,
    border: "1px solid #ccc",
  },
}))(TableCell);

const RevenueByCategoryGrid: React.FC<any> = (props) => {
  const [summaryDataLabor, setSummaryDataLabor] = useState<any[]>([]);
  const [summaryDataParts, setSummaryDataParts] = useState<any[]>([]);

  useEffect(() => {
    const fetchLaborRevenue = async () => {
      try {
        const result = await getRevenueSummaryLaborRevenueByCategory();
        if (result) {
          const sorted = result.sort((a: any, b: any) =>
            a.heading.localeCompare(b.heading)
          );
          sorted.unshift({
            id: "720",
            elrRev: "  ",
            gpPercLbrRev: "  ",
            heading: "Repair",
            lbrRevenue: "  ",
            optElrRev: "  ",
            optGpPercLbrRev: "  ",
            optLbrRevenue: "  ",
            storeId: "217486972",
          });
          setSummaryDataLabor(sorted);
        }
      } catch (error) {
        console.error("Error fetching labor revenue by category:", error);
      }
    };

    const fetchPartsRevenue = async () => {
      try {
        const result = await getRevenueSummaryPartsRevenueByCategory();
        if (result) {
          const sorted = result.sort((a: any, b: any) =>
            a.heading.localeCompare(b.heading)
          );
          sorted.unshift({
            selCost: "  ",
            selSale: "  ",
            optSelSale: "  ",
            optSelCost: "  ",
            heading: "Repair",
            id: "700",
          });
          setSummaryDataParts(sorted);
        }
      } catch (error) {
        console.error("Error fetching parts revenue by category:", error);
      }
    };

    fetchLaborRevenue();
    fetchPartsRevenue();
  }, [props.optMonth, props.department]);

  return (
    <>
      <SummaryDataHeader type="laborRevenueByCategory" />
      {summaryDataLabor.length > 0 && (
        <SummaryDataGrid
          summaryData={summaryDataLabor}
          isChartSubTotal={false}
          type="laborRevenueByCategory"
        />
      )}
      <StyledTableRow>
        <StyledTableCell colSpan={5}>&nbsp;</StyledTableCell>
        <StyledTableCellSubHeader>&nbsp;</StyledTableCellSubHeader>
        <StyledTableCell colSpan={5}>&nbsp;</StyledTableCell>
      </StyledTableRow>
      <SummaryDataHeader type="partsRevenueByCategory" />
      {summaryDataParts.length > 0 && (
        <SummaryDataGrid
          summaryData={summaryDataParts}
          isChartSubTotal={false}
          type="partsRevenueByCategory"
        />
      )}
    </>
  );
};

export default RevenueByCategoryGrid;
