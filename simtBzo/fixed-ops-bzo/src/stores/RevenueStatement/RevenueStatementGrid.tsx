import React, { useEffect, useState } from "react";
import { DatePicker, DatePickerProps } from "antd";
import { withStyles, makeStyles } from  "@material-ui/core/styles";
import {
  Grid,
  Paper,
  Tabs,
  Tab,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  Tooltip,
}  from "@mui/material";
import moment from "moment";
import { getRevenueSummaryRevenueByPaytype } from "../../service/dataFetchQueries";
import SummaryDataHeader from "./SummaryDataHeader";
import SummaryDataGrid from "./SummaryDataGrid";
import OpenInNewOutlinedIcon from "@mui/icons-material/OpenInNewOutlined";
import RevenueByCategoryGrid from "./RevenueByCategoryGrid";
import RevenueByComponentGrid from "./RevenueByComponentGrid";
import JobLevelBreakDownGrid from "./JobLevelBreakDownGrid";
import RevenueStatement from "./RevenueStatement";
import { useNavigate } from "react-router-dom";
import { PageRoutes } from "../../utils/pageRoutes";
import { useRecordContext } from "react-admin";
import { traceSpan } from "../../utils/OTTTracing";
const Dealer = process.env.REACT_APP_DEALER || "";

const useStyles = makeStyles((theme) => ({
  root: {
    paddingTop: theme.spacing(3),
    paddingBottom: theme.spacing(3),
  },
  paper: {
    margin: 8,
    padding: 20,
  },
  table: {
    border: "1px solid #d8d8d8",
  },
  titleCell: {
    minWidth: "50px",
    padding: 10,
  },
  headerCell: {
    color: "#fff !important",
    lineHeight: "1.0rem",
    backgroundColor:
      Dealer === "Armatus" ? "#003d6b !important" : "#c2185b !important",
  },
  subHeaderCell: {
    border: "1px solid #d1d1d1 !important",
    lineHeight: "1.0rem",
    backgroundColor: "rgb(209, 209, 209)",
    fontWeight: "bold",
    padding: 10,
  },
  bodyCell: {
    fontSize: 12,
    padding: 10,
    backgroundColor: "rgb(209, 209, 209)",
    fontWeight: "bold",
  },
  oddRow: {
    padding: 10,
  },
}));

const RevenueStatementGrid: React.FC<any> = (props: any) => {
  const record = useRecordContext();
  const classes = useStyles();
  const { queryMonth, optQueryMonthYear }: any = props;
  const [summaryDataNoFilters, setSummaryDataNoFilters] = useState<any[]>([]);
  const [summaryData, setSummaryData] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [optQueryMonth, setOptQueryMonth] = useState<any>(optQueryMonthYear);
  const [componentType, setComponentType] = useState<any>("all");
  const [department, setDepartment] = useState<any>("service");
  const navigate = useNavigate();
  const changeRevenueComponentType = (params: any) => {
    setComponentType(params);
  };
  
  useEffect(() => {
    GetRevenueSummaryRevenueByPaytype();
  }, []);

  const GetRevenueSummaryRevenueByPaytype = async () => {
    try {
      const res = await getRevenueSummaryRevenueByPaytype();
      if (res) {
        const roData: any[] = res;
        const FiltersData = roData.filter(
          (o) => o.heading !== "revenue - no filters - labor and parts"
        );
        const sorted = FiltersData.sort((a, b) => a.id - b.id);
        setSummaryData(sorted);
        const noFiltersData = roData.filter(
          (o) => o.heading === "revenue - no filters - labor and parts"
        );
        setSummaryDataNoFilters(noFiltersData);
      }
    } catch (error) {
      console.error("Error fetching menu model data:", error);
      throw error;
    }
  };

  return (
    <div>
      <Paper square className={classes.paper}>
        <Grid xs={12} id="showTable">
          <Table className={classes.table}>
            <TableHead>
              <TableRow>
                <TableCell className={classes.titleCell} colSpan={5}>
                  Current Month - {moment(queryMonth).format("MMM-YY")}
                </TableCell>
                <TableCell className={classes.subHeaderCell}>
                  Service Department <br></br>
                </TableCell>
                <TableCell className={classes.titleCell} colSpan={5}>
                  <DatePicker
                  onChange={(date, dateString) => {
                                    traceSpan('select_revenudetail_datepicker', {
                                      event: 'select_revenudetail_datepicker',
                                      pageUrl: window.location.pathname,
                                      timestamp: new Date().toISOString(),
                                      userId:
                                        localStorage.getItem('user') || '',
                                      value: dateString
                                    });
                                  }}
                  onFocus={() => {
                                    traceSpan('revenudetail_datepicker_focused', {
                                      event: 'revenudetail_datepicker_focused',
                                      pageUrl: window.location.pathname,
                                      timestamp: new Date().toISOString(),
                                      userId:
                                        localStorage.getItem('user') || ''
                                    });
                                  }}
                  />
                </TableCell>
              </TableRow>
              <SummaryDataHeader type={"all"} />
            </TableHead>
            <TableBody>
              {summaryDataNoFilters.length > 0 && (
                <SummaryDataGrid
                  summaryData={summaryDataNoFilters}
                  isFirstRow={true}
                  type="all"
                  currMonth={queryMonth}
                  optMonth={optQueryMonth}
                  // department="service"
                />
              )}
              <TableRow>
                <TableCell colSpan={5}>&nbsp;</TableCell>
                <TableCell className={classes.subHeaderCell}>&nbsp;</TableCell>
                <TableCell colSpan={5}>&nbsp;</TableCell>
              </TableRow>

              <TableRow>
                <TableCell colSpan={5}></TableCell>
                <TableCell className={classes.subHeaderCell}>
                  Revenue By Pay Type
                  <span>
                    <a
                      className="viewDetail"
                      id="laborparts"
                      style={{ cursor: "pointer" }}
                    >
                      <Tooltip title="Drill Down">
                        <img
                          // onClick={() =>
                          //   navigate(
                          //     PageRoutes.getRevenueSummaryRoute(record.id)
                          //   )
                          // }
                          style={{
                            marginLeft: "5px",
                            width: "17px",
                            height: "17px",
                            float: "right",
                          }}
                          src="/images/Icons/external.png"
                        />
                      </Tooltip>
                    </a>
                  </span>
                </TableCell>
                <TableCell colSpan={5}></TableCell>
              </TableRow>
              {summaryData.length > 0 && (
                <SummaryDataGrid
                  summaryData={summaryData}
                  type="all"
                  currMonth={queryMonth}
                  optMonth={optQueryMonth}
                  department={"service"}
                />
              )}
            </TableBody>

            <TableBody>
              <TableRow>
                <TableCell colSpan={5}>&nbsp;</TableCell>
                <TableCell className={classes.subHeaderCell}>&nbsp;</TableCell>
                <TableCell colSpan={5}>&nbsp;</TableCell>
              </TableRow>
              <SummaryDataHeader
                type="laborRevenueByComponent"
                changeRevenueComponentType={changeRevenueComponentType}
              />
              <RevenueByComponentGrid
                currMonth={queryMonth}
                componentType={componentType}
                optMonth={optQueryMonth}
                department={department}
              />
            </TableBody>

            <TableBody>
              <TableRow>
                <TableCell colSpan={5}>&nbsp;</TableCell>
                <TableCell className={classes.subHeaderCell}>&nbsp;</TableCell>
                <TableCell colSpan={5}>&nbsp;</TableCell>
              </TableRow>

              <RevenueByCategoryGrid
                currMonth={queryMonth}
                componentType={componentType}
                optMonth={optQueryMonth}
                department={department}
              />
            </TableBody>

            <TableBody>
              <TableRow>
                <TableCell colSpan={5}>&nbsp;</TableCell>
                <TableCell className={classes.subHeaderCell}>&nbsp;</TableCell>
                <TableCell colSpan={5}>&nbsp;</TableCell>
              </TableRow>

              <JobLevelBreakDownGrid
                currMonth={queryMonth}
                componentType={componentType}
                optMonth={optQueryMonth}
                department={department}
              />
            </TableBody>
          </Table>
        </Grid>
      </Paper>
    </div>
  );
};

export default RevenueStatementGrid;
