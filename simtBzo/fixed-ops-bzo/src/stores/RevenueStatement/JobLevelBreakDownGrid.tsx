import React, { useEffect, useState } from "react";
import { makeStyles, withStyles } from "@material-ui/styles";
import SummaryDataGrid from "./SummaryDataGrid";
import SummaryDataHeader from "./SummaryDataHeader";
import {
  getRevenueSummaryCustomerJobLevelBkdown,
  getRevenueSummaryCustomerJobLevelBkdownPerc,
} from "../../service/dataFetchQueries";
import { TableCell, TableRow } from "@mui/material";

const StyledTableRow = withStyles((theme) => ({
  root: {
    "&:nth-of-type(odd)": {
      //color: '#ccc !important'
    },
    padding: 10,
  },
}))(TableRow);

const StyledTableCellHeader = withStyles((theme) => ({
  head: {
    color: "#fff !important",
    border: "1px solid #100101 !important",
    lineHeight: "1.0rem",
    backgroundColor: "#c2185b !important",
  },
  body: {
    fontSize: 14,
  },
}))(TableCell);

const StyledTableCellSubHeader = withStyles((theme) => ({
  head: {
    border: "1px solid #100101 !important",
    lineHeight: "1.0rem",
    backgroundColor: "rgb(209, 209, 209)",
    fontWeight: "bold",
    padding: 10,
  },
  body: {
    fontSize: 12,
    padding: 10,
    backgroundColor: "rgb(209, 209, 209)",
    fontWeight: "bold",
  },
}))(TableCell);

const StyledTableCell = withStyles((theme) => ({
  head: {
    color: "#100101 !important",
    border: "1px solid #ccc !important",
    lineHeight: "0.2rem",
    padding: 10,
  },
  body: {
    fontSize: 12,
    padding: 10,
    border: "1px solid #ccc !important",
  },
}))(TableCell);

const JobLevelBreakDownGrid: React.FC<any> = (props) => {
  const [summaryDataLabor, setSummaryDataLabor] = useState<any[]>([]);
  const [summaryDataParts, setSummaryDataParts] = useState<any[]>([]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const revenueSummaryLabor =
          await getRevenueSummaryCustomerJobLevelBkdown();

        if (revenueSummaryLabor) {
          const sortedLabor = revenueSummaryLabor.sort(
            (a: any, b: any) => a.id - b.id
          );

          setSummaryDataLabor(sortedLabor);
        }

        const revenueSummaryParts =
          await getRevenueSummaryCustomerJobLevelBkdownPerc();

        if (revenueSummaryParts) {
          const sortedParts = revenueSummaryParts.sort(
            (a: any, b: any) => Number(a.id) - Number(b.id)
          );
          setSummaryDataParts(sortedParts);
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };

    fetchData();
  }, [props.optMonth, props.department]);

  return (
    <>
      {}
      <SummaryDataHeader type="jobLevelBrkDown" />
      {summaryDataLabor.length > 0 && (
        <SummaryDataGrid
          summaryData={summaryDataLabor}
          isChartSubTotal={false}
          type="jobLevelBrkDown"
        />
      )}
      <StyledTableRow>
        <StyledTableCell colSpan={5}>&nbsp;</StyledTableCell>
        <StyledTableCellSubHeader>&nbsp;</StyledTableCellSubHeader>
        <StyledTableCell colSpan={5}>&nbsp;</StyledTableCell>
      </StyledTableRow>
      <SummaryDataHeader type="jobLevelBrkDownPerc" />
      {summaryDataParts.length > 0 && (
        <SummaryDataGrid
          summaryData={summaryDataParts}
          isChartSubTotal={false}
          type="jobLevelBrkDownPerc"
        />
      )}
    </>
  );
};

export default JobLevelBreakDownGrid;
