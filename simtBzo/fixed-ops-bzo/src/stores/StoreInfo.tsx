import * as React from "react";
import { useRecordContext } from "react-admin";
import {
  Box,
  Card,
  Table,
  TableBody,
  TableCell,
  TableRow,
  Typography,
  Link,
} from "@mui/material";
import { traceSpan } from "../utils/OTTTracing";

export const StoreInfo = () => {
  const record = useRecordContext();
  return (
    <>
      <Typography sx={{ mt: 3, ml: 2, fontWeight: "bold" }}>
        Store Info
      </Typography>
      <Box
        width={"35%"}
        sx={{ display: "block", paddingX: "20px", }}
      >
        <Card
          sx={{
            mt: 1,
            border: "2px solid rgb(224, 224, 227)",
            boxShadow: "none",
          }}
        >
          <Table size="small">
            <TableBody>
              <TableRow key={record.id}>
                <TableCell>DMS</TableCell>
                <TableCell>{record.dms}</TableCell>
              </TableRow>
              <TableRow key={record.id}>
                <TableCell>Manufacturer</TableCell>
                <TableCell>{record.manufacturer}</TableCell>
              </TableRow>
              <TableRow key={record.id}>
                <TableCell>Keycloak-Group Name</TableCell>
                <TableCell>{record.groupName}</TableCell>
              </TableRow>
              <TableRow key={record.id}>
                <TableCell>Website</TableCell>
                <TableCell>
                  <Link
                    href={record.website}
                    target="_blank"
                    rel="noopener noreferrer"
                    underline="hover"
                    onClick={() => {
                      traceSpan(`click_website_link`, {
                        event: `click_website_link`,
                        pageUrl: window.location.pathname,
                        timestamp: new Date().toISOString(),
                        userId: localStorage.getItem('user') || '',
                        linkUrl: record.website
                      });
                    }}
                  >
                    {record.website}
                  </Link>
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </Card>
      </Box>
    </>
  );
};
