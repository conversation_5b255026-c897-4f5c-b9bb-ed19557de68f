import { useRecordContext } from "react-admin";
import { useEffect } from "react";
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  Typography,
} from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { AgGridReact } from "ag-grid-react";
import BulkLoadGridDefs from "./BulkLoadGridDefs";
import useOnboarding from "../../CustomHooks/useOnboarding";
import { Constants } from "../../utils/constants";
import { traceSpan } from "../../utils/OTTTracing";

const BulkLoadStatusLog = (props: any) => {
  const { phase, phaseStatus } = props;
  const record = useRecordContext();
  const { getBulkLoadStatuslog, rowData } = useOnboarding({
    record: record,
    phase: phase,
    id: record.id,
  });
  const { columnDefs, defaultColDef, gridData } = BulkLoadGridDefs(rowData);
  const onFilterChanged = (e: any) => {
          const filterValues = e.api.getFilterModel();
          Object.keys(filterValues).forEach((colId) => {
            traceSpan(`filter_grid_${colId}`, {
              event: `filter_grid_${colId}`,
            pageUrl: window.location.pathname,
            timestamp: new Date().toISOString(),
            userId: localStorage.getItem("user") || "",
            column: colId,
            filterValue: JSON.stringify(filterValues[colId])
            });
          });
        };
      const onSortChanged = (params: any) => {
        // Get column states and filter only sorted columns
        const sortModel = params.columnApi.getColumnState()
          .filter((col: any) => col.sort != null)
          .map((col: any) => ({
            colId: col.colId,
            sort: col.sort
          }));
      
        sortModel.forEach((sortItem: { colId: string; sort: 'asc' | 'desc' }) => {
          traceSpan(`sort_grid_${sortItem.colId}`, {
             event: `sort_grid_${sortItem.colId}`,
          pageUrl: window.location.pathname,
          timestamp: new Date().toISOString(),
          userId: localStorage.getItem('user') || '',
          column: sortItem.colId,
          direction: sortItem.sort
          });
        });
      };

  if (phaseStatus === "Active" || phaseStatus === "Inactive") return null;
  return (
    <Accordion
      style={{
        marginBottom: 16,
        backgroundColor: "#eaf1f6",
        border: "solid 2px",
        borderColor: "#003d6b",
      }}
      onChange={(e: React.SyntheticEvent, expanded: boolean) => {
        if (expanded) {
          traceSpan(`opcode_row_expansion`, {
      event: `opcode_row_expansion`,
      pageUrl: window.location.pathname,
      timestamp: new Date().toISOString(),
      userId: localStorage.getItem('user') || '',
      action: expanded ? 'expand' : 'collapse',
      // Note: Accordion doesn't have rowIndex, but you can add other relevant data
      value: phase === "bulk_phase_1" 
        ? "Phase 1" 
        : phase === "bulk_phase_2" 
        ? "Phase 2" 
        : "Daily Load"
    });
          getBulkLoadStatuslog();
        } else {
        }
      }}
    >
      <AccordionSummary
        expandIcon={<ExpandMoreIcon />}
        sx={{ display: "flex" }}
      >
        <Box sx={{ flex: 2 }}>
          <Typography
            sx={{
              fontSize: 14,
              color: "#003d6b",
              fontWeight: "bold",
            }}
          >
            {phase === "bulk_phase_1"
              ? "Phase 1"
              : phase === "bulk_phase_2"
              ? "Phase 2"
              : "Daily Load"}
          </Typography>
        </Box>
      </AccordionSummary>
      <AccordionDetails>
        <div
          className={Constants.ag_grid_theme}
          style={{ height: "85vh", width: "100%" }}
        >
          <AgGridReact
            columnDefs={columnDefs}
            editType="fullRow"
            rowData={gridData}
            defaultColDef={defaultColDef}
            rowSelection="single"
            animateRows={true}
            // @ts-ignore
            //  ref={gridRef}
            singleClickEdit={true}
            suppressColumnVirtualisation={true}
            suppressChangeDetection={true}
            stopEditingWhenCellsLoseFocus={true}
            onFilterChanged={onFilterChanged}
            onSortChanged={onSortChanged}
          />
        </div>
      </AccordionDetails>
    </Accordion>
  );
};

export default BulkLoadStatusLog;
