import React, { useState } from "react";
import { FormControl, TextField } from "@material-ui/core";
import { withStyles } from "@material-ui/styles";
import OutlinedInput from "@material-ui/core/OutlinedInput";
import InputAdornment from "@material-ui/core/InputAdornment";
import DateRangePicker from "react-bootstrap-daterangepicker";
import "bootstrap-daterangepicker/daterangepicker.css";
import clsx from "clsx";
import moment from "moment";
import { TransferWithinAStationOutlined } from "@material-ui/icons";

/*This page not using Now*/ 

const FixedRateRenderer = (props: any) => {
  const [isFixedRateChecked, setIsFixedRateChecked] = useState(false);
  const [isValid, setIsValid] = useState(
    props.data.partsFixedratevalue !== null &&
      props.data.partsFixedratevalue !== "N/A" &&
      (props.data.partsFixedratevalue.split(" ")[0] === "Cost" ||
        props.data.partsFixedratevalue.split(" ")[0] === "List")
  );

  const [partsMarkup, setPartsMarkup] = useState(
    isValid ? props.data.partsFixedratevalue.split(" ")[0] : "Cost"
  );

  const [laborfixedValue, setLaborfixedValue] = useState<any>(
    props.data.laborFixedratevalue
  );

  const [laborfixedDate, setLaborfixedDate] = useState<any>(
    props.data.laborFixedratedate !== null
      ? props.data.laborFixedratedate
      : new Date()
  );

  const [partsfixedValue, setPartsfixedValue] = useState(
    props.data.partsFixedratevalue !== null
      ? props.data.partsFixedratevalue.split(" ")[1]
      : ""
  );

  const [partsfixedDate, setPartsfixedDate] = useState<any>(
    props.data.partsFixedratedate !== null
      ? props.data.partsFixedratedate
      : new Date()
  );

  const checkedHandler = (event: any) => {
    let checked = event.target.checked;
    let colId = props.column.colId;

    if (props.column.colId == "laborFixedRate") {
      props.node.setDataValue(colId, checked == true ? 1 : 0);
    }
    if (props.data.laborFixedRate == 1 && checked == true) {
      props.api.refreshCells({
        columns: ["laborFixedRate"],
        rowNodes: [props.node],
        force: true,
      });
    } else {
      props.api.refreshCells({
        columns: ["laborFixedRate"],
        rowNodes: [props.node],
        force: true,
      });
    }
  };

  const partsCheckedHandler = (event: any) => {
    let checked = event.target.checked;
    let colId = props.column.colId;

    if (props.column.colId == "partsFixedRate") {
      props.node.setDataValue(colId, checked == true ? 1 : 0);
    }
    if (props.data.partsFixedRate == 1 && checked == true) {
      props.api.refreshCells({
        columns: ["partsFixedRate"],
        rowNodes: [props.node],
        force: true,
      });
      props.context.componentParent.onPartsFixedDateChanged(
        props.data.fixedRateDate,
        props.data.fixedRateDate == null || props.fixedRateDate == ""
          ? moment(new Date()).format("MM/DD/YY")
          : props.data.fixedRateDate
      );
      props.context.componentParent.onPartsFixedRateChanged(
        props.data.fixedRateValue,
        "Cost"
      );
    } else {
      props.api.refreshCells({
        columns: ["partsFixedRate"],
        rowNodes: [props.node],
        force: true,
      });
    }
  };

  const handleCallback = (start: any, end: any, label: any) => {
    let colId = props.column.colId;
    if (props.column.colId == "laborFixedRate") {
      if (props.data.laborFixedRate == 1) {
        props.context.componentParent.onLaborFixedDateChanged(
          props.data.laborFixedratedate,
          start.format("YYYY-MM-DD")
        );
      } else {
        props.context.componentParent.onLaborFixedDateChanged(
          props.data.laborFixedratedate,
          null
        );
      }
    } else {
      if (props.data.partsFixedRate == 1) {
        props.context.componentParent.onPartsFixedDateChanged(
          props.data.partsFixedratedate,
          start.format("YYYY-MM-DD")
        );
      } else {
        props.context.componentParent.onPartsFixedDateChanged(
          props.data.partsFixedratedate,
          null
        );
      }
    }
  };

  const handleMarkupChange = (event: any) => {
    setPartsMarkup(event.target.value);
    if (props.data.partsFixedRate == 1) {
      if (event.target.value == "Cost" || event.target.value == "List") {
        setIsValid(true);
        props.context.componentParent.onPartsFixedRateChanged(
          props.data.partsFixedratevalue,
          event.target.value == "N/A" ? "Cost" : event.target.value
        );
      } else {
        setIsValid(false);
        var val =
          partsfixedValue != null && partsfixedValue != ""
            ? partsfixedValue
            : "";
        props.context.componentParent.onPartsFixedRateChanged(
          props.data.partsFixedratevalue,
          (event.target.value == "N/A" ? "Cost" : event.target.value) +
            " " +
            val
        );
      }
    } else {
      props.context.componentParent.onPartsFixedRateChanged(
        props.data.partsFixedratevalue,
        null
      );
    }
    if (props.data.partsFixedRate == 1) {
      props.context.componentParent.onPartsFixedDateChanged(
        props.data.partsFixedratedate,
        props.data.partsFixedratedate == null || props.partsFixedratedate == ""
          ? moment(new Date()).format("MM/DD/YY")
          : props.data.partsFixedratedate
      );
    }
  };
  const handleFixedRateChange = (event: any) => {
    let colId = props.column.colId;
    if (props.column.colId == "laborFixedRate") {
      if (props.data.laborFixedRate == 1) {
        props.context.componentParent.onLaborFixedRateChanged(
          props.data.laborFixedratevalue,
          event.target.value
        );
      } else {
        props.context.componentParent.onLaborFixedRateChanged(
          props.data.laborFixedratevalue,
          null
        );
      }
      if (props.data.laborFixedRate == 1) {
        props.context.componentParent.onLaborFixedDateChanged(
          props.data.laborFixedratedate,
          props.data.laborFixedratedate == null ||
            props.laborFixedratedate == ""
            ? moment(new Date()).format("MM/DD/YY")
            : props.data.laborFixedratedate
        );
      }
    } else {
      if (props.data.partsFixedRate == 1) {
        props.context.componentParent.onPartsFixedRateChanged(
          props.data.partsFixedratevalue,
          (partsMarkup == "N/A" ? "Cost" : partsMarkup) + " " + partsfixedValue
        );
      } else {
        props.context.componentParent.onPartsFixedRateChanged(
          props.data.partsFixedratevalue,
          null
        );
      }
      if (props.data.partsFixedRate == 1) {
        props.context.componentParent.onPartsFixedDateChanged(
          props.data.partsFixedratedate,
          props.data.partsFixedratedate == null ||
            props.partsFixedratedate == ""
            ? moment(new Date()).format("MM/DD/YY")
            : props.data.partsFixedratedate
        );
      }
    }
  };
  var regEx = /^[+-]?([0-9]+\.?[0-9]*|\.[0-9]+)$/;
  var regExParts = /^[+-]?([0-9]+\.?[0-9]*|\.[0-9]+)$/;

  return props.colDef.field == "laborFixedRate" ? (
    <div
      style={{
        display: "inline-flex",
        gap: 3,
        marginTop:
          props?.context?.componentParent?.editedRowId != null ? "-2px" : "1px",
        alignItems: "center",
      }}
    >
      <input
        type="checkbox"
        title="Labor Fixed Rate"
        className="laborFixedRate"
        disabled={
          props?.context?.componentParent?.editedRowId == null
            ? true
            : props?.context?.componentParent?.editedRowId != props.rowIndex
            ? true
            : props.data.gridExcluded == 1
            ? true
            : false
        }
        onClick={checkedHandler}
        checked={props.data.laborFixedRate == 0 ? false : true}
      />

      <FormControl
        variant="outlined"
        className="laborFixedRateValue"
        disabled={
          props?.context?.componentParent?.editedRowId != null &&
          props.data.laborFixedRate == 1
            ? false
            : true
        }
        style={{
          display:
            props?.context?.componentParent?.editedRowId != null &&
            props.data.gridExcluded != 1
              ? props.context?.componentParent?.editedRowId == props.rowIndex
                ? "block"
                : "none"
              : "none",
        }}
      >
        <OutlinedInput
          style={{
            height: 24,
            width: 60,
            backgroundColor: "#fff",
            marginTop: 3,
            paddingLeft: "8px !important",
            fontSize: 12,
          }}
          id="outlined-adornment-weight"
          title="Labor Fixed Rate($)"
          value={laborfixedValue}
          error={
            props?.context?.componentParent?.laborfixedRateError == null
              ? false
              : props?.context?.componentParent?.laborfixedRateError ==
                props.rowIndex
              ? true
              : false
          }
          onBlur={handleFixedRateChange}
          onChange={(e: any) => {
            (e.target.value === "" || regEx.test(e.target.value)) &&
              setLaborfixedValue(e.target.value);
          }}
          startAdornment={
            <InputAdornment
              style={{
                fontSize: "12px !important",
                paddingLeft: "0px !important",
              }}
              position="start"
              disableTypography={true}
            >
              $
            </InputAdornment>
          }
          aria-describedby="outlined-weight-helper-text"
          inputProps={{
            "aria-label": "Fixed Rate Value",
          }}
          labelWidth={0}
        />
      </FormControl>
      <span
        style={{
          display:
            props?.context?.componentParent?.editedRowId == null &&
            props.data.laborFixedRate == 1
              ? "block"
              : props?.context?.componentParent?.editedRowId != props.rowIndex
              ? "block"
              : "none",
        }}
      >
        {props.data.laborFixedratevalue != null &&
          props.data.laborFixedratevalue != "" &&
          props.data.laborFixedratevalue != 0 &&
          "$" + props.data.laborFixedratevalue}
      </span>

      <FormControl
        variant="outlined"
        style={{
          display:
            props?.context?.componentParent?.editedRowId != null &&
            props.data.gridExcluded != 1
              ? props?.context?.componentParent?.editedRowId == props.rowIndex
                ? "block"
                : "none"
              : "none",
        }}
        margin="dense"
        title="Effective From"
      >
        <DateRangePicker
          initialSettings={{
            locale: {
              format: "MM/DD/YY",
              separator: " - ",
            },
            autoUpdateInput: true,
            showDropdowns: true,
            autoApply: true,
            singleDatePicker: true,
            startDate: new Date(laborfixedDate),
          }}
          onCallback={handleCallback}
        >
          <input
            type="text"
            disabled={
              props?.context?.componentParent?.editedRowId != null &&
              props.data.laborFixedRate == 1
                ? false
                : true
            }
            style={{
              height: "24px",
              width: "75px",
              border: "1px solid #c0c0c0",
              borderRadius: "4px",
              boxSizing: "border-box",
              paddingLeft: "5px",
              fontSize: 12,
            }}
            id="picker"
            name="picker"
            aria-labelledby="label-picker"
          />
        </DateRangePicker>
      </FormControl>
      <span
        style={{
          display:
            props?.context?.componentParent?.editedRowId == null &&
            props.data.laborFixedRate == 1
              ? "block"
              : props?.context?.componentParent?.editedRowId != props.rowIndex
              ? "block"
              : "none",
        }}
      >
        {props.data.laborFixedratedate != null &&
          "[" +
            moment(new Date(props.data.laborFixedratedate)).format("MM/DD/YY") +
            "]"}
      </span>
    </div>
  ) : props.colDef.field == "partsFixedRate" ? (
    <div
      style={{
        display: "inline-flex",
        gap: 3,
        marginTop:
          props?.context?.componentParent?.editedRowId != null ? "-2px" : "1px",
        alignItems: "center",
      }}
    >
      <input
        type="checkbox"
        title="Parts Fixed Rate"
        className="partsFixedRate"
        disabled={
          props?.context?.componentParent?.editedRowId == null
            ? true
            : props?.context?.componentParent?.editedRowId != props.rowIndex
            ? true
            : props.data.gridExcluded == 1
            ? true
            : false
        }
        onClick={partsCheckedHandler}
        checked={props.data.partsFixedRate == 0 ? false : true}
      />
      <select
        disabled={
          props?.context?.componentParent?.editedRowId != null &&
          props.data.partsFixedRate == 1
            ? false
            : true
        }
        style={{
          display:
            props?.context?.componentParent?.editedRowId != null &&
            props.data.gridExcluded != 1
              ? props?.context?.componentParent?.editedRowId == props.rowIndex
                ? "block"
                : "none"
              : "none",

          background: "#fff",
          height: "24px",
          border: "1px solid #ccc",
          borderRadius: "4px",
          marginTop: "2px",
          paddingLeft: 4,
        }}
        onChange={(e) => handleMarkupChange(e)}
        value={partsMarkup}
        name="parts-markups"
        id="parts-markups"
      >
        <option value="Cost">Cost</option>
        <option value="Cost+">Cost+</option>
        <option value="Cost%">Cost%</option>
        <option value="List">List</option>
        <option value="List-">List-</option>
        <option value="List+">List+</option>
        <option value="List%">List%</option>
      </select>

      <FormControl
        variant="outlined"
        disabled={
          props?.context?.componentParent?.editedRowId != null &&
          props.data.partsFixedRate == 1 &&
          !isValid
            ? false
            : true
        }
        className="partsFixedRateValue"
        style={{
          display:
            props?.context?.componentParent?.editedRowId != null &&
            props.data.gridExcluded != 1
              ? props?.context?.componentParent?.editedRowId == props.rowIndex
                ? "block"
                : "none"
              : "none",
        }}
      >
        <OutlinedInput
          style={{
            height: 24,
            width: 60,
            backgroundColor: "#fff",
            marginTop: 3,
            fontSize: 12,
            paddingLeft: "0px !important",
          }}
          title="Parts Fixed Rate(%)"
          id="outlined-adornment-weight"
          value={partsfixedValue}
          error={
            props?.context?.componentParent?.partsfixedRateError == null
              ? false
              : props?.context?.componentParent?.partsfixedRateError ==
                props.rowIndex
              ? true
              : false
          }
          onBlur={(e) => handleFixedRateChange(e)}
          onChange={(e) => {
            (e.target.value === "" || regExParts.test(e.target.value)) &&
              setPartsfixedValue(e.target.value);
          }}
          endAdornment={
            <InputAdornment position="end" disableTypography={true}>
              %
            </InputAdornment>
          }
          aria-describedby="outlined-weight-helper-text"
          inputProps={{
            "aria-label": "Fixed Rate Value",
          }}
          labelWidth={0}
        />
      </FormControl>
      <span
        style={{
          display:
            props?.context?.componentParent?.editedRowId == null &&
            props.data.partsFixedRate == 1
              ? "block"
              : props?.context?.componentParent?.editedRowId != props.rowIndex
              ? "block"
              : "none",
        }}
      >
        {props.data.partsFixedratevalue != null &&
        props.data.partsFixedratevalue != "" &&
        props.data.partsFixedratevalue != "N/A" &&
        props.data.partsFixedratevalue != "Cost" &&
        props.data.partsFixedratevalue != "List"
          ? props.data.partsFixedratevalue + "%"
          : props.data.partsFixedratevalue}
      </span>

      <FormControl
        variant="outlined"
        style={{
          display:
            props?.context?.componentParent?.editedRowId != null &&
            props.data.gridExcluded != 1
              ? props?.context?.componentParent?.editedRowId == props.rowIndex
                ? "block"
                : "none"
              : "none",
        }}
        margin="dense"
        title="Effective From"
      >
        <DateRangePicker
          initialSettings={{
            locale: {
              format: "MM/DD/YY",
              separator: " - ",
            },
            autoUpdateInput: true,
            showDropdowns: true,
            autoApply: true,
            singleDatePicker: true,
            startDate: new Date(partsfixedDate),
          }}
          onCallback={handleCallback}
        >
          <input
            type="text"
            disabled={
              props?.context?.componentParent?.editedRowId != null &&
              props.data.partsFixedRate == 1
                ? false
                : true
            }
            style={{
              height: "24px",
              width: "75px",
              border: "1px solid #c0c0c0",
              borderRadius: "4px",
              boxSizing: "border-box",
              paddingLeft: "5px",
              fontSize: 12,
            }}
            id="picker"
            name="picker"
            aria-labelledby="label-picker"
          />
        </DateRangePicker>
      </FormControl>

      <span
        style={{
          display:
            props.context.componentParent.editedRowId == null &&
            props.data.partsFixedRate == 1
              ? "block"
              : props.context.componentParent.editedRowId != props.rowIndex
              ? "block"
              : "none",
        }}
      >
        {props.data.partsFixedratedate != null &&
          "[" +
            moment(new Date(props.data.partsFixedratedate)).format("MM/DD/YY") +
            "]"}
      </span>
    </div>
  ) : (
    ""
  );
};

export default FixedRateRenderer;
