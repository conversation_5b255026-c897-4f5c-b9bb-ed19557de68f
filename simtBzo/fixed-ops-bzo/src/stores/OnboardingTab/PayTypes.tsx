import { AgGridReact } from "ag-grid-react";
import "ag-grid-community/styles/ag-grid.css";
// import "ag-grid-community/styles/ag-theme-alpine.css";
import { useEffect, useMemo, useRef, useState } from "react";
import "../../style.css";
import { updatePayTypeMasterByCode } from "../../service/mutations";
import { useRecordContext, useTranslate } from "react-admin";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { Button } from "@mui/material";
import {
  faBan,
  faPencil,
  faFloppyDisk,
} from "@fortawesome/free-solid-svg-icons";
import {
  GetOpcodeChoiceQuery,
  GetPayTypeMasterDetails,
} from "../../service/dataFetchQueries";
import { Constants } from "../../utils/constants";
import SnackBarMessage from "../../components/SnackBarMessage";
import { traceSpan } from "../../utils/OTTTracing";

const CustomTooltipCellRenderer = ({ value, colDef }: any) => {
  const isOpcodeColumn = colDef.field === "lbropcode";

  return (
    <div
      title={value}
      style={{ fontWeight: isOpcodeColumn ? "bold" : "normal" }}
    >
      {value}
    </div>
  );
};
function PayTypes() {
  const record = useRecordContext();
  const gridApiRef: any = useRef();
  let gridRef = useRef<any>();
  const gridColumnApiRef: any = useRef();
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [rowData, setRowData] = useState();
  const [originalRowData, setOriginalRowData] = useState<any>(null);
  const [saveNewValue, setsaveNewValue] = useState(false);
  const [opcategoryOptions, setOpcategoryOptions] = useState<any>(null);
  const [departmentOptions, setDepartmentOptions] = useState<any>(null);
  const [dataForSave, setDataForSave] = useState<any>([]); // ToDo :Need to change variable name
  const [tempDataForSave, setTempDataForSave] = useState<any>([]); // ToDo :Need to change variable name
  const [paramsApi, setParamsApi] = useState<any>(null);
  const translate = useTranslate();
  const buttonStyle = {
    borderRadius: "3px",
    height: "22px",
    border: "0",
    fontSize: "12px",
    cursor: "pointer",
    lineHeight: "13px",
  };

  const onFilterChanged = (e: any) => {
          const filterValues = e.api.getFilterModel();
          Object.keys(filterValues).forEach((colId) => {
            traceSpan(`filter_grid_${colId}`, {
              event: `filter_grid_${colId}`,
          pageUrl: window.location.pathname,
          timestamp: new Date().toISOString(),
          userId: localStorage.getItem("user") || "",
          column: colId,
          filterValue: JSON.stringify(filterValues[colId])
            });
          });
        };
      const onSortChanged = (params: any) => {
        // Get column states and filter only sorted columns
        const sortModel = params.columnApi.getColumnState()
          .filter((col: any) => col.sort != null)
          .map((col: any) => ({
            colId: col.colId,
            sort: col.sort
          }));
      
        sortModel.forEach((sortItem: { colId: string; sort: 'asc' | 'desc' }) => {
          traceSpan(`sort_grid_${sortItem.colId}`, {
             event: `sort_grid_${sortItem.colId}`,
        pageUrl: window.location.pathname,
        timestamp: new Date().toISOString(),
        userId: localStorage.getItem('user') || '',
        column: sortItem.colId,
        direction: sortItem.sort
          });
        });
      };

  const columnDefs = [
    {
      headerName: "Id",
      flex: 1,
      field: "id",
      suppressMovable: true,
      hide: true,
      suppressMenu: true,
      unSortIcon: true,
    },
    {
      headerName: "Dealer Pay Type",
      flex: 1,
      field: "payType",
      editable: false,
      suppressMovable: true,
      suppressMenu: true,
      unSortIcon: true,
      cellStyle() {
        return { textAlign: "left", border: " 0px white" };
      },
    },
    {
      headerName: "FOPC Group",
      flex: 1,
      field: "payTypeCode",
      suppressMovable: true,
      suppressMenu: true,
      unSortIcon: true,
      editable: true,
      singleClickEdit: true,
      filter: "agSetColumnFilter",
      filterParams: {
        applyMiniFilterWhileTyping: true,
      },
      cellStyle() {
        return { textAlign: "left", border: " 0px white" };
      },
      cellEditor: "agSelectCellEditor",
      cellEditorParams: {
        values: ["C", "E", "F", "I", "M", "W"],
      },
    },
    {
      field: "department",
      flex: 1,
      minWidth: 130,
      editable: true,
      headerName: "Dept",
      cellEditor: "agSelectCellEditor",
      cellRendererFramework: CustomTooltipCellRenderer,
      cellEditorParams: {
        values: departmentOptions,
      },
    },
    {
      headerName: "Action",
      flex: 1,
      minWidth: 110,
      cellStyle: { border: "none" },
      suppressNavigable: true,
      sortable: false,
      filter: false,
      unSortIcon: false,
      cellRenderer: function (params: any) {
        const editingCells = params?.api?.getEditingCells();
        const isCurrentRowEditing = editingCells?.some((cell: any) => {
          return cell?.rowIndex === params?.node?.rowIndex;
        });
        if (isCurrentRowEditing) {
          return (
            <div style={{ marginTop: "0px" }}>
              <button
                style={buttonStyle}
                className="action-button update "
                data-action={Constants.actions.update}
              >
                <FontAwesomeIcon icon={faFloppyDisk} />
              </button>
              <button
                style={buttonStyle}
                className="action-button cancel "
                data-action={Constants.actions.cancel}
              >
                <FontAwesomeIcon icon={faBan} />
              </button>
            </div>
          );
        } else {
          return (
            <div style={{ marginTop: "0px" }}>
              <button
                style={buttonStyle}
                className="action-button"
                // hide for store launched status
                // className={`action-button ${
                //   record.storeLaunched? "action-button-disabled" : ""
                // }`}
                data-action={Constants.actions.edit}
                // disabled={record.storeLaunched}
              >
                <FontAwesomeIcon icon={faPencil} />
              </button>
            </div>
          );
        }
      },
      editable: false,
      colId: "action",
    },
  ];

  const defaultColDef = useMemo(() => {
    return {
      minWidth: 100,
      resizable: true,
      floatingFilter: true,
      sortable: true,
      filter: true,
      suppressMenu: true,
      unSortIcon: true,
      filterParams: {
        applyMiniFilterWhileTyping: true,
      },
    };
  }, []);

  const updateOpcode = (updatedData: any) => {
    paramsApi?.showLoadingOverlay();
    const pValData = updatedData.map((item: any) => ({
      paytype: item.payType,
      oldpaytypecode: item.oldpaytypecode,
      newpaytypecode: item.payTypeCode,
      olddepartment: item.olddepartment,
      newdepartment: item.department,
      oldgridexcluded: item.gridExcluded,
      newgridexcluded: item.gridExcluded,
      oldlabor_fixedratevalue: item.laborFixedratevalue,
      newlabor_fixedratevalue: item.laborFixedratevalue,
      oldparts_fixedratevalue: item.partsFixedratevalue,
      newparts_fixedratevalue: item.partsFixedratevalue,
      oldlabor_fixedratedate: item.laborFixedratedate,
      newlabor_fixedratedate: item.laborFixedratedate,
      oldparts_fixedratedate: item.partsFixedratedate,
      newparts_fixedratedate: item.partsFixedratedate,
      oldlabor_fixed_rate: item.laborFixedRate,
      newlabor_fixed_rate: item.laborFixedRate,
      oldparts_fixed_rate: item.partsFixedRate,
      newparts_fixed_rate: item.partsFixedRate,
    }));
    updatePayTypeMasterByCode(pValData, record.storeId, record.tenantId)
      .then((result: any) => {
        if (result.statuses == 1) {
          fetchPayTypes();
          setOpenSnackbar(true);
          setDataForSave([]);
          setTempDataForSave([]);
        }
      })
      .catch((error: any) => {
        console.log(error);
        // ToDo: Need show Error Message
      });
  };

  const fetchOpcodeChoice = async (pType: any) => {
    try {
      const result = await GetOpcodeChoiceQuery(pType).then((res) => {
        return res;
      });
      return result.map((item: any) => item.value);
    } catch (error) {
      console.log(error);
      return [];
    }
  };

  const fetchPayTypes = async () => {
    const fetchOnboardingOpcodeDataPromise = GetPayTypeMasterDetails(
      record.tenantId,
      record.storeId
    ).then((res) => {
      return res;
    });

    const fetchDepartmentChoicePromise = fetchOpcodeChoice("department");
    Promise.all([
      fetchOnboardingOpcodeDataPromise,
      fetchDepartmentChoicePromise,
    ])
      .then(([onboardingData, departmentChoice /*categoryChoice*/]) => {
        departmentChoice.unshift(
          ""
        ); /* Adding an empty option at the beginning */
        setRowData(onboardingData);
        setDepartmentOptions(departmentChoice);
      })
      .catch((error) => {
        console.log(error);
      });
  };

  const onGridReady = (params: any) => {
    gridApiRef.current = params.api;
    gridRef.current = params.api;
    gridColumnApiRef.current = params.columnApi;
    setParamsApi(params.api);
    fetchPayTypes();
  };
  const onCellClicked = (params: any) => {
    const target = params.event.target;
    let action = target.closest("[data-action]")?.dataset.action;
    // Handle click event for action cells
    if (params.column.colId === "action" && action) {
      if (action === Constants.actions.edit) {
        traceSpan(`click_edit_icon`, {
          event: `click_edit_icon`,
          pageUrl: window.location.pathname,
          timestamp: new Date().toISOString(),
          userId: localStorage.getItem('user') || '',
          value: params.node.rowIndex
        });
        const node = params.node;
        const data = node.data;
        params.api.startEditingCell({
          rowIndex: params.node.rowIndex,
          // gets the first columnKey
          colKey:
            gridColumnApiRef?.current?.getDisplayedCenterColumns()[0].colId,
        });
        setOriginalRowData({ ...data });
      }
      if (action === Constants.actions.update) {
        traceSpan(`click_save_icon`, {
          event: `click_save_icon`,
          pageUrl: window.location.pathname,
          timestamp: new Date().toISOString(),
          userId: localStorage.getItem('user') || '',
          value: params.node.rowIndex
        });
        setsaveNewValue(true);
        params.api.stopEditing(false);
      }

      if (action === Constants.actions.cancel) {
        traceSpan(`click_editcancel_icon`, {
          event: `click_editcancel_icon`,
          pageUrl: window.location.pathname,
          timestamp: new Date().toISOString(),
          userId: localStorage.getItem('user') || '',
          value: params.node.rowIndex
        });
        params.api.stopEditing(true);
      }
    }
  };
  const onRowEditingStarted = (params: any) => {
    params.api.refreshCells({
      columns: ["action"],
      rowNodes: [params.node],
      force: true,
    });
  };
  const onRowEditingStopped = (params: any) => {
    const { api, node } = params;
    const editedData = node.data;
    const originalData: any = originalRowData;
    if (originalData) {
      if (!saveNewValue) {
        // Update the row with the filtered or original data
        const filteredData = tempDataForSave.find(
          (item: any) => item.id === originalData.id
        );
        node.setData(filteredData || originalData);
        // Update state arrays based on the filtered data
        setTempDataForSave(
          tempDataForSave.filter(
            (item: { id: any }) => item.id !== originalData.id
          )
        );
        setDataForSave(
          dataForSave.filter((item: { id: any }) => item.id !== originalData.id)
        );
      } else {
        // Update the row with the edited data
        const updatedData = {
          ...originalData,
          ...editedData,
          olddepartment: originalData.department,
          oldpaytypecode: originalData.payTypeCode,
        };

        node.setData(updatedData);

        if (JSON.stringify(updatedData) !== JSON.stringify(originalData)) {
          // Update dataForSave array
          const existingItemIndex = dataForSave.findIndex(
            (item: { id: any }) => item.id === updatedData.id
          );
          if (existingItemIndex !== -1) {
            dataForSave.splice(existingItemIndex, 1);
          }
          dataForSave.push(updatedData);
          // Update tempDataForSave array
          const existingOriginalIndex = tempDataForSave.findIndex(
            (item: any) => item.id === originalData.id
          );
          if (existingOriginalIndex === -1) {
            tempDataForSave.push(originalData);
          }
        }
      }
    }
    setsaveNewValue(false);
  };

  return (
    <>
      <div
        className={Constants.ag_grid_theme}
        style={{ height: "70vh", width: "35vw" }}
      >
        <div style={{ height: "3vh", width: "100%", position: "relative" }}>
          <Button
            size="small"
            variant="contained"
            color="primary"
            disabled={dataForSave.length > 0 ? false : true}
            onClick={() => {
              traceSpan(`click_savechanges_button`, {
      event: `click_savechanges_button`,
      pageUrl: window.location.pathname,
      timestamp: new Date().toISOString(),
      userId:localStorage.getItem('user') || ''
    });
              updateOpcode(dataForSave);
            }}
            style={{
              position: "absolute",
              top: 0,
              right: 0,
              transform: "translateY(-50%)",
              textTransform: "none",
              fontSize: "12px",
            }}
          >
            Save Changes
          </Button>
        </div>
        <AgGridReact
          onRowEditingStopped={onRowEditingStopped}
          onRowEditingStarted={onRowEditingStarted}
          onCellClicked={onCellClicked}
          editType="fullRow"
          suppressClickEdit={true}
          suppressColumnVirtualisation={true}
          columnDefs={columnDefs}
          suppressChangeDetection={true}
          onGridReady={onGridReady}
          rowData={rowData}
          suppressCellSelection={true}
          suppressRowClickSelection={true}
          defaultColDef={defaultColDef}
          tooltipShowDelay={0}
          onFilterChanged={onFilterChanged}
          onSortChanged={onSortChanged}
        />
        <SnackBarMessage
          onClose={() => setOpenSnackbar(false)}
          open={openSnackbar}
          message={translate("SUCCESS_MESSAGES.UPDATE_MESSAGE", {
            entityName: "Pay Types",
          })}
        />
      </div>
    </>
  );
}

export default PayTypes;
