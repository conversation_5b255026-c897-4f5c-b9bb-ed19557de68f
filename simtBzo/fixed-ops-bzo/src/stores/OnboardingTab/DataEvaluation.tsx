import { Box, Typography } from "@mui/material";
import { makeStyles } from "@material-ui/core/styles";
import { useCallback, useEffect, useMemo, useState } from "react";
import { AgGridReact } from "ag-grid-react";
import { ColDef } from "ag-grid-community";
import BulkLoadingQueries from "../../service/DataFetchQueries/bulkLoadingQueries";
import { useRecordContext } from "react-admin";
import { Constants } from "../../utils/constants";
import { traceSpan } from "../../utils/OTTTracing";

const useStyles = makeStyles((theme: any) => ({
  activeTab: {
    border: "solid 1px",
    borderColor: "#003d6b",
    backgroundColor: "#b4d1e7",
    fontWeight: "bold",
    fontSize: "14px",
    fontFamily: "Montserrat",
  },
  inActiveTab: {
    border: "solid 1px",
    borderColor: "#d5dde2",
    fontSize: "14px",
    fontFamily: "Montserrat",
  },
}));

const DataEvaluation = (props: any) => {
  const classes = useStyles();
  const record = useRecordContext();
  const {
    GetDataEvaluation,
    GetMissingLaborPartsDates,
    GetIngestTableCloasedDates,
  } = BulkLoadingQueries;
  const [activeTab, setActiveTab] = useState("phase_1");
  const [gridApi, setGridApi] = useState<any>();
  const [rowDataPhase1, setRowDataPhase1] = useState([]);
  const [rowDataPhase2, setRowDataPhase2] = useState([]);
  const [rowDataOpcodes, setRowDataOpcodes] = useState([]);
  const [missingLaborDates, setMissingLaborDates] = useState<any>();
  const [missingPartsDates, setMissingPartsDates] = useState<any>([]);
  const [closedDates, setClosedDates] = useState<any>([]);
  const dms =
    record.dms === "Dealertrack"
      ? "dtk"
      : record.dms === "Automate"
      ? "atm"
      : record.dms === "Reynolds"
      ? "rlds"
      : record.dms === "CDK Global"
      ? "cdk"
      : record.dms === "Tekion"
      ? "tek"
      : "";
  useEffect(() => {
    if (gridApi) {
      getEvaluationDetails("phase_1");
      getMissingDates("labor_missing_dates");
      getMissingDates("parts_missing_dates");
      getClosedDates();
      getEvaluationDetails("phase_2");
      getEvaluationDetails("opcodes");
    }
  }, [gridApi]);

  const getClosedDates = () => {
    GetIngestTableCloasedDates({
      storeId: record.storeId,
      tenantId: record.tenantId,
      Realm: record.realmName,
    }).then((res) => {
      setClosedDates(res);
    });
  };

  const getEvaluationDetails = (grid: string) => {
    GetDataEvaluation({
      storeId: record.storeId,
      tenantId: record.tenantId,
      callType: grid,
      Realm: record.realmName,
      dms: dms,
    }).then((res) => {
      grid === "opcodes"
        ? setRowDataOpcodes(res)
        : grid === "phase_1"
        ? setRowDataPhase1(res)
        : setRowDataPhase2(res);
    });
  };

  const getMissingDates = (grid: string) => {
    GetMissingLaborPartsDates({
      storeId: record.storeId,
      tenantId: record.tenantId,
      callType: grid,
      Realm: record.realmName,
      dms: dms,
    }).then((res) => {
      grid === "labor_missing_dates"
        ? setMissingLaborDates(res)
        : setMissingPartsDates(res);
    });
  };

  const onGridReady = useCallback((params: any) => {
    setGridApi(params?.api);
    params?.api?.showLoadingOverlay();
  }, []);

  const columnDefs: ColDef[] = [
    {
      headerName: "sortOrder",
      field: "sortOrder",
      hide: true,
      sortable: true,
      sort: "asc",
    },
    {
      headerName: "",
      field: "label",
      cellStyle: (params: any) => {
        return {
          fontWeight: params.data.isBold ? "bold" : "normal",
          fontSize: params.data.isBold ? "16px" : "14px",
        };
      },
    },
    {
      headerName: "",
      field: "value",
      cellStyle: (params: any) => {
        return {
          fontWeight: params.data.isBold ? "bold" : "normal",
          fontSize: params.data.isBold ? "16px" : "14px",
          color: params.data.isBold ? params.data.statusFlag : "black",
        };
      },
    },
  ];
  const columnMissingDatesDefs: ColDef[] = [
    { field: "year", rowGroup: true, hide: true, headerName: "Year" },
    { field: "missingDates", headerName: "Missing Dates" },
  ];

  const columnIngestClosedDatesDefs: ColDef[] = [
    {
      field: "tableName",
      rowGroup: true,
      hide: true,
      headerName: "Table Name",
      maxWidth: 400,
    },
    { field: "closedDates", headerName: "Closed Dates", maxWidth: 200 },
  ];

  const defaultColDef = useMemo(() => {
    return {
      flex: 1,
      minWidth: 150,
      resizable: true,
      suppressMenu: true,
      unSortIcon: true,
      enableValue: true,
    };
  }, []);
  const autoGroupColumnDef = useMemo(() => {
    return {
      headerName: "Year",
    };
  }, []);

  const autoGroupColumnClosedDatesDef = useMemo(() => {
    return {
      headerName: "Table Name",
    };
  }, []);

  const getGridWrapper = (table?: string) => {
    return (
      <div
      className={Constants.ag_grid_theme}
        style={{
          height: activeTab === "phase_1" ? 532 : 600,
          width:
            table === "labor" || table === "parts"
              ? 400
              : table === "ingest"
              ? 600
              : 1200,
          marginBottom: "10px",
        }}
      >
        <AgGridReact
          columnDefs={
            table === "labor" || table === "parts"
              ? columnMissingDatesDefs
              : table === "ingest"
              ? columnIngestClosedDatesDefs
              : columnDefs
          }
          onGridReady={onGridReady}
          rowData={
            activeTab === "phase_1" && table === "labor"
              ? missingLaborDates
              : activeTab === "phase_1" && table === "parts"
              ? missingPartsDates
              : activeTab === "phase_1" && table === "ingest"
              ? closedDates
              : activeTab === "phase_1"
              ? rowDataPhase1
              : activeTab === "phase_2"
              ? rowDataPhase2
              : rowDataOpcodes
          }
          defaultColDef={defaultColDef}
          autoGroupColumnDef={
            table === "ingest"
              ? autoGroupColumnClosedDatesDef
              : autoGroupColumnDef
          }
          // rowHeight={45}
          // @ts-ignore
          // ref={gridRef}
          suppressColumnVirtualisation={true}
          suppressChangeDetection={true}
        />
      </div>
    );
  };

  return (
    <Box sx={{ paddingTop: "10px", minHeight: "70vh" }}>
      <Box
        sx={{
          height: "36px",
          backgroundColor: "#eaf1f6",
          display: "flex",
          alignItems: "center",
          mb: 2,
        }}
      >
        <Box
          className={
            activeTab === "phase_1" ? classes.activeTab : classes.inActiveTab
          }
          sx={{
            flex: 1,
            display: "flex",
            justifyContent: "center",
            height: "100%",
            alignItems: "center",
            cursor: "pointer",
          }}
          onClick={() => {
            traceSpan(`click_dataevaluation_phase1_tab`, {
              event: `click_dataevaluation_phase1_tab`,
              pageUrl: window.location.pathname,
              timestamp: new Date().toISOString(),
              userId:localStorage.getItem('user') || ''
            });
            setActiveTab("phase_1");
          }}
        >
          Phase 1
        </Box>
        <Box
          className={
            activeTab === "opcodes" ? classes.activeTab : classes.inActiveTab
          }
          sx={{
            flex: 1,
            display: "flex",
            justifyContent: "center",
            height: "100%",
            alignItems: "center",
            cursor: "pointer",
          }}
          onClick={() => {
            traceSpan(`click_dataevaluation_opcodes_tab`, {
              event: `click_dataevaluation_opcodes_tab`,
              pageUrl: window.location.pathname,
              timestamp: new Date().toISOString(),
              userId:localStorage.getItem('user') || ''
            });
            setActiveTab("opcodes");
          }}
        >
          Opcodes
        </Box>
        <Box
          className={
            activeTab === "phase_2" ? classes.activeTab : classes.inActiveTab
          }
          sx={{
            flex: 1,
            display: "flex",
            justifyContent: "center",
            height: "100%",
            alignItems: "center",
            cursor: "pointer",
          }}
          onClick={() => {
            traceSpan(`click_dataevaluation_phase2_tab`, {
              event: `click_dataevaluation_phase2_tab`,
              pageUrl: window.location.pathname,
              timestamp: new Date().toISOString(),
              userId:localStorage.getItem('user') || ''
            });
            setActiveTab("phase_2");
          }}
        >
          Phase 2
        </Box>
      </Box>
      <Box>{getGridWrapper()}</Box>
      {activeTab === "phase_1" && (
        <Box
          sx={{
            display: "flex",
            // justifyContent: "space-between",
            width: "100%",
            mt: 2,
          }}
        >
          <Box sx={{ display: "flex", mr: 2, flexDirection: "column" }}>
            <Typography variant="subtitle1" color="#003d6b" fontWeight={"bold"} fontSize={14} fontFamily={"Roboto"}>
              Missing dates for labor grid
            </Typography>
            {getGridWrapper("labor")}
          </Box>
          <Box sx={{ display: "flex", mr: 2, flexDirection: "column" }}>
            <Typography variant="subtitle1" color="#003d6b" fontWeight={"bold"} fontSize={14} fontFamily={"Roboto"}>
              Missing dates for parts matrix
            </Typography>
            {getGridWrapper("parts")}
          </Box>
          <Box sx={{ display: "flex", mr: 2, flexDirection: "column" }}>
            <Typography variant="subtitle1" color="#003d6b" fontWeight={"bold"} fontSize={14} fontFamily={"Roboto"}>
              Ingest schema table closed dates
            </Typography>
            {getGridWrapper("ingest")}
          </Box>
        </Box>
      )}
    </Box>
  );
};

export default DataEvaluation;
