import React, { useRef, useState } from "react";
import { ColDef } from "ag-grid-community";
import moment from "moment";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { Constants } from "../../../utils/constants";
import {
  faBan,
  faPencil,
  faFloppyDisk,
  faTrash,
} from "@fortawesome/free-solid-svg-icons";
import { useGetList } from "react-admin";
import CustomTextEditor from "./CustomTextEditor";
import CustomSelectEditor from "./CustomSelectEditor";
import CustomDateEditor from "./CustomDateEditor";
import { read } from "fs";

const DataFeedListGridDefs = (props: any) => {
  const { allLoadList } = props;
  console.log("rrrrrrrrrrrrrrrrrr---", allLoadList);
  const { data, isLoading } = useGetList("dmsMasters");
  const buttonStyle = {
    borderRadius: "3px",
    height: "22px",
    border: "0",
    fontSize: "12px",
    cursor: "pointer",
    lineHeight: "13px",
    margin: "0 4px",
  };

  const columnDefs: ColDef[] = [
    {
      headerName: "DMS",
      field: "dms",
      flex: 4,
      editable: true,
      cellEditor: CustomSelectEditor,
      cellEditorParams: {
        values: data?.map((item: any) => item.dms) || [],
        onValueChange: (newValue: string, props: any) => {
          const rowNode = props.node;

          // Copy current row data and update fields
          const updatedData = { ...rowNode.data, dms: newValue };

          // Clear dependent fields
          if (
            newValue === "Tekion" ||
            newValue === "CDK Global" ||
            newValue === "Automate" ||
            newValue === "Reynolds"
          ) {
            updatedData.ftlsSubscriptionId = "";
            updatedData.ftlsDepartment = "";
            updatedData.enterpriseCode = "";
            updatedData.companyNumber = "";
          }
          if (newValue === "Fortellis") {
            updatedData.dealerId = "";
            updatedData.enterpriseCode = "";
            updatedData.companyNumber = "";
          }
          if (newValue === "Dealertrack") {
            updatedData.dealerId = "";
            updatedData.ftlsSubscriptionId = "";
            updatedData.ftlsDepartment = "";
          }

          // Update row data
          rowNode.setData(updatedData);

          // Force full row refresh so cleared values visually disappear
          props.api.refreshCells({
            rowNodes: [rowNode],
            force: true, // re-evaluate valueFormatters, editable, etc.
          });

          // Optionally, also redraw the row entirely

          props.api.redrawRows({ rowNodes: [rowNode] });

          setTimeout(() => {
            const editButton = document.querySelector<HTMLButtonElement>(
              `.edit_${rowNode.rowIndex}`
            );
            if (editButton) {
              editButton.click();
            }
          }, 10);

          console.log("Updated row data:", updatedData);
        },
      },
    },

    // {
    //   headerName: "DMS",
    //   field: "dms",
    //   flex: 4,
    //   editable: true,
    //   cellEditor: "CustomSelectEditor", // use our custom editor
    //   cellEditorParams: {
    //     values: data?.map((item: any) => item.dms) || [],
    //   },
    // },
    {
      headerName: "Dealer Id",
      field: "dealerId",
      flex: 5,
      editable: (params) =>
        params.data.dms !== "Dealertrack" && params.data.dms !== "Fortellis",
      cellEditor: CustomTextEditor,
      cellEditorParams: {
        regex: /^[A-Za-z0-9]{1,10}$/, // Example: only alphanumeric max 10 chars
      },
      valueFormatter: (params: any) => (params.value ? params.value : "----"),
    },
    {
      headerName: "Enterprise Code",
      field: "enterpriseCode",
      flex: 5,
      editable: (params) => params.data.dms === "Dealertrack",
      cellEditor: CustomTextEditor,
      cellEditorParams: {
        regex: /^[0-9]{3,6}$/, // Example: 3–6 digits only
      },
      valueFormatter: (params: any) => (params.value ? params.value : "----"),
    },
    {
      headerName: "Company Number",
      field: "companyNumber",
      flex: 5,
      editable: (params) => params.data.dms === "Dealertrack",
      cellEditor: CustomTextEditor,
      cellEditorParams: {
        regex: /^[0-9]+$/, // only numbers
      },
      valueFormatter: (params: any) => (params.value ? params.value : "----"),
    },
    {
      headerName: "Subscription ID",
      field: "ftlsSubscriptionId",
      flex: 5,
      editable: (params) => params.data.dms === "Fortellis",
      cellEditor: CustomTextEditor,
      cellEditorParams: {
        regex: /^[A-Z0-9-]+$/, // Example: uppercase letters, numbers, hyphen
      },
      valueFormatter: (params: any) => (params.value ? params.value : "----"),
    },
    {
      headerName: "Department",
      field: "ftlsDepartment",
      flex: 5,
      editable: (params) => params.data.dms === "Fortellis",
      cellEditor: CustomTextEditor,
      cellEditorParams: {
        regex: /^[A-Za-z ]+$/, // only alphabets and space
      },
      valueFormatter: (params: any) => (params.value ? params.value : "----"),
    },
    {
      headerName: "Effective Date",
      field: "effectiveDate",
      cellStyle: { textAlign: "left", readOnly: "true" },
      cellEditor: CustomDateEditor,
      editable: (params) => params.data.effectiveDate != null,
      flex: 2,
      valueFormatter: (params: any) =>
        params.value ? moment(params.value).format("MM/DD/YYYY") : params.value,
    },

    {
      headerName: "Action",
      minWidth: 150,
      flex: 2,
      cellStyle: { border: "none" },
      suppressNavigable: true,
      sortable: false,
      filter: false,
      unSortIcon: false,
      cellRenderer: function (params: any) {
        const rowIndex = params.node.rowIndex;
        const editingCells = params?.api?.getEditingCells();
        const isCurrentRowEditing = editingCells?.some(
          (cell: any) => cell.rowIndex === rowIndex
        );

        if (isCurrentRowEditing) {
          return (
            <div style={{ marginTop: "0px" }}>
              <button
                style={buttonStyle}
                className={`action-button update save_${rowIndex}`}
                data-action={Constants.actions.update}>
                <FontAwesomeIcon icon={faFloppyDisk} />
              </button>
              <button
                style={buttonStyle}
                className={`action-button cancel cancel_${rowIndex}`}
                data-action={Constants.actions.cancel}>
                <FontAwesomeIcon icon={faBan} />
              </button>
            </div>
          );
        } else {
          return (
            <div style={{ marginTop: "0px" }}>
              <button
                style={buttonStyle}
                className={`action-button edit edit_${rowIndex}`}
                data-action={Constants.actions.edit}>
                <FontAwesomeIcon icon={faPencil} />
              </button>
              <button
                disabled={
                  allLoadList.length <= 1 || params.data.effectiveDate == null
                }
                style={{
                  ...buttonStyle,
                  opacity:
                    allLoadList.length <= 1 || params.data.effectiveDate == null
                      ? 0.5
                      : 1,
                  cursor:
                    allLoadList.length <= 1 || params.data.effectiveDate == null
                      ? "not-allowed"
                      : "pointer",
                }}
                className={`action-button delete delete_${rowIndex}`}
                data-action={Constants.actions.delete}>
                <FontAwesomeIcon icon={faTrash} />
              </button>
            </div>
          );
        }
      },
      editable: false,
      colId: "action",
    },
  ];

  const defaultColDef = React.useMemo(() => {
    return {
      flex: 1,
      minWidth: 150,
      resizable: true,
      floatingFilter: true,
      sortable: true,
      filter: true,
      suppressMenu: true,
      unSortIcon: true,
      filterParams: {
        applyMiniFilterWhileTyping: true,
      },
      enableValue: true,
      editable: true,
    };
  }, []);
  return {
    columnDefs,
    defaultColDef,
  };
};

export default DataFeedListGridDefs;
