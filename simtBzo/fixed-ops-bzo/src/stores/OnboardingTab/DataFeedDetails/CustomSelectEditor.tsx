import React, { useEffect, useState } from "react";

const CustomSelectEditor = (props: any) => {
  const [value, setValue] = useState(props.value);

  const values: string[] = props?.values || [];

  const onChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newVal = e.target.value;
    setValue(newVal);

    // Notify parent/grid
    if (props.onValueChange) {
      props.onValueChange(newVal, props); // pass new value + props back
    }

    //props.api.stopEditing(); // close editor & apply new value
  };

  useEffect(() => {
    setValue(props.value);
  }, [props.value]);

  return (
    <select
      value={value}
      onChange={onChange}
      autoFocus
      style={{ width: "100%", height: "100%" }}>
      {values.map((val) => (
        <option key={val} value={val}>
          {val}
        </option>
      ))}
    </select>
  );
};

export default CustomSelectEditor;
