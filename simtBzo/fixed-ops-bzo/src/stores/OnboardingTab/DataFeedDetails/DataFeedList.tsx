import { Box, Typography } from "@mui/material";
import DataFeedListGridDefs from "./DataFeedListGridDefs";
import { Constants } from "../../../utils/constants";
import { AgGridReact } from "ag-grid-react";
import useDataFeedDmsList from "../../../CustomHooks/useDataFeedDmsList";
import { useEffect } from "react";
import { Confirm } from "react-admin";
import { traceSpan } from "../../../utils/OTTTracing";

const DataFeedList = (props: any) => {
  const {
    record,
    setOpenSnackbar,
    setStatusMessage,
    getStoreQuery,
    getDataFeed,
    setStatusMessageType,
    allLoadList,
    getDataFeedDmsList,
  } = props;
  const {
    onRowEditingStopped,
    onRowEditingStarted,
    onGridReady,
    onCellClicked,
    gridApiRef,
    openDeleteModal,
    handleDialogClose,
    handleConfirm,
  }: any = useDataFeedDmsList({
    record,
    setOpenSnackbar,
    setStatusMessage,
    getStoreQuery,
    getDataFeed,
    setStatusMessageType,
    getDataFeedDmsList,
  });
  const { columnDefs, defaultColDef } = DataFeedListGridDefs({ allLoadList });
  const onFilterChanged = (e: any) => {
    const filterValues = e.api.getFilterModel();
    Object.keys(filterValues).forEach((colId) => {
      traceSpan(`filter_grid_${colId}`, {
        event: `filter_grid_${colId}`,
        pageUrl: window.location.pathname,
        timestamp: new Date().toISOString(),
        userId: localStorage.getItem("user") || "",
        column: colId,
        filterValue: JSON.stringify(filterValues[colId]),
      });
    });
  };
  const onSortChanged = (params: any) => {
    // Get column states and filter only sorted columns
    const sortModel = params.columnApi
      .getColumnState()
      .filter((col: any) => col.sort != null)
      .map((col: any) => ({
        colId: col.colId,
        sort: col.sort,
      }));

    sortModel.forEach((sortItem: { colId: string; sort: "asc" | "desc" }) => {
      traceSpan(`sort_grid_${sortItem.colId}`, {
        event: `sort_grid_${sortItem.colId}`,
        pageUrl: window.location.pathname,
        timestamp: new Date().toISOString(),
        userId: localStorage.getItem("user") || "",
        column: sortItem.colId,
        direction: sortItem.sort,
      });
    });
  };

  useEffect(() => {
    if (gridApiRef.current) {
      gridApiRef.current.setRowData(allLoadList); // Set updated data to the grid
    }
  }, [allLoadList]);
  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        justifyContent: "space-between",
        mt: 3,
        ml: 1,
      }}>
      <Typography
        sx={{
          fontSize: 16,
          color: "#003d6b",
          fontWeight: "bold",
        }}>
        All Data Feeds
      </Typography>
      <Box sx={{ marginTop: "20px" }}>
        <div className={Constants.ag_grid_theme} style={{ height: "30vh" }}>
          <AgGridReact
            onRowEditingStopped={onRowEditingStopped}
            onRowEditingStarted={onRowEditingStarted}
            onCellClicked={onCellClicked}
            editType="fullRow"
            suppressClickEdit={true}
            suppressColumnVirtualisation={true}
            columnDefs={columnDefs}
            suppressChangeDetection={true}
            onGridReady={onGridReady}
            rowData={allLoadList}
            suppressCellSelection={true}
            suppressRowClickSelection={true}
            defaultColDef={defaultColDef}
            tooltipShowDelay={0}
            onFilterChanged={onFilterChanged}
            onSortChanged={onSortChanged}
          />
        </div>
      </Box>
      <Confirm
        isOpen={openDeleteModal}
        // loading={isLoading}
        title={`Delete data feed`}
        content={"Are you sure you want to delete these details?"}
        onConfirm={handleConfirm}
        onClose={handleDialogClose}
      />
    </Box>
  );
};

export default DataFeedList;
