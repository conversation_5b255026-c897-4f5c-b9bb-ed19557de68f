import React, { useState, forwardRef, useImperative<PERSON>andle } from "react";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import moment from "moment";

const CustomDateEditor = forwardRef((props: any, ref) => {
  const [value, setValue] = useState<Date | null>(() => {
    if (!props.value) return null;
    const date = new Date(props.value);
    return isNaN(date.getTime()) ? null : date;
  });

  useImperativeHandle(ref, () => ({
    getValue() {
      return value ? moment(value).format("MM/DD/YYYY") : "";
    },
    isPopup() {
      return true; // AG Grid knows it's a popup
    },
    getPopupPosition() {
      return "over"; // makes it float over the cell
    },
  }));

  return (
    <div
      onClick={(e) => e.stopPropagation()} // prevent grid closing editor
    >
      <DatePicker
        selected={value}
        onChange={(date: Date | null) => setValue(date)}
        dateFormat="MM/dd/yyyy"
        placeholderText="MM/DD/YYYY"
        className="ag-input-field-input"
        autoFocus
        // 👇 this forces calendar to render outside the grid container
        portalId="root"
        popperPlacement="bottom-start"
      />
    </div>
  );
});

export default CustomDateEditor;
