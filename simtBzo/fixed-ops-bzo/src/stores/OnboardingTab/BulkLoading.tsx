import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>ir<PERSON>P<PERSON>ress,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Typo<PERSON>,
} from "@mui/material";
import React, { useEffect, useRef, useState } from "react";
import {
  Confirm,
  Form,
  SaveButton,
  TextInput,
  maxLength,
  required,
  useRecordContext,
} from "react-admin";
import useOnboarding from "../../CustomHooks/useOnboarding";
import { AdjustSharp, CheckCircle } from "@mui/icons-material";
import ErrorIcon from "@mui/icons-material/Error";
import BulkLoadStatusLog from "./BulkLoadStatusLog";
import { DatePicker, DatePickerProps, Space } from "antd";
import dayjs from "dayjs";
import useStoreCreate from "../../CustomHooks/useStoreCreate";
import SnackBarMessage from "../../components/SnackBarMessage";
import CancelIcon from "@mui/icons-material/Cancel";
import ForumIcon from "@mui/icons-material/Forum";
import moment from "moment";
import AddCommentIcon from "@mui/icons-material/AddComment";
import StoreMutations from "../../service/Mutations/storeMutations";
import Mutations from "../../service/mutations";

import DeleteIcon from "@mui/icons-material/Delete";
import MotionPhotosOffIcon from "@mui/icons-material/MotionPhotosOff";
import { Phase1Message } from "../../service/mutations";
import { traceSpan } from "../../utils/OTTTracing";

const BulkLoading = (props?: any) => {
  const { getStoreQuery } = props;
  const record = useRecordContext();
  const [selectedBillingDate, setSelectedBillingDate] = useState<any>(null);
  const [selectedDate, setSelectedDate] = useState<any>(null);
  const [showCommentBox, setShowCommentBox] = useState<boolean>(false);
  const [openConfirmDelete, setOpenConfirmDelete] = useState<boolean>(false);
  const [deleteComment, setDeleteComment] = useState<any>({});
  const [phase1FailMessage, setPhase1FailMessage] = useState<any>("");
  const [isPhase1VerifiedStatus, setIsPhase1VerifiedStatus] =
    useState<boolean>(true);
  const [isPhase2VerifiedStatus, setIsPhase2VerifiedStatus] =
    useState<boolean>(true);
  const [isOpcodeCategorizedStatus, setIsOpcodeCategorizedStatus] =
    useState<boolean>(true);
  const [isOpcodeVerifiedStatus, setIsOpcodeVerifiedStatus] =
    useState<boolean>(true);

  const {
    handleSubmit,
    updatingBilling,
    setUpdatingBilling,
    showBillingSaveButton,
    setShowBillingSaveButton,
    setOpenSnackbar,
    openSnackbar,
    statusMessage,
    setStatusMessage,
  } = useStoreCreate({ action: "billingdate", getStoreQuery: getStoreQuery });
  const { handleBulkLoad, getQaComments, comments } = useOnboarding({
    id: record.id,
    record: record,
    bulkStoreQuery: getStoreQuery,
  });
  console.log("rrrrrrrrrrrrrrrrr-----", record);
  const { InsertComment } = StoreMutations;
  const {
    phase1Validationclick,
    opcodeCategorizationClick,
    opcodeValidationClick,
    phase2ValidationClick,
  } = Mutations;
  const boxRef = useRef<any>(null);
  // show message if the phase 1 is failed
  // if (record.bulkPhase1 == "Failed") {
  //   Phase1Message(record)
  //     .then((res) => {
  //       setPhase1FailMessage(res.string);
  //     })
  //     .catch((error) => {
  //       console.log("error", error);
  //     });
  // }

  useEffect(() => {
    if (boxRef.current) {
      boxRef.current.scrollTop = boxRef.current.scrollHeight;
    }
  }, [comments]);
  const onDateChange: DatePickerProps["onChange"] = (date, dateString) => {
    traceSpan("select_billing_datepicker", {
      event: "select_billing_datepicker",
      pageUrl: window.location.pathname,
      timestamp: new Date().toISOString(),
      userId: localStorage.getItem("user") || "",
      value: dateString,
    });
    setSelectedBillingDate(date);
    setShowBillingSaveButton(true);
  };
  const updateEstimatedDate = () => {
    traceSpan(`click_estimateddate_button`, {
      event: `click_estimateddate_button`,
      pageUrl: window.location.pathname,
      timestamp: new Date().toISOString(),
      userId: localStorage.getItem("user") || "",
    });
    setUpdatingBilling(true);
    let submitdata = {
      ...record,
      billingDate: selectedBillingDate
        ? dayjs(selectedBillingDate).format("MM/DD/YYYY")
        : null,
    };
    handleSubmit(submitdata);
  };

  function StepIcon(props: any) {
    const { record } = props;
    return (
      <div>
        {record === "In Progress" ? (
          <AdjustSharp
            className="QontoStepIcon-activeIcon"
            style={{ color: "#f7b500" }}
            fontSize="large"
          />
        ) : record === "Completed" ? (
          <CheckCircle style={{ color: "#008000" }} fontSize="large" />
        ) : record === "Active" ? (
          <AdjustSharp
            className="QontoStepIcon-activeIcon"
            style={{ color: "#1976d2" }}
            fontSize="large"
          />
        ) : record === "Inactive" ? (
          <AdjustSharp
            className="QontoStepIcon-activeIcon"
            style={{ color: "#00000061" }}
            fontSize="large"
          />
        ) : record == false ? (
          <AdjustSharp
            className="QontoStepIcon-activeIcon"
            style={{ color: "#00000061" }}
            fontSize="large"
          />
        ) : record == true ? (
          <AdjustSharp
            className="QontoStepIcon-activeIcon"
            style={{ color: "#00000061" }}
            fontSize="large"
          />
        ) : record === "Failed" ? (
          <ErrorIcon style={{ color: "#d32f2f" }} fontSize="large" />
        ) : (
          <MotionPhotosOffIcon
            style={{ color: "#00000061" }}
            fontSize="large"
          />
        )}
      </div>
    );
  }

  useEffect(() => {
    record?.billingDate && setSelectedBillingDate(dayjs(record?.billingDate));
  }, [record]);

  const handleSubmitComment = (values: any, activity: string) => {
    InsertComment({ ...values, activity: activity }).then((res) => {
      getQaComments();
      setShowCommentBox(false);
      setDeleteComment({});
      setOpenConfirmDelete(false);
      setOpenSnackbar(true);
      setStatusMessage(res);
    });
  };
  useEffect(() => {
    getQaComments();
  }, []);
  const handleDialogClose = () => {
    traceSpan(`click_commentdeletecancel_button`, {
      event: `click_commentdeletecancel_button`,
      pageUrl: window.location.pathname,
      timestamp: new Date().toISOString(),
      userId: localStorage.getItem("user") || "",
    });
    setDeleteComment({});
    setOpenConfirmDelete(false);
  };
  const handleConfirm = () => {
    traceSpan(`click_deleteconfirm_button`, {
      event: `click_deleteconfirm_button`,
      pageUrl: window.location.pathname,
      timestamp: new Date().toISOString(),
      userId: localStorage.getItem("user") || "",
    });
    handleSubmitComment(deleteComment, "Delete");
  };
  return (
    <Box>
      <SnackBarMessage
        onClose={() => setOpenSnackbar(false)}
        open={openSnackbar}
        message={statusMessage}
      />
      <Box
        sx={{
          display: "flex",
          alignContent: "center",
          mt: 3,
          flexDirection: "column",
        }}>
        <Typography
          sx={{
            fontSize: 16,
            color: "#003d6b",
            fontWeight: "bold",
            // display: "flex",
            alignItems: "center",
            mb: "32px",
          }}>
          Bulk Data Load
        </Typography>
        <Stepper alternativeLabel sx={{ width: "100%" }} activeStep={2}>
          {/* ✅ PHASE 1 */}
          <Step completed={record.bulkPhase1 === "Completed"}>
            <Tooltip title={record.bulkPhase1}>
              <StepLabel
                StepIconComponent={() => (
                  <StepIcon record={record.bulkPhase1} />
                )}>
                <Button
                  onClick={() => {
                    traceSpan("click_phase1_button", {
                      event: "click_phase1_button",
                      pageUrl: window.location.pathname,
                      timestamp: new Date().toISOString(),
                      userId: localStorage.getItem("user") || "",
                    });
                    handleBulkLoad("bulk_phase_1", "In Progress");
                  }}
                  disabled={
                    record.bulkPhase1 !== "Active" &&
                    record.bulkPhase1 !== "Failed"
                  } // only allow if not completed yet
                  sx={{ "&:hover": { backgroundColor: "#C0DCF5" } }}>
                  PHASE 1 {record.bulkPhase1 === "Failed" && "Retry"}
                </Button>

                {record.bulkPhase1StartDate && (
                  <Typography sx={{ fontSize: "12px", color: "grey" }}>
                    Start Date:{" "}
                    {moment(record.bulkPhase1StartDate).format("MM/DD/YY")}
                  </Typography>
                )}
                {record.bulkPhase1CompletedDate && (
                  <Typography sx={{ fontSize: "12px", color: "grey" }}>
                    End Date:{" "}
                    {moment(record.bulkPhase1CompletedDate).format("MM/DD/YY")}
                  </Typography>
                )}
              </StepLabel>
            </Tooltip>
          </Step>

          {/* ✅ PHASE 1 VALIDATION */}
          <Step completed={record.isPhase1Verified === true}>
            <Tooltip
              title={
                record.isPhase1Verified === true
                  ? "Completed"
                  : isPhase1VerifiedStatus === false ||
                    (record.bulkPhase1 === "Completed" &&
                      record.isPhase1Verified === false &&
                      record.phase1VerifiedDate !== null)
                  ? "Failed"
                  : record.bulkPhase1 === "Completed" &&
                    record.isPhase1Verified === false &&
                    record.phase1VerifiedDate === null
                  ? "Active"
                  : "Inactive"
              }>
              <StepLabel
                StepIconComponent={() => (
                  <StepIcon
                    record={
                      record.isPhase1Verified === true
                        ? "Completed"
                        : isPhase1VerifiedStatus === false ||
                          (record.bulkPhase1 === "Completed" &&
                            record.isPhase1Verified === false &&
                            record.phase1VerifiedDate !== null)
                        ? "Failed"
                        : record.bulkPhase1 === "Completed" &&
                          record.isPhase1Verified === false &&
                          record.phase1VerifiedDate === null
                        ? "Active"
                        : "Inactive"
                    }
                  />
                )}>
                <Button
                  onClick={() => {
                    traceSpan("click_phase1_validation_button", {
                      event: "click_phase1_validation_button",
                      pageUrl: window.location.pathname,
                      timestamp: new Date().toISOString(),
                      userId: localStorage.getItem("user") || "",
                    });
                    phase1Validationclick(
                      record.dms,
                      record.tenantId,
                      record.storeId
                    )
                      .then((res: any) => {
                        if (res?.status == true) {
                          props.getStoreQuery();
                          setIsPhase1VerifiedStatus(true);
                        } else {
                          console.log("show error message --- ", res.message);
                          setIsPhase1VerifiedStatus(false);
                        }
                      })
                      .catch((err: any) => {
                        console.error("Phase 1 validation failed", err);
                      });
                  }}
                  disabled={
                    record.bulkPhase1 !== "Completed" ||
                    record.isPhase1Verified === true
                  } // unlock only after Phase 1 is completed
                  sx={{ "&:hover": { backgroundColor: "#C0DCF5" } }}>
                  PHASE 1 Validation{" "}
                  {(isPhase1VerifiedStatus === false ||
                    (record.bulkPhase1 === "Completed" &&
                      record.isPhase1Verified === false &&
                      record.phase1VerifiedDate !== null)) &&
                    "Retry"}
                </Button>
                {record.phase1VerifiedDate && (
                  <Typography sx={{ fontSize: "12px", color: "grey" }}>
                    Verified Date:{" "}
                    {moment(record.phase1VerifiedDate).format("MM/DD/YY")}
                  </Typography>
                )}
              </StepLabel>
            </Tooltip>
          </Step>

          {/* ✅ OPCODE CATEGORIZATION */}
          <Step completed={record.isOpcodeCategorized === true}>
            <Tooltip
              title={
                record.isOpcodeCategorized === true
                  ? "Completed"
                  : isOpcodeCategorizedStatus === false ||
                    (record.isPhase1Verified === true &&
                      record.isOpcodeCategorized === false &&
                      record.opcodeCategorizedDate !== null)
                  ? "Failed"
                  : record.isPhase1Verified === true &&
                    record.isOpcodeCategorized === false &&
                    record.opcodeCategorizedDate === null
                  ? "Active"
                  : "Inactive"
              }>
              <StepLabel
                StepIconComponent={() => (
                  <StepIcon
                    record={
                      record.isOpcodeCategorized === true
                        ? "Completed"
                        : isOpcodeCategorizedStatus === false ||
                          (record.isPhase1Verified === true &&
                            record.isOpcodeCategorized === false &&
                            record.opcodeCategorizedDate !== null)
                        ? "Failed"
                        : record.isPhase1Verified === true &&
                          record.isOpcodeCategorized === false &&
                          record.opcodeCategorizedDate === null
                        ? "Active"
                        : "Inactive"
                    }
                  />
                )}>
                <Button
                  onClick={() => {
                    traceSpan("click_opcode_categorization_button", {
                      event: "click_opcode_categorization_button",
                      pageUrl: window.location.pathname,
                      timestamp: new Date().toISOString(),
                      userId: localStorage.getItem("user") || "",
                    });
                    opcodeCategorizationClick(
                      record.dms,
                      record.tenantId,
                      record.storeId
                    )
                      .then((res: any) => {
                        if (res?.status == true) {
                          props.getStoreQuery();
                          setIsOpcodeCategorizedStatus(true);
                        } else {
                          console.log("show error message --- ", res.message);
                          setIsOpcodeCategorizedStatus(false);
                        }
                      })
                      .catch((err: any) => {
                        console.error("Opcode Categorization failed", err);
                      });
                  }}
                  disabled={
                    record.isPhase1Verified !== true ||
                    record.isOpcodeCategorized === true
                  } // unlock only after Phase 1 Validation is completed
                  sx={{ "&:hover": { backgroundColor: "#C0DCF5" } }}>
                  Opcode Categorization{" "}
                  {(isOpcodeCategorizedStatus === false ||
                    (record.isPhase1Verified === true &&
                      record.isOpcodeCategorized === false &&
                      record.opcodeCategorizedDate !== null)) &&
                    "Retry"}
                </Button>
                {record.opcodeCategorizedDate && (
                  <Typography sx={{ fontSize: "12px", color: "grey" }}>
                    Categorized Date:{" "}
                    {moment(record.opcodeCategorizedDate).format("MM/DD/YY")}
                  </Typography>
                )}
              </StepLabel>
            </Tooltip>
          </Step>

          {/* ✅ OPCODE VALIDATION */}
          <Step completed={record.isOpcodeVerified === true}>
            <Tooltip
              title={
                record.isOpcodeVerified === true
                  ? "Completed"
                  : // : isOpcodeVerifiedStatus === false ||
                  //   (record.isOpcodeCategorized === true &&
                  //     record.isOpcodeVerified === false)
                  // ? "Failed"
                  record.isOpcodeCategorized === true &&
                    record.isOpcodeVerified === false
                  ? "Active"
                  : "Inactive"
              }>
              <StepLabel
                StepIconComponent={() => (
                  <StepIcon
                    record={
                      record.isOpcodeVerified === true
                        ? "Completed"
                        : // : isOpcodeVerifiedStatus === false ||
                        //   (record.isOpcodeCategorized === true &&
                        //     record.isOpcodeVerified === false)
                        // ? "Failed"
                        record.isOpcodeCategorized === true &&
                          record.isOpcodeVerified === false
                        ? "Active"
                        : "Inactive"
                    }
                  />
                )}>
                <Button
                  onClick={() => {
                    traceSpan("click_opcode_validation_button", {
                      event: "click_opcode_validation_button",
                      pageUrl: window.location.pathname,
                      timestamp: new Date().toISOString(),
                      userId: localStorage.getItem("user") || "",
                    });
                    opcodeValidationClick(
                      record.dms,
                      record.tenantId,
                      record.storeId
                    )
                      .then((res: any) => {
                        if (res?.status == true) {
                          props.getStoreQuery();
                          setIsOpcodeVerifiedStatus(true);
                        } else {
                          console.log("show error message --- ", res.message);
                          setIsOpcodeVerifiedStatus(false);
                        }
                      })
                      .catch((err: any) => {
                        console.error("Opcode validation failed", err);
                      });
                  }}
                  disabled={
                    record.isOpcodeCategorized !== true ||
                    record.isOpcodeVerified === true
                  } // unlock only after Opcode Categorization is completed
                  sx={{ "&:hover": { backgroundColor: "#C0DCF5" } }}>
                  Opcode Validation{" "}
                  {/* {(isOpcodeVerifiedStatus === false ||
                    (record.isOpcodeCategorized === true &&
                      record.isOpcodeVerified === false)) &&
                    "Retry"} */}
                </Button>
              </StepLabel>
            </Tooltip>
          </Step>

          {/* ✅ PHASE 2 */}
          <Step completed={record.bulkPhase2 === "Completed"}>
            <Tooltip
              title={
                record.bulkPhase2 === "Completed"
                  ? "Completed"
                  : record.bulkPhase2 === "In Progress"
                  ? "In Progress"
                  : record.isOpcodeVerified === true
                  ? "Active"
                  : "Inactive"
              }>
              <StepLabel
                StepIconComponent={() => (
                  <StepIcon
                    record={
                      record.bulkPhase2 === "Completed"
                        ? "Completed"
                        : record.bulkPhase2 === "In Progress"
                        ? "In Progress"
                        : record.isOpcodeVerified === true
                        ? "Active"
                        : "Inactive"
                    }
                  />
                )}>
                <Button
                  onClick={() => {
                    traceSpan("click_phase2_button", {
                      event: "click_phase2_button",
                      pageUrl: window.location.pathname,
                      timestamp: new Date().toISOString(),
                      userId: localStorage.getItem("user") || "",
                    });
                    handleBulkLoad("bulk_phase_2", "In Progress");
                  }}
                  disabled={
                    record.isOpcodeVerified !== true ||
                    record.bulkPhase2 === "Completed" ||
                    record.bulkPhase2 === "In Progress"
                  } // unlock only after Opcode Validation is completed
                  sx={{ "&:hover": { backgroundColor: "#C0DCF5" } }}>
                  PHASE 2 {record.bulkPhase2 === "Failed" && "Retry"}
                </Button>
                {record.bulkPhase2StartDate && (
                  <Typography sx={{ fontSize: "12px", color: "grey" }}>
                    Start Date:{" "}
                    {moment(record.bulkPhase2StartDate).format("MM/DD/YY")}
                  </Typography>
                )}
                {record.bulkPhase2CompletedDate && (
                  <Typography sx={{ fontSize: "12px", color: "grey" }}>
                    End Date:{" "}
                    {moment(record.bulkPhase2CompletedDate).format("MM/DD/YY")}
                  </Typography>
                )}
              </StepLabel>
            </Tooltip>
          </Step>

          {/* ✅ PHASE 2 VALIDATION */}
          <Step completed={record.isPhase2Verified === true}>
            <Tooltip
              title={
                record.isPhase2Verified === true
                  ? "Completed"
                  : isPhase1VerifiedStatus === false ||
                    (record.bulkPhase2 === "Completed" &&
                      record.isPhase2Verified === false &&
                      record.phase2VerifiedDate !== null)
                  ? "Failed"
                  : record.bulkPhase2 === "Completed" &&
                    record.isPhase2Verified === false &&
                    record.phase2VerifiedDate === null
                  ? "Active"
                  : "Inactive"
              }>
              <StepLabel
                StepIconComponent={() => (
                  <StepIcon
                    record={
                      record.isPhase2Verified === true
                        ? "Completed"
                        : isPhase2VerifiedStatus === false ||
                          (record.bulkPhase2 === "Completed" &&
                            record.isPhase2Verified === false &&
                            record.phase2VerifiedDate !== null)
                        ? "Failed"
                        : record.bulkPhase2 === "Completed" &&
                          record.isPhase2Verified === false &&
                          record.phase2VerifiedDate === null
                        ? "Active"
                        : "Inactive"
                    }
                  />
                )}>
                <Button
                  onClick={() => {
                    traceSpan("click_phase2_validation_button", {
                      event: "click_phase2_validation_button",
                      pageUrl: window.location.pathname,
                      timestamp: new Date().toISOString(),
                      userId: localStorage.getItem("user") || "",
                    });
                    phase2ValidationClick(
                      record.dms,
                      record.tenantId,
                      record.storeId
                    )
                      .then((res: any) => {
                        if (res?.status == true) {
                          props.getStoreQuery();
                          setIsPhase2VerifiedStatus(true);
                        } else {
                          console.log("show error message --- ", res.message);
                          setIsPhase2VerifiedStatus(false);
                        }
                      })
                      .catch((err: any) => {
                        console.error("Phase2 validation failed", err);
                      });
                  }}
                  disabled={
                    record.bulkPhase2 !== "Completed" ||
                    record.isPhase2Verified === true
                  } // unlock only after Phase 2 is completed
                  sx={{ "&:hover": { backgroundColor: "#C0DCF5" } }}>
                  PHASE 2 Validation{" "}
                  {(isPhase2VerifiedStatus === false ||
                    (record.bulkPhase2 === "Completed" &&
                      record.isPhase2Verified === false &&
                      record.phase2VerifiedDate !== null)) &&
                    "Retry"}
                </Button>
                {record.phase2VerifiedDate && (
                  <Typography sx={{ fontSize: "12px", color: "grey" }}>
                    Verified Date:{" "}
                    {moment(record.phase2VerifiedDate).format("MM/DD/YY")}
                  </Typography>
                )}
              </StepLabel>
            </Tooltip>
          </Step>

          <Step>
            <Tooltip
              title={
                record.dailyLoad === "Completed"
                  ? "Completed"
                  : record.dailyLoad === "In Progress"
                  ? "In Progress"
                  : record.isPhase2Verified === true
                  ? "Active"
                  : "Inactive"
              }>
              <StepLabel
                StepIconComponent={() => (
                  <StepIcon
                    record={
                      record.dailyLoad === "Completed"
                        ? "Completed"
                        : record.dailyLoad === "In Progress"
                        ? "In Progress"
                        : record.isPhase2Verified === true
                        ? "Active"
                        : "Inactive"
                    }
                  />
                )}>
                <Button
                  disabled={
                    record.isPhase2Verified !== true ||
                    record.dailyLoad == "Completed" ||
                    record.dailyLoad == "In Progress"
                  }
                  onClick={() => {
                    traceSpan("click_dailyloadsetup_button", {
                      event: "click_dailyloadsetup_button",
                      pageUrl: window.location.pathname,
                      timestamp: new Date().toISOString(),
                      userId: localStorage.getItem("user") || "",
                    });
                    record.dailyLoad === "Completed"
                      ? handleBulkLoad("daily_load", "Disabled")
                      : handleBulkLoad("daily_load", "In Progress");
                  }}
                  sx={{
                    "&:hover": {
                      backgroundColor: "#C0DCF5",
                    },
                  }}>
                  {record.dailyLoad === "Completed" && "Disable"} Daily Load
                  Setup {record.dailyLoad === "Failed" && "Retry"}
                </Button>

                {record.dailyLoadStartDate && (
                  <Typography sx={{ fontSize: "12px", color: "grey" }}>
                    Start Date:{" "}
                    {moment(record.dailyLoadStartDate).format("MM/DD/YY")}
                  </Typography>
                )}
                {record.dailyLoadCompletedDate && (
                  <Typography sx={{ fontSize: "12px", color: "grey" }}>
                    End Date:{" "}
                    {moment(record.dailyLoadCompletedDate).format("MM/DD/YY")}
                  </Typography>
                )}
              </StepLabel>
            </Tooltip>
          </Step>
          <Step>
            <Tooltip
              title={
                record.nsQaValidation === "Completed"
                  ? "Completed"
                  : record.nsQaValidation === "In Progress"
                  ? "In Progress"
                  : record.dailyLoad === "Completed" &&
                    record.nsQaValidation === "Active"
                  ? "Active"
                  : "Inactive"
              }>
              <StepLabel
                StepIconComponent={() => (
                  <StepIcon
                    record={
                      record.nsQaValidation === "Completed"
                        ? "Completed"
                        : record.nsQaValidation === "In Progress"
                        ? "In Progress"
                        : record.dailyLoad === "Completed" &&
                          record.nsQaValidation === "Active"
                        ? "Active"
                        : "Inactive"
                    }
                  />
                )}>
                <Button
                  disabled={
                    record.dailyLoad !== "Completed" ||
                    record.nsQaValidation == "Completed" ||
                    record.nsQaValidation == "Inactive"
                  }
                  onClick={() => {
                    traceSpan(`click_nsqavalidation_button`, {
                      event: `click_nsqavalidation_button`,
                      pageUrl: window.location.pathname,
                      timestamp: new Date().toISOString(),
                      userId: localStorage.getItem("user") || "",
                    });
                    record.nsQaValidation === "Active"
                      ? handleBulkLoad("ns_qa_validation", "In Progress")
                      : handleBulkLoad("ns_qa_validation", "Completed");
                  }}
                  sx={{
                    "&:hover": {
                      backgroundColor: "#C0DCF5",
                    },
                  }}>
                  NS QA Validation
                </Button>
                {record.nsQaValidationStartDate && (
                  <Typography sx={{ fontSize: "12px", color: "grey" }}>
                    Start Date:{" "}
                    {moment(record.nsQaValidationStartDate).format("MM/DD/YY")}
                  </Typography>
                )}
                {record.nsQaValidationCompletedDate && (
                  <Typography sx={{ fontSize: "12px", color: "grey" }}>
                    End Date:{" "}
                    {moment(record.nsQaValidationCompletedDate).format(
                      "MM/DD/YY"
                    )}
                  </Typography>
                )}
              </StepLabel>
            </Tooltip>
          </Step>
          <Step>
            <Tooltip
              title={
                record.review === "Completed"
                  ? "Completed"
                  : record.review == "In Progress"
                  ? "In Progress"
                  : record.nsQaValidation === "Completed" &&
                    record.review == "Active"
                  ? "Active"
                  : "Inactive"
              }>
              <StepLabel
                StepIconComponent={() => (
                  <StepIcon
                    record={
                      record.review === "Completed"
                        ? "Completed"
                        : record.review == "In Progress"
                        ? "In Progress"
                        : record.nsQaValidation === "Completed" &&
                          record.review == "Active"
                        ? "Active"
                        : "Inactive"
                    }
                  />
                )}>
                <Button
                  disabled={
                    record.review == "Completed" ||
                    record.nsQaValidation !== "Completed" ||
                    record.review == "Inactive"
                  }
                  onClick={() => {
                    traceSpan(`click_review_button`, {
                      event: `click_review_button`,
                      pageUrl: window.location.pathname,
                      timestamp: new Date().toISOString(),
                      userId: localStorage.getItem("user") || "",
                    });
                    record.review === "Active"
                      ? handleBulkLoad("review", "In Progress")
                      : handleBulkLoad("review", "Completed");
                  }}
                  sx={{
                    "&:hover": {
                      backgroundColor: "#C0DCF5",
                    },
                  }}>
                  Review
                </Button>
                {record.reviewStartDate && (
                  <Typography sx={{ fontSize: "12px", color: "grey" }}>
                    Start Date:{" "}
                    {moment(record.reviewStartDate).format("MM/DD/YY")}
                  </Typography>
                )}
                {record.reviewCompletedDate && (
                  <Typography sx={{ fontSize: "12px", color: "grey" }}>
                    End Date:{" "}
                    {moment(record.reviewCompletedDate).format("MM/DD/YY")}
                  </Typography>
                )}
              </StepLabel>
            </Tooltip>
          </Step>
          <Step>
            <Tooltip
              title={
                record.storeLaunched === "Completed"
                  ? "Completed"
                  : record.review === "Completed" &&
                    record.storeLaunched == "Active"
                  ? "Active"
                  : "Inactive"
              }>
              <StepLabel
                StepIconComponent={() => (
                  <StepIcon
                    record={
                      record.storeLaunched === "Completed"
                        ? "Completed"
                        : record.review === "Completed" &&
                          record.storeLaunched == "Active"
                        ? "Active"
                        : "Inactive"
                    }
                  />
                )}>
                <Button
                  disabled={
                    record.storeLaunched == "Completed" ||
                    record.review !== "Completed" ||
                    record.storeLaunched == "Inactive"
                  }
                  onClick={() => {
                    traceSpan(`click_launchstore_button`, {
                      event: `click_launchstore_button`,
                      pageUrl: window.location.pathname,
                      timestamp: new Date().toISOString(),
                      userId: localStorage.getItem("user") || "",
                    });
                    handleBulkLoad("store_launched", "Completed");
                  }}
                  sx={{
                    "&:hover": {
                      backgroundColor: "#C0DCF5",
                    },
                  }}>
                  Launch Store
                </Button>
                {record.storeLaunchedDate && (
                  <Typography sx={{ fontSize: "12px", color: "grey" }}>
                    Launched Date:{" "}
                    {moment(record.storeLaunchedDate).format("MM/DD/YY")}
                  </Typography>
                )}
              </StepLabel>
            </Tooltip>
          </Step>
        </Stepper>
        <Box
          sx={{
            display: "flex",
            alignItems: "flex-end",
            marginBottom: "10px",
            height: "fit-content",
          }}>
          <Space direction="vertical" style={{ justifyContent: "flex-end" }}>
            <Box sx={{ display: "flex", flexDirection: "column" }}>
              <Typography
                sx={{
                  color: record.dailyLoad === "Completed" ? "#1976D2" : "grey",
                  fontSize: "12px",
                }}>
                Billing Date
              </Typography>
              <DatePicker
                disabled={record.dailyLoad !== "Completed"}
                onChange={onDateChange}
                placeholder="Select Date"
                format={"MM/DD/YYYY"}
                value={selectedBillingDate}
                color="primary"
                onFocus={() => {
                  traceSpan("billing_datepicker_focused", {
                    event: "billing_datepicker_focused",
                    pageUrl: window.location.pathname,
                    timestamp: new Date().toISOString(),
                    userId: localStorage.getItem("user") || "",
                  });
                }}
              />
            </Box>
          </Space>
          <Button
            disabled={!showBillingSaveButton}
            onClick={updateEstimatedDate}
            variant="contained"
            size="small"
            sx={{
              marginLeft: "16px",
              height: "fit-content",
            }}>
            {updatingBilling ? <CircularProgress size={24} /> : "Submit"}
          </Button>
        </Box>
      </Box>
      {/* {record.bulkPhase1 !== "Active" && ( */}
      <Box sx={{ mt: 3, width: "100%" }}>
        <Typography
          mb={2}
          sx={{ fontSize: 16, color: "#003d6b", fontWeight: "bold" }}>
          Comments ({comments.length})
        </Typography>
        <Box
          ref={boxRef}
          sx={{ maxHeight: "30vh", overflow: "auto", padding: "0 12px" }}>
          {comments.length !== 0 &&
            comments.map((comment: any) => {
              return (
                <Box
                  key={comment.createdAt}
                  sx={{ display: "flex", mb: 2, width: "100%" }}>
                  <Box sx={{ mr: 1 }}>
                    <ForumIcon sx={{ fontSize: 14, color: "grey" }} />
                  </Box>
                  <Box sx={{ width: "100%" }}>
                    <Box
                      sx={{ display: "flex", justifyContent: "space-between" }}>
                      <Typography sx={{ fontSize: 14, fontWeight: "bold" }}>
                        {comment.createdBy}
                      </Typography>
                      {/* <Box sx={{ display: "flex"}}> */}
                      <Typography
                        sx={{
                          fontSize: 12,
                          color: "grey",
                        }}>
                        {" "}
                        {moment(comment.createdAt).format("MM/DD/YYYY, h:mm a")}
                      </Typography>

                      {/* </Box> */}
                    </Box>
                    <Typography
                      sx={{
                        fontSize: 14,
                        textAlign: "justify",
                        wordBreak: "break-all",
                      }}>
                      {comment.commentText}
                    </Typography>
                  </Box>
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "flex-start",
                      padding: 0,
                      ml: 2,
                    }}>
                    <Tooltip title="Delete">
                      <IconButton
                        aria-label="delete"
                        onClick={() => {
                          traceSpan(`click_deletecomment_button`, {
                            event: `click_deletecomment_button`,
                            pageUrl: window.location.pathname,
                            timestamp: new Date().toISOString(),
                            userId: localStorage.getItem("user") || "",
                          });
                          setDeleteComment({
                            comment: comment.commentText,
                            inCreatedBy: comment.createdBy,
                            storeId: comment.storeId,
                            tenantId: comment.tenantId,
                          });
                          setOpenConfirmDelete(true);
                        }}>
                        <DeleteIcon />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </Box>
              );
            })}
        </Box>
        <Confirm
          isOpen={openConfirmDelete}
          //  loading={isLoading}
          title={`Delete comment`}
          content="Do you want to delete this comment?"
          onConfirm={handleConfirm}
          onClose={handleDialogClose}
        />
        {showCommentBox ? (
          <Box>
            <Form
              onSubmit={(values: any) => {
                traceSpan(`click_commentsave_button`, {
                  event: `click_commentsave_button`,
                  pageUrl: window.location.pathname,
                  timestamp: new Date().toISOString(),
                  userId: localStorage.getItem("user") || "",
                });
                handleSubmitComment(values, "Insert");
              }}>
              <TextInput
                source="comment"
                validate={[required(), maxLength(400)]}
                multiline
                rows={5}
                fullWidth
              />
              <SaveButton label="Save" />
              <Button
                color="primary"
                variant="contained"
                startIcon={<CancelIcon />}
                sx={{ m: 2 }}
                onClick={() => {
                  traceSpan(`click_commentcancel_button`, {
                    event: `click_commentcancel_button`,
                    pageUrl: window.location.pathname,
                    timestamp: new Date().toISOString(),
                    userId: localStorage.getItem("user") || "",
                  });
                  setShowCommentBox(false);
                }}>
                Cancel
              </Button>
            </Form>
          </Box>
        ) : (
          <Button
            color="primary"
            variant="contained"
            sx={{ m: "8px 0px", padding: "4px 10px" }}
            startIcon={<AddCommentIcon />}
            onClick={() => {
              traceSpan(`click_addcomment_button`, {
                event: `click_addcomment_button`,
                pageUrl: window.location.pathname,
                timestamp: new Date().toISOString(),
                userId: localStorage.getItem("user") || "",
              });
              setShowCommentBox(true);
            }}>
            Add
          </Button>
        )}
      </Box>
      {/*  )} */}
      {record.bulkPhase1 !== "Active" && (
        <Box sx={{ mt: 3 }}>
          <Typography
            mb={2}
            sx={{ fontSize: 16, color: "#003d6b", fontWeight: "bold" }}>
            Status Log
          </Typography>
          <BulkLoadStatusLog
            phase="bulk_phase_1"
            phaseStatus={record.bulkPhase1}
          />{" "}
          <BulkLoadStatusLog
            phase="bulk_phase_2"
            phaseStatus={record.bulkPhase2}
          />{" "}
          <BulkLoadStatusLog
            phase="daily_load"
            phaseStatus={record.dailyLoad}
          />{" "}
        </Box>
      )}
    </Box>
  );
};

export default BulkLoading;
