import { useState, ChangeEvent } from "react";
import { Al<PERSON>, Box, Tab, Tabs } from "@mui/material";
import { TabPanel } from "../../Layout/TabPanel";
import DataFeeds from "./DataFeedDetails/DataFeeds";
import OpcodesList from "./OpcodesDetails/OpcodesList";
import { useRecordContext, useTranslate } from "react-admin";
import PayTypes from "./PayTypes";
import BulkLoading from "./BulkLoading";
import DataEvaluation from "./DataEvaluation";
import { traceSpan } from "../../utils/OTTTracing";

export const OnBoarding = (props: any) => {
  const record = useRecordContext();
  const [tabValue, setTabValue] = useState(0);
  const translate = useTranslate();
  const handleTabChange = (event: ChangeEvent<{}>, newValue: number) => {
    const target = event.target as HTMLElement;
    const clickedTab = target.closest('[role="tab"]') as HTMLElement;
    const tabLabel = clickedTab?.textContent || clickedTab?.innerText || "";
    traceSpan(`click_${tabLabel}_tab`, {
      event: `click_${tabLabel}_tab`,
      pageUrl: window.location.pathname,
      timestamp: new Date().toISOString(),
      userId: localStorage.getItem("user") || "",
    });
    setTabValue(newValue);
  };
  const realmRole = JSON.parse(localStorage.getItem("role") || "");
  return (
    <Box>
      <Box>
        <Tabs
          value={tabValue}
          indicatorColor="primary"
          textColor="primary"
          onChange={handleTabChange}>
          <Tab label={translate("TABS.DATA_FEEDS")} />
          <Tab label={"Bulk Data Load"} />
          {realmRole === "superadmin" && (
            <Tab
              label={translate("Data Evaluation")}
              disabled={
                (record &&
                  record.infrastructureConfiguration !== "Completed") ||
                (record && record.bulkPhase1 !== "Completed")
              }
            />
          )}
          <Tab
            label={translate("TABS.OPCODES")}
            disabled={record && record.bulkPhase1 !== "Completed"}
          />
          <Tab
            label={translate("TABS.PAYTYPES")}
            disabled={
              (record &&
                record.dms === "CDK Global" &&
                record &&
                record.bulkPhase2 !== "Completed") ||
              (record && record.bulkPhase1 !== "Completed")
            }
          />
        </Tabs>
      </Box>
      <TabPanel value={tabValue} index={0}>
        {/* {record.infrastructureConfiguration !== "Completed" && (
          <Alert
            severity={
              record.infrastructureConfiguration === "Completed"
                ? "success"
                : record.infrastructureConfiguration === "In Progress"
                ? "info"
                : "warning"
            }
            sx={{ mt: 3 }}
          >
            Infrastructure configuration{" "}
            {record.infrastructureConfiguration.toLowerCase()}.
          </Alert>
        )} */}
        <DataFeeds
          getStoreQuery={props.getStoreQuery}
          dataFeedRecord={record}
        />
      </TabPanel>
      <TabPanel value={tabValue} index={1}>
        <BulkLoading getStoreQuery={props.getStoreQuery} />
      </TabPanel>
      {realmRole === "superadmin" && (
        <TabPanel value={tabValue} index={2}>
          <DataEvaluation />
        </TabPanel>
      )}
      <TabPanel value={tabValue} index={realmRole === "superadmin" ? 3 : 2}>
        <OpcodesList />
      </TabPanel>
      <TabPanel value={tabValue} index={realmRole === "superadmin" ? 4 : 3}>
        <PayTypes />
      </TabPanel>
    </Box>
  );
};
