import { ColDef } from "ag-grid-community";
import {
  ReactChild,
  ReactFragment,
  ReactPortal,
  useMemo,
  useState,
} from "react";
import { CommonColumnDefinition } from "../../../types";
import { RowDragCallbackParams } from "ag-grid-community";
import { Box, IconButton, Tooltip } from "@mui/material";
import { useTranslate } from "react-admin";
import { Constants } from "../../../utils/constants";
import {
  OpcodeMutation,
  getOpcodeCategoryStatus,
  getCategorizedOpcodeStatus,
  getOpcodeCounts,
  UpdateOpcodeCategoryChange,
} from "../../../service/mutations";
import {
  GetOpcodeQuery,
  GetOpcodeChoiceQuery,
} from "../../../service/dataFetchQueries";
import { Delete } from "@mui/icons-material";
import { traceSpan } from "../../../utils/OTTTracing";

const OpcodeGridDefs = (props: any) => {
  const translate = useTranslate();
  const { record } = props;
  const [rightApi, setRightApi] = useState<any>(null);
  const [leftApi, setLeftApi] = useState<any>(null);
  const [isSuccessful, setIsSuccessful] = useState<boolean>(true);
  const [departmentOptions, setDepartmentOptions] = useState<any>(null);
  const [opcategoryOptions, setOpcategoryOptions] = useState<any>(null);
  const [rowData, setRowData] = useState<any>([]);
  const [leftRowData, setLeftRowData] = useState<any>();
  const [rightRowData, setRightRowData] = useState<any>();
  const [loading, setLoading] = useState(true);
  const [showUncategorizedTable, setShowUncategorizedTable] = useState(false);
  const [statusMessage, setStatusMessage] = useState<any>("");
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [selectedDepartment, setSelectedDepartment] = useState("Service");
  const [opcodeCount, setOpcodeCount] = useState<any>({});

  const [opcodeCategorizationStatus, setOpcodeCategorizationStatus] =
    useState(false);
  const [categorizedOpcodeStatus, setCategorizedOpcodeStatus] = useState(true);
  const CustomTooltipCellRenderer = ({ value, colDef }: any) => {
    const isOpcodeColumn = colDef.field === "lbropcode";
    return (
      <div
        title={value}
        style={{ fontWeight: isOpcodeColumn ? "bold" : "normal" }}>
        {value}
      </div>
    );
  };
  const CustomDollarCellRenderer = ({ value }: any) => {
    if (value) {
      const formattedValue = new Intl.NumberFormat("en-US", {
        style: "currency",
        currency: "USD",
      }).format(value);

      return <div title={formattedValue}>{formattedValue}</div>;
    }
  };
  var rowDrag = function (params: RowDragCallbackParams) {
    // only rows that are NOT groups should be draggable
    return !params.node.group;
  };
  const DepartmentCellRenderer = (props: any) => {
    const handleClick = () => {
      traceSpan(`click_edit_icon`, {
      event: `click_edit_icon`,
      pageUrl: window.location.pathname,
      timestamp: new Date().toISOString(),
      userId:localStorage.getItem('user') || ''
    });
      props.api.startEditingCell({
        rowIndex: props.node.rowIndex!,
        colKey: props.column!.getId(),
      });
    };

    if (!props.node.group && props?.node?.data?.lbropcode) {
      return (
        <span>
          {!opcodeCategorizationStatus && (
            <Tooltip title={"Edit"}>
              <button style={{ height: "18px" }} onClick={handleClick}>
                ✎
              </button>
            </Tooltip>
          )}
          <span style={{ paddingLeft: "4px" }}>{props.value}</span>
        </span>
      );
    } else {
      return null;
    }
  };
  const SportRenderer = (props: any) => {
    const lbropcode = props?.node?.data?.lbropcode;
    return (
      !props.node.group &&
      lbropcode !== null && (
        <Box>
          <Tooltip title={translate("GENERAL.RESET_CATEGORY")}>
            <IconButton
              size="small"
              sx={{
                width: 9,
                height: 9,
              }}
              onClick={() => {
                traceSpan(`click_delete_button`, {
                  event: `click_delete_button`,
                  pageUrl: window.location.pathname,
                  timestamp: new Date().toISOString(),
                  userId:localStorage.getItem('user') || ''
                });
                resetCategory(props)}}>
              <Delete />
            </IconButton>
          </Tooltip>
        </Box>
      )
    );
  };

  const fetchOpcodeCategorizationStatus = async (data: any) => {
    try {
      const result = await getOpcodeCategoryStatus(
        record.tenantId,
        record.storeId
      );
      if (result && typeof result.string === "string") {
        setOpcodeCategorizationStatus(result.string === "true");
        setLoading(false);
        if (result.string === "true") {
          // setShowUncategorizedTable(false);
        } else {
          if (data.length > 0) {
            // setShowUncategorizedTable(true);
          } else {
            setShowUncategorizedTable(false);
          }
        }
      }
    } catch (error) {
      setLoading(false);
      console.error("Failed to fetch opcode categorization status:", error);
    }
  };
  const fetchCategorizationOpcodeStatus = async (
    dmsType: any,
    storeId?: any,
    tenantId?: any
  ) => {
    try {
      const result = await getCategorizedOpcodeStatus(
        dmsType,
        storeId,
        tenantId
      );
      if (result && typeof result.string === "string") {
        setCategorizedOpcodeStatus(result.string == "true" ? true : false);
      }
    } catch (error) {
      setLoading(false);
      console.error("Failed to fetch opcode categorization status:", error);
    }
  };
  const fetchOpcodeCounts = async (
    dmsType: any,
    storeId?: any,
    tenantId?: any
  ) => {
    try {
      const result = await getOpcodeCounts(dmsType, storeId, tenantId);
      if (result.results[0]) {
        setOpcodeCount(result.results[0]);
      }
    } catch (error) {
      setLoading(false);
      console.error("Failed to fetch opcode categorization status:", error);
    }
  };
  const fetchOpcodeChoice = async (pType: any) => {
    try {
      const result = await GetOpcodeChoiceQuery(pType).then((res) => {
        return res;
      });
      return result.map((item: any) => item.value);
    } catch (error) {
      console.log(error);
      return [];
    }
  };
  const fetchOpcodes = async () => {
    const fetchOnboardingOpcodeDataPromise = await GetOpcodeQuery(record).then(
      (res) => {
        return res;
      }
    );
    const fetchDepartmentChoicePromise = fetchOpcodeChoice("department");
    const fetchCategoryChoicePromise = fetchOpcodeChoice("opcategory");
    Promise.all([
      fetchOnboardingOpcodeDataPromise,
      fetchDepartmentChoicePromise,
      fetchCategoryChoicePromise,
    ])
      .then(async ([onboardingData, departmentChoice, categoryChoice]) => {
        onboardingData = onboardingData.map((item: any) => {
          return {
            ...item,
            lbropcodedesc: item?.lbropcodedesc?.replace(
              /customer states/gi,
              "CS"
            ),
          };
        });
        departmentChoice.unshift("");
        setDepartmentOptions(departmentChoice);
        setOpcategoryOptions(categoryChoice);
        setRowData(onboardingData);
        const tempNA = onboardingData.filter((item: any) => {
          return item.opcategory === "-----" || item.opcategory === "";
        });
        const tempOthers = onboardingData.filter((item: any) => {
          return item.opcategory !== "-----" && item.opcategory !== "";
        });
        console.log("tempppppppp---", tempNA);

        if (showUncategorizedTable === false) {
          try {
            const result = await UpdateOpcodeCategoryChange(
              localStorage.getItem("dmsType"),
              record.storeId,
              record.tenantId
            );
            if (result) {
              console.log("ttttt--t-t-t-t--t", result);
              setLeftRowData(result.results);
              // const statusMessage = translate(result?.string);
              // setStatusMessage(statusMessage);
              // setOpenSnackbar(true);
              // setCategorizedOpcodeStatus(true);
            }
          } catch (error) {
            const statusMessage = translate(
              "ERROR_MESSAGES.SOMETHING_WENT_WRONG"
            );
            setStatusMessage(statusMessage);
            setOpenSnackbar(true);
            console.log(error);
          }
        }
        // setLeftRowData(tempNA);
        // Check if any category is missing in the rightRowData
        const categoriesInRightTable = tempOthers.map(
          (item: any) => item.opcategory
        );
        const categoriesFromAPI = categoryChoice;
        const missingCategories = [...categoriesFromAPI].filter(
          (category) =>
            !categoriesInRightTable.includes(category) &&
            category !== "-----" &&
            category !== ""
        );
        // Create dummy rows for the missing categories and add them to rightRowData
        const dummyRows = missingCategories.map((category) => ({
          opcategory: category,
          id: category,
          lbropcode: null,
        }));
        const updatedRightRowData: any = [...tempOthers, ...dummyRows];
        console.log("vvvvvvvvvvvv----", updatedRightRowData);
        setRightRowData(updatedRightRowData);
        fetchOpcodeCategorizationStatus(tempNA);
        const dmsType = localStorage.getItem("dmsType")
          ? localStorage.getItem("dmsType")
          : "";
        const tenantId = record.tenantId;
        const storeId = record.storeId;
        fetchCategorizationOpcodeStatus(dmsType, storeId, tenantId);
        fetchOpcodeCounts(dmsType, storeId, tenantId);
      })
      .catch((error) => {
        setIsSuccessful(false);
        const statusMessage = translate("ERROR_MESSAGES.SOMETHING_WENT_WRONG");
        setStatusMessage(statusMessage);
        setOpenSnackbar(true);
        console.log(error);
      });
  };
  const handleRadioChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSelectedDepartment(event.target.value);
  };

  const generatePValData = (
    node: any,
    newopcategory: any,
    department: any,
    action?: any,
    oldDepartment?: any
  ) => ({
    p_action: Constants.actions.update,
    laboropcode: node?.data?.lbropcode,
    pay_type: node?.data?.lbrlabortype,
    oldopcategory:
      action == "reset" ? node.data.opcategory : Constants.nullText,
    newopcategory,
    olddepartment:
      action == "editCell"
        ? oldDepartment
        : node?.data?.department
        ? node?.data?.department
        : Constants.nullText,
    newdepartment:
      action == "dragDrop"
        ? department
        : node?.data?.department
        ? node?.data?.department
        : "Service",
  });

  const updateOpcodes = async (
    pValData: any,
    tenantId: any,
    storeId: any,
    Action: any,
    props?: any
  ) => {
    try {
      rightApi?.showLoadingOverlay();
      leftApi?.showLoadingOverlay();
      const result = await OpcodeMutation(pValData, tenantId, storeId);
      const opcodeName = pValData[0].laboropcode;
      const categoryName = pValData[0].newopcategory;
      const newDepartment = pValData[0].newdepartment;
      if (result.string === Constants.successCap) {
        setIsSuccessful(true);
        fetchOpcodes();
        const statusMessage =
          Action == "reset"
            ? translate("SUCCESS_MESSAGES.RESET_CATEGORY_MESSAGE", {
                opcodeName,
              })
            : Action == "editCell"
            ? translate("SUCCESS_MESSAGES.SET_DEPARTMENT_MESSAGE", {
                opcodeName,
                newDepartment,
              })
            : pValData.length > 1
            ? translate(
                "SUCCESS_MESSAGES.SET_CATEGORY_MESSAGE_FOR_MULTIPLE_OPCODE",
                { categoryName }
              )
            : translate("SUCCESS_MESSAGES.SET_CATEGORY_MESSAGE", {
                opcodeName,
                categoryName,
              });
        setStatusMessage(statusMessage);
        setOpenSnackbar(true);
      }
    } catch (error) {
      rightApi?.hideOverlay();
      leftApi?.hideOverlay();
      props?.api?.hideOverlay();
      setIsSuccessful(false);
      const statusMessage = translate("ERROR_MESSAGES.SOMETHING_WENT_WRONG");
      setStatusMessage(statusMessage);
      setOpenSnackbar(true);
      console.log(error);
    }
  };
  function resetCategory(props: any) {
    props?.api?.showLoadingOverlay();
    const pValData = [generatePValData(props.node, "-----", "", "reset")];
    updateOpcodes(pValData, record.tenantId, record.storeId, "reset", props);
  }

  const commonColumnDefs: ColDef<CommonColumnDefinition>[] = [
    {
      field: "cprocount",
      width: 70,
      minWidth: 70,
      headerName: "# of ROs",
      cellRendererFramework: CustomTooltipCellRenderer,
    },
    {
      field: "totalhours",
      width: 80,
      minWidth: 80,
      headerName: "Total Hours",
      cellRendererFramework: CustomTooltipCellRenderer,
    },
    {
      field: "retailqualhours",
      width: 80,
      minWidth: 80,
      headerName: "Hours / RO",
      cellRendererFramework: CustomTooltipCellRenderer,
    },
    {
      field: "totallabordollars",
      width: 75,
      minWidth: 75,
      headerName: "Total Labor $",
      cellRendererFramework: CustomDollarCellRenderer,
      sortable: true,
      comparator: function (valueA: any, valueB: any) {
        var numA = parseFloat(valueA);
        var numB = parseFloat(valueB);
        if (numA === numB) {
          return 0;
        } else if (numA < numB) {
          return -1;
        } else {
          return 1;
        }
      },
      sort: "desc",
    },
    {
      field: "retailqualsale",
      width: 100,
      minWidth: 100,
      headerName: "Labor $/RO",
      cellRendererFramework: CustomTooltipCellRenderer,
    },
    {
      field: "elr",
      width: 90,
      minWidth: 90,
      headerName: "ELR",
      cellRendererFramework: CustomDollarCellRenderer,
    },
    {
      field: "lbrlabortype",
      width: 90,
      minWidth: 90,
      headerName: "Pay Type",
      hide: true,
      cellRendererFramework: CustomTooltipCellRenderer,
    },
  ];
  const [leftColumnDefs, setLeftColumnDefs] = useState<ColDef[]>([
    {
      field: "lbropcode",
      width: 90,
      minWidth: 90,
      headerName: "Opcode",
      cellRendererFramework: CustomTooltipCellRenderer,
      pinned: "left",
      suppressColumnsToolPanel: true,
    },
    {
      field: "lbropcodedesc",
      width: 90,
      minWidth: 90,
      headerName: "Opcode Description",
      cellRendererFramework: CustomTooltipCellRenderer,
      pinned: "left",
    },
    {
      field: "additionalDescription",
      width: 90,
      minWidth: 90,
      headerName: "Additional Description",
      cellRendererFramework: CustomTooltipCellRenderer,
      pinned: "left",
    },
    ...commonColumnDefs,
  ]);

  const [rightColumnDefs, setRightColumnDefs] = useState<any>([
    {
      field: "opcategory",
      rowGroup: true,
      hide: true,
      headerName: "Category",
      suppressColumnsToolPanel: true,
      width: 130,
      minWidth: 130,
      suppressNavigable: true,
      sortable: false,
      filter: false,
      unSortIcon: false,
    },
    {
      field: "lbropcode",
      rowDrag: rowDrag,
      width: 90,
      minWidth: 90,
      headerName: "Opcode",
      hide: true,
      suppressColumnsToolPanel: true,
    },
    {
      field: "lbropcodedesc",
      width: 110,
      minWidth: 110,
      headerName: "Opcode Description",
      cellRendererFramework: CustomTooltipCellRenderer,
    },
    {
      field: "additionalDescription",
      width: 110,
      minWidth: 110,
      headerName: "Additional Description",
      cellRendererFramework: CustomTooltipCellRenderer,
    },
    {
      field: "department",
      width: 90,
      minWidth: 90,
      headerName: "Department",
      cellRendererFramework: CustomTooltipCellRenderer,
      cellRenderer: DepartmentCellRenderer,
      cellEditor: "agSelectCellEditor",
      cellEditorParams: {
        values: ["Service", "Body Shop"],
      },
    },
    ...commonColumnDefs,
    {
      suppressMenu: true,
      maxWidth: 60,
      cellRenderer: SportRenderer,
      suppressColumnsToolPanel: true,
      cellStyle: { border: "none" },
      suppressNavigable: true,
      sortable: false,
      filter: false,
      unSortIcon: false,
      pinned: "right",
    },
  ]);
  const autoGroupColumnDef = useMemo(() => {
    return {
      cellRendererSelector: (params: any) => {
        return { component: "agGroupCellRenderer" };
      },
      headerName: "Opcode",
      field: "lbropcode",
      rowGroup: true,
      cellRendererParams: {
        suppressCount: true,
      },
      cellStyle: (params: any) => {
        return {
          color: params.node.group && "black", // Custom text color
          fontWeight: params.node.group && "bold",
        }; // Bold text
      },
    };
  }, []);

  const defaultColDef = useMemo(() => {
    return {
      flex: 1,
      minWidth: 180,
      resizable: true,
      floatingFilter: true,
      sortable: true,
      filter: true,
      suppressMenu: true,
      unSortIcon: true,
      filterParams: {
        applyMiniFilterWhileTyping: true,
      },
      wrapHeaderText: true,
      autoHeaderHeight: true,
      // suppressRowDrag:!opcodeCategorizationStatus,
    };
  }, []);
  return {
    commonColumnDefs,
    leftColumnDefs,
    rightColumnDefs,
    defaultColDef,
    autoGroupColumnDef,
    showUncategorizedTable,
    opcodeCategorizationStatus,
    setShowUncategorizedTable,
    setLoading,
    fetchOpcodes,
    generatePValData,
    updateOpcodes,
    leftApi,
    rightApi,
    setLeftApi,
    setRightApi,
    leftRowData,
    setLeftRowData,
    rightRowData,
    setOpenSnackbar,
    openSnackbar,
    statusMessage,
    isSuccessful,
    setIsSuccessful,
    setStatusMessage,
    loading,
    rowData,
    selectedDepartment,
    setSelectedDepartment,
    handleRadioChange,
    categorizedOpcodeStatus,
    setCategorizedOpcodeStatus,
    opcodeCount,
  };
};

export default OpcodeGridDefs;
