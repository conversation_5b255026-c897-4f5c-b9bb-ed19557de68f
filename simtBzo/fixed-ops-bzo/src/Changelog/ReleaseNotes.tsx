import React, { useEffect, useState } from "react";
import { Divider, Grid, Typography } from "@material-ui/core";
import { makeStyles } from "@material-ui/core/styles";
import Markdown from "markdown-to-jsx";
import { Paper } from "@mui/material";

const useStyles = makeStyles((theme) => ({
  mainLabel: {
    display: "flex",
    color: "rgb(0, 61, 107)",
  },
  markdownText: {
    paddingLeft: 40,
    paddingTop: 30,
    paddingBottom: 20,
    fontFamily: [ "Roboto", "Montserrat", "Helvetica", "Arial", "sans-serif"].join(","),
    fontSize: 15,
  },
}));

const ReleaseNotes: React.FC = () => {
  const classes = useStyles();
  const [htmlStr, setHtmlStr] = useState<string>("");

  useEffect(() => {
    const readmePath = require("../docs/Changelog.md");

    fetch(readmePath.default)
      .then((response) => response.text())
      .then((text) => {
        setHtmlStr(text);
      });
  }, []);

  return (
    <div>
       <Paper square style={{ margin: 8, paddingTop: "6px" }}> 
       <Grid item xs={12} style={{ justifyContent: "left" }}>
          <Typography
            style={{ padding: 12, color: "#ee7600" }}
            variant="h6"
            className={classes.mainLabel}
          >
            Site Release Log
          </Typography>
          <Divider />
          <div className={classes.markdownText}>
            <Markdown
              children={htmlStr}
              options={{
                overrides: {
                  h1: {
                    component: "div",
                    props: {
                      style: {
                        color: "#ee7600",
                        padding: "10px 0px",
                        margin: "10px 0px",
                        fontSize: 14,
                        borderBottom: "1px solid #eee",
                        borderTop: "1px solid #eee",
                      },
                    },
                  },
                  h3: {
                    component: "div",
                    props: {
                      style: {
                        padding: "10px 0px",
                        fontSize: 16,
                        fontWeight: "bold",
                      },
                    },
                  },
                  Grid: {
                    component: "div",
                    props: { style: { display: "flex" } },
                  },
                  div: {
                    component: "div",
                    props: {
                      style: {
                        marginRight: 30,
                        fontSize: 13,
                        marginBottom: 10,
                      },
                    },
                  },
                  p: {
                    component: "div",
                    props: { style: { fontSize: 16, fontWeight: "bold" } },
                  },
                  body: {
                    component: "div",
                    props: {
                      style: { fontSize: 13, marginTop: 8, display: "flex" },
                    },
                  },
                  h4: {
                    component: "div",
                    props: {
                      style: {
                        fontSize: 13,
                        marginRight: 4,
                        fontWeight: "bold",
                      },
                    },
                  },
                },
              }}
            />
          </div>
        </Grid>
        </Paper>
    </div>
  );
};

export default ReleaseNotes;
