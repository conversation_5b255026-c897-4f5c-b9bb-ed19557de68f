import { Box } from "@mui/material";
import React from "react";
import { AgGridReact } from "ag-grid-react";
import { Constants } from "../../utils/constants";
import DataAsOfGridDefs from "./DataAsOfGridDefs";
import { traceSpan } from "../../utils/OTTTracing";

const DailyDataAsOf = () => {
  const {
    columnDefs,
    defaultColDef,
    onGridReady,
    allLoadList,
  } = DataAsOfGridDefs();

   const onFilterChanged = (e: any) => {
        const filterValues = e.api.getFilterModel();
        Object.keys(filterValues).forEach((colId) => {
          traceSpan(`filter_grid_${colId}`, {
            event: `filter_grid_${colId}`,
          pageUrl: window.location.pathname,
          timestamp: new Date().toISOString(),
          userId: localStorage.getItem("user") || "",
          column: colId,
          filterValue: JSON.stringify(filterValues[colId])
          });
        });
      };
    const onSortChanged = (params: any) => {
      // Get column states and filter only sorted columns
      const sortModel = params.columnApi.getColumnState()
        .filter((col: any) => col.sort != null)
        .map((col: any) => ({
          colId: col.colId,
          sort: col.sort
        }));
    
      sortModel.forEach((sortItem: { colId: string; sort: 'asc' | 'desc' }) => {
        traceSpan(`sort_grid_${sortItem.colId}`, {
        event: `sort_grid_${sortItem.colId}`,
        pageUrl: window.location.pathname,
        timestamp: new Date().toISOString(),
        userId: localStorage.getItem('user') || '',
        column: sortItem.colId,
        direction: sortItem.sort
        });
      });
    };

  return (
    <Box sx={{ paddingX: "10px", width: "100%" ,marginTop: "20px" }}>
      <div
        className={Constants.ag_grid_theme}
        style={{ height: "90vh", width: "100%" }}
      >
        <AgGridReact
          columnDefs={columnDefs}
          editType="fullRow"
          rowData={allLoadList}
          defaultColDef={defaultColDef}
          rowSelection="single"
          onGridReady={(params: any) => onGridReady(params)}
          singleClickEdit={true}
          suppressColumnVirtualisation={true}
          suppressChangeDetection={true}
          stopEditingWhenCellsLoseFocus={true}
          onFilterChanged={onFilterChanged}
          onSortChanged={onSortChanged}
        />
      </div>
    </Box>
  );
};

export default DailyDataAsOf;