import React from "react";
import { AgGridReact } from "ag-grid-react";
import {
  Box,
  Typography,
  Button,
  Paper,
  Divider,
  useTheme,
} from "@mui/material";
import PictureAsPdfIcon from "@mui/icons-material/PictureAsPdf";
import moment from "moment";
import jsPDF from "jspdf";
import autoTable from "jspdf-autotable";
import { ColDef } from "ag-grid-community";

import "ag-grid-community/styles/ag-grid.css";
import "ag-grid-community/styles/ag-theme-alpine.css";
import "./DetailedSummaryList.css";
import { traceSpan } from "../../utils/OTTTracing";

interface LoginAttempt {
  user_name: string;
  login_date: string[];
}

interface AgGridLoginTableProps {
  clientName: string;
  loginAttempts: LoginAttempt[];
  startDate: string;
  endDate: string;
}

// Vertical header showing day number (e.g., "01", "02", etc.)
const VerticalHeader: React.FC<{ value: string }> = ({ value }) => (
  <div
    style={{
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      height: "100%", // full height of header cell
      width: "100%",
      textAlign: "center",
      fontWeight: "600",
      fontSize: "12px",
      userSelect: "none",
      padding: 7,
      borderRight: "1px solid #e0e0e0",
    }}>
    {value}
  </div>
);

const DetailedSummaryList: React.FC<AgGridLoginTableProps> = ({
  clientName,
  loginAttempts,
  startDate,
  endDate,
}) => {
  const theme = useTheme();

  const generateDateColumns = (start: string, end: string): ColDef[] => {
    const columns: ColDef[] = [];
    const current = moment(start);
    const last = moment(end);

    while (current <= last) {
      // Format day as zero-padded two-digit number
      const formattedDate = current.format("DD");

      columns.push({
        headerName: formattedDate,
        field: formattedDate,
        width: 41,
        minWidth: 41,
        resizable: false,
        filter: false,
        sortable: false,
        tooltipField: formattedDate,
        headerComponentFramework: () => (
          <VerticalHeader value={formattedDate} />
        ),
        cellStyle: {
          borderRight: "1px solid #e0e0e0",
          padding: 0,
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "100%", // Ensure full height
          fontSize: "14px",
        },
        cellRenderer: (params: { value: any }) => (
          <span
            style={{
              color: params.value ? "#2e7d32" : "#999",
              userSelect: "none",
            }}>
            {params.value || "-"}
          </span>
        ),
      });

      current.add(1, "day");
    }
    return columns;
  };

  const dateColumns = generateDateColumns(startDate, endDate);

  const columnDefs: ColDef[] = [
    {
      headerName: "User Name",
      field: "user_name",
      pinned: "left",
      tooltipField: "user_name",
      cellStyle: {
        borderRight: "1px solid #e0e0e0",
        textAlign: "left",
        fontWeight: "normal",
        fontSize: "13px",
        paddingLeft: "8px",
      },
      width: 210,
      resizable: true,
    },
    ...dateColumns,
  ];

  const rowData =
    loginAttempts?.map((user) => {
      const row: { [key: string]: any } = { user_name: user.user_name };
      user.login_date.forEach((loginDate) => {
        const formattedDate = moment(loginDate).format("DD");
        row[formattedDate] = "✔️";
      });
      return row;
    }) || [];

  const exportPdf = () => {

    
traceSpan(`download_excel_monthlysummaryreport`, {
          event: `download_excel_monthlysummaryreport`,
          pageUrl: window.location.pathname,
          timestamp: new Date().toISOString(),
          userId: localStorage.getItem('user') || ''
        });
    const doc = new jsPDF({ orientation: "landscape" });

    doc.setFontSize(14);
    doc.text(`Login Report for ${clientName}`, 14, 20);

    doc.setFontSize(10);
    doc.text(
      `From: ${moment(startDate).format("DD/MM/YYYY")} To: ${moment(
        endDate
      ).format("DD/MM/YYYY")}`,
      14,
      28
    );

    const pdfColumns = [
      ...columnDefs.map((col) => ({
        header: col.headerName,
        dataKey: col.field,
      })),
      { header: "Count", dataKey: "Count" },
    ];

    const pdfRows = rowData.map((row) => {
      const pdfRow: { [key: string]: any } = {};
      let count = 0;

      pdfColumns.forEach((col) => {
        if (col.dataKey === "Count") return;

        let val = row[col.dataKey!] || "-";
        if (val === "✔️") {
          val = String.fromCharCode(51); // ZapfDingbats tick
          count++;
        }
        pdfRow[col.dataKey!] = val;
      });

      pdfRow["Count"] = count;
      return pdfRow;
    });

    autoTable(doc, {
      startY: 35,
      columns: pdfColumns,
      body: pdfRows,
      styles: {
        fontSize: 8,
        cellPadding: 2,
        halign: "center",
        valign: "middle",
        font: "helvetica",
        cellWidth: "wrap",
      },
      headStyles: {
        fillColor: [25, 118, 210],
        halign: "center",
        textColor: 255,
      },
      columnStyles: {
        user_name: { halign: "left" },
        Count: {
          halign: "center",
          fontStyle: "bold",
          minCellWidth: 25,
        },
      },
      margin: { left: 14, right: 14 },
      didParseCell: (data) => {
        const isTick = data.cell.raw === String.fromCharCode(51);
        const isCountCol = data.column.dataKey === "Count";
        if (isTick && !isCountCol) {
          data.cell.styles.font = "zapfdingbats";
          data.cell.styles.textColor = [0, 128, 0];
        }
      },
      didDrawPage: (data: any) => {
        const pageCount = doc.getNumberOfPages();
        doc.setFontSize(8);
        doc.setFont("helvetica");
        doc.text(
          `Page ${pageCount}`,
          data.settings.margin.left,
          doc.internal.pageSize.height - 10
        );
      },
    });

    doc.save(
      `Login_Report_${clientName}_${moment().format("YYYYMMDD_HHmmss")}.pdf`
    );
  };

  const onFilterChanged = (e: any) => {
      const filterValues = e.api.getFilterModel();
      Object.keys(filterValues).forEach((colId) => {
        traceSpan(`filter_grid_${colId}`, {
          event: `filter_grid_${colId}`,
          pageUrl: window.location.pathname,
          timestamp: new Date().toISOString(),
          userId: localStorage.getItem("user") || "",
          column: colId,
          filterValue: JSON.stringify(filterValues[colId])
        });
      });
    };
  const onSortChanged = (params: any) => {
    // Get column states and filter only sorted columns
    const sortModel = params.columnApi.getColumnState()
      .filter((col: any) => col.sort != null)
      .map((col: any) => ({
        colId: col.colId,
        sort: col.sort
      }));
  
    sortModel.forEach((sortItem: { colId: string; sort: 'asc' | 'desc' }) => {
      traceSpan(`sort_grid_${sortItem.colId}`, {
         event: `sort_grid_${sortItem.colId}`,
        pageUrl: window.location.pathname,
        timestamp: new Date().toISOString(),
        userId: localStorage.getItem("user") || "",
        column: sortItem.colId,
        direction: sortItem.sort
      });
    });
  };

  return (
    <Box mt={1} px={2}>
      <Paper
        elevation={3}
        sx={{
          p: 3,
          borderRadius: 2,
          backgroundColor: "#ffffff",
        }}>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography fontSize="18" color="primary" fontWeight="bold"
           onClick={(event) => {
            traceSpan('click_client_name', {
              event: 'click_client_name',
              pageUrl: window.location.pathname,
              timestamp: new Date().toISOString(),
              userId: localStorage.getItem('user') || '',
              clientName: clientName,
            });
           }}
          >
            {clientName}
          </Typography>

          {loginAttempts?.length > 0 && (
            <Button
              variant="contained"
              startIcon={<PictureAsPdfIcon />}
              size="small"
              onClick={exportPdf}
              sx={{
                textTransform: "none",
                borderRadius: 2,
                fontWeight: 600,
                background: "#1976d2",
                "&:hover": {
                  background: "#1565c0",
                },
              }}>
              Download PDF
            </Button>
          )}
        </Box>

        <Divider sx={{ my: 2 }} />

        {loginAttempts?.length > 0 ? (
          <Box
            className="ag-theme-alpine"
            sx={{
              width: "100%",
              border: "1px solid #e0e0e0",
              borderRadius: 2,
              overflowX: "auto",
              overflowY: "hidden",
            }}>
            <Box
              sx={{
                minWidth: `${150 + dateColumns.length * 40}px`,
                overflow: "visible",
              }}>
              <AgGridReact
                rowHeight={20}
                headerHeight={30} // Make space for vertical day numbers
                rowData={rowData}
                columnDefs={columnDefs}
                defaultColDef={{
                  sortable: true,
                  resizable: true,
                  filter: true,
                  suppressMenu: true,
                  cellStyle: { borderRight: "1px solid #e0e0e0" },
                }}
                suppressRowClickSelection
                rowMultiSelectWithClick={false}
                domLayout="autoHeight"
                onFilterChanged={onFilterChanged}
                onSortChanged={onSortChanged}
              />
            </Box>
          </Box>
        ) : (
          <Box
            sx={{
              p: 2,
              fontSize: "13px",
              borderRadius: 1,
              backgroundColor: "#f9f9f9",
              border: "1px solid lightgrey",
              mt: 1,
            }}>
            No login data available.
          </Box>
        )}
      </Paper>
    </Box>
  );
};

export default DetailedSummaryList;
