import { Box, Typography, Tab, Tabs, Select, MenuItem } from "@mui/material";
import { useEffect, useState } from "react";
import CustomLinearProgress from "../../components/CustomLinearProgress";
import CircleIcon from "@mui/icons-material/Circle";
import DataFetchQueries from "../../service/dataFetchQueries";
import Mutations from "../../service/mutations";
import moment from "moment";
import { TabPanel } from "../../Layout/TabPanel";
import AgGridLoginTable from "./DetailedSummaryList";
import AgGridLoginSummaryTable from "./LoginSummaryList";
import { Button, DatePicker, Form } from "antd";
import dayjs from "dayjs";
import { RangePickerProps } from "antd/es/date-picker";
import { traceSpan } from "../../utils/OTTTracing";

const { RangePicker } = DatePicker;

const formItemLayout = {
  labelCol: { xs: { span: 24 }, sm: { span: 6 } },
  wrapperCol: { xs: { span: 24 }, sm: { span: 14 } },
};

const DailyLogin = () => {
  const { GetDailyLogins } = DataFetchQueries;
  const { getDetailedLoginList, getDetailedLoginSummary } = Mutations;

  const [loginsList, setLoginsList] = useState<any[]>([]);
  const [detailedLoginsList, setDetailedLoginsList] = useState([]);
  const [loginSummary, setLoginSummary] = useState([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [tabValue, setTabValue] = useState(0);
  const [selectedMonth, setSelectedMonth] = useState(
    moment().format("MMMM - YYYY")
  );

  const [startDate, setStartDate] = useState<any>(dayjs().subtract(1, "month"));
  const [endDate, setEndDate] = useState<any>(dayjs());

  const monthOptions = Array.from({ length: 12 }).map((_, index) =>
    moment().subtract(index, "months").format("MMMM - YYYY")
  );

  var selectedMoment = moment(selectedMonth, "MMMM - YYYY");

  const [fromDate, setFromDate] = useState<any>(
    selectedMoment.startOf("month").format("YYYY-MM-DD")
  );
  const [toDate, setTodate] = useState<any>(
    selectedMoment.endOf("month").format("YYYY-MM-DD")
  );

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    const target = event.target as HTMLElement;
    const clickedTab = target.closest('[role="tab"]') as HTMLElement;
    const tabLabel = clickedTab?.textContent || clickedTab?.innerText || '';
    traceSpan(`click_${tabLabel}_tab`, {
              event: `click_${tabLabel}_tab`,
              pageUrl: window.location.pathname,
              timestamp: new Date().toISOString(),
              userId:localStorage.getItem('user') || ''
            });
    setTabValue(newValue);
    setSelectedMonth(moment().format("MMMM - YYYY"));
    const from = selectedMoment.startOf("month").format("YYYY-MM-DD");
    const to = selectedMoment.endOf("month").format("YYYY-MM-DD");
    setFromDate(from);
    setTodate(to);
    const start = dayjs().subtract(1, "month").startOf("day");
    const end = dayjs().endOf("day");
    setStartDate(start);
    setEndDate(end);
    if (newValue === 1) {
      fetchDetailedLoginListData(from, to);
    } else if (newValue === 2) {
      fetchLoginSummary(start, end);
    }
  };

  const onDateChange: RangePickerProps["onChange"] = (
    dates: any,
    _dateStrings: [string, string]
  ) => {
    if (dates && dates?.length === 2) {
      const start = dates[0].startOf("day");
      const end = dates[1].endOf("day");
      setStartDate(start);
      setEndDate(end);
    }
  };

  const fetchLoginSummary = async (start: any, end: any) => {
    try {
      setLoading(true);
      const responseSummary = await getDetailedLoginSummary();
      setLoginSummary(responseSummary);
    } catch (error) {
      console.error("Error fetching login summary:", error);
    } finally {
      setLoading(false);
    }
  };

  const fetchDetailedLoginListData = async (from: string, to: string) => {
    try {
      setLoading(true);
      const response = await getDetailedLoginList(from, to);
      setDetailedLoginsList(response);
    } catch (error) {
      console.error("Error fetching detailed login list:", error);
    } finally {
      setLoading(false);
    }
  };

  const onFinish = () => {
    fetchLoginSummary(startDate, endDate);
  };

  const getDailyLoadList = async () => {
    setLoading(true);
    GetDailyLogins()
      .then((res: any) => {
        setLoginsList(res);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  useEffect(() => {
    getDailyLoadList();
  }, []);

  useEffect(() => {
    if (tabValue == 1) {
      const from = selectedMoment.startOf("month").format("YYYY-MM-DD");
      const to = selectedMoment.endOf("month").format("YYYY-MM-DD");
      setFromDate(from);
      setTodate(to);
      fetchDetailedLoginListData(from, to);
    }
  }, [selectedMonth]);

  const renderLoginContent = () => (
    <Box sx={{ display: "flex", flexDirection: "column", gap: "12px" }}>
      <Box sx={{ display: "flex", justifyContent: "center" }}>
        <Typography sx={{ fontWeight: "bold", fontSize: "20px" }}>
          Daily Login Details :{" "}
          {moment().subtract(1, "days").format("MM-DD-YYYY")}
        </Typography>
      </Box>
      {!loading ? (
        loginsList?.map((item: any, idx: number) => (
          <Box key={idx} mb={2} sx={{ px: "10px" }}>
            <Typography sx={{ fontWeight: "bold", fontSize: "18px" }}>
              {item.client_name}
            </Typography>
            {item.total_logins > 0 ? (
              <>
                <Typography
                  sx={{ fontWeight: "bold", fontSize: "14px", color: "grey" }}>
                  Total Logins: {item.total_logins}
                </Typography>
                <Box
                  sx={{
                    width: "100%",
                    mt: "4px",
                    border: "1px lightgrey solid",
                    display: "flex",
                    flexDirection: "column",
                  }}>
                  <Box
                    sx={{
                      border: "0.5px black solid",
                      backgroundColor: "rgb(171, 64, 0)",
                      height: "30px",
                      pl: "10px",
                      display: "flex",
                      alignItems: "center",
                    }}>
                    <Typography
                      sx={{
                        fontWeight: "bold",
                        fontSize: "14px",
                        color: "white",
                      }}>
                      Users
                    </Typography>
                  </Box>
                  <Box sx={{ border: "1px lightgrey solid", p: "5px 10px" }}>
                    {item.login_user_name.map((user: any, idx2: number) => (
                      <Typography key={idx2} sx={{ fontSize: "12px" }}>
                        <CircleIcon
                          sx={{ fontSize: "6px", color: "#003d6b", mr: "8px" }}
                        />
                        {user}
                      </Typography>
                    ))}
                  </Box>
                </Box>
              </>
            ) : (
              <Box
                sx={{
                  width: "100%",
                  border: "1px lightgrey solid",
                  p: "5px 10px",
                  fontSize: "12px",
                  mt: "4px",
                }}>
                No logins found
              </Box>
            )}
          </Box>
        ))
      ) : (
        <Box sx={{ display: "flex", justifyContent: "center" }}>
          <CustomLinearProgress />
        </Box>
      )}
    </Box>
  );

  const renderDetailedSummary = () => (
    <div className="detailsummary">
      <Box sx={{ display: "flex", flexDirection: "column" }}>
        <Box
          sx={{ display: "flex", justifyContent: "flex-start", px: 2, mt: 2 }}>
          <span
            style={{
              color: "#003D6B",
              fontWeight: "bold",
              marginTop: "5px",
              marginLeft: "12px",
            }}>
            Month :
          </span>
          <Select
            value={selectedMonth}
            onChange={(e) => setSelectedMonth(e.target.value)}
            variant="outlined"
            size="small"
            sx={{ minWidth: 320, marginLeft: "10px" }}>
            {monthOptions.map((month, idx) => (
              <MenuItem key={idx} value={month}>
                {month}
              </MenuItem>
            ))}
          </Select>
        </Box>

        {!loading ? (
          Array.isArray(detailedLoginsList) && detailedLoginsList.length > 0 ? (
            detailedLoginsList.map((item: any, idx: number) => (
              <Box key={idx}>
                <AgGridLoginTable
                  clientName={item.client_name}
                  loginAttempts={item.login_attempts}
                  startDate={fromDate}
                  endDate={toDate}
                />
              </Box>
            ))
          ) : (
            <Typography sx={{ px: 2 }}>No login data found.</Typography>
          )
        ) : (
          <Box sx={{ display: "flex", justifyContent: "center" }}>
            <CustomLinearProgress />
          </Box>
        )}
      </Box>
    </div>
  );

  const renderLoginSummary = () => (
    <div className="loginsummary">
      <Box sx={{ display: "flex", flexDirection: "column", mt: 2 }}>
        <Box sx={{ display: "flex", flexDirection: "column", gap: "12px" }}>
          {!loading ? (
            Array.isArray(loginSummary) && loginSummary.length > 0 ? (
              loginSummary.map((item: any, idx: number) => (
                <AgGridLoginSummaryTable
                  key={idx}
                  clientName={item.display_name}
                  loginAttempts={item.login_attempts}
                />
              ))
            ) : (
              <Typography sx={{ px: 2 }}>No login data found.</Typography>
            )
          ) : (
            <Box sx={{ display: "flex", justifyContent: "center" }}>
              <CustomLinearProgress />
            </Box>
          )}
        </Box>
      </Box>
    </div>
  );

  return (
    <Box sx={{ m: "20px 16px 0px" }}>
      <Tabs
        value={tabValue}
        onChange={handleTabChange}
        indicatorColor="primary"
        textColor="primary"
        sx={{
          "& .MuiTab-root": {
            fontWeight: "bold",
            fontSize: "14px",
            textTransform: "none",
            px: 3,
            py: 1.5,
            borderBottom: "2px solid transparent",
            cursor: "pointer",
            "&:hover": {
              color: "#1976d2",
              backgroundColor: "#f5f5f5",
            },
          },
          "& .Mui-selected": {
            color: "#1976d2",
            borderBottom: "2px solid #1976d2",
            backgroundColor: "#e3f2fd",
          },
        }}>
        <Tab label="Daily Logins" />
        <Tab label="Monthly Summary" />
        <Tab label="Login Summary" />
      </Tabs>

      <TabPanel value={tabValue} index={0}>
        {renderLoginContent()}
      </TabPanel>
      <TabPanel value={tabValue} index={1}>
        {renderDetailedSummary()}
      </TabPanel>
      <TabPanel value={tabValue} index={2}>
        {renderLoginSummary()}
      </TabPanel>
    </Box>
  );
};

export default DailyLogin;
