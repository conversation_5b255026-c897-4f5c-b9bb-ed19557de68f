import {
  Box,
  Card,
  CardContent,
  CircularProgress,
  Typography,
} from "@mui/material";
import { useEffect, useState } from "react";
import DataFetchQueries from "../service/dataFetchQueries";
import { Link as RouterLink } from "react-router-dom";

const ErrorStatuses = (props: any) => {
  const { eventType } = props;
  const { GetGeneralMainErrorStatuses, GetGeneralNeverErrorStatuses } =
    DataFetchQueries;
  const [issues, setIssues] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isHovered, setIsHovered] = useState("");
  useEffect(() => {
    const fetchIssues = async () => {
      setIsLoading(true);
      try {
        const res =
          eventType === "never"
            ? await GetGeneralNeverErrorStatuses()
            : await GetGeneralMainErrorStatuses();
        setIssues(res);
      } finally {
        setIsLoading(false);
      }
    };
    fetchIssues();
  }, [eventType]);

  return (
    <Box>
      <Box
        sx={{
          padding: "10px",
          marginTop: "30px",
        }}
      >
        {isLoading ? (
          <CircularProgress
            size={24}
            sx={{
              position: "absolute",
              top: "50%",
              left: "50%",
              marginTop: "-12px",
              marginLeft: "-12px",
            }}
          />
        ) : issues.length > 0 ? (
          <Box
            display="flex"
            flexDirection={"row"}
            flexWrap="wrap"
            width="90%"
            gap={2}
          >
            {issues.map((item: any) => (
              <Card
                key={item.clientId}
                style={{ textDecoration: "none" }}
                component={RouterLink}
                to={`/NeverEvents/${item.clientName.replace(/ /g, "")}`}
                state={{
                  clientId: item.clientId,
                  clientName: item.clientName,
                  eventType: eventType,
                }} // Pass clientId as state
                onMouseOver={() => setIsHovered(item.clientId)}
                onMouseOut={() => setIsHovered("")}
                elevation={isHovered === item.clientId ? 10 : 3}
                raised
                sx={{
                  width: "15rem",
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  mr: "2%",
                  backgroundColor:
                    isHovered === item.clientId ? "#e2eefe" : "white",
                }}
              >
                <CardContent
                  style={{
                    paddingBottom: "5px",
                  }}
                  sx={{
                    width: "90%",
                    height: "90%",
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    padding: "5px",
                    paddingTop: "5px",
                  }}
                >
                  <Box
                    sx={{
                      display: "flex",
                      justifyContent: "flex-start",
                      alignItems: "center",
                    }}
                  >
                    <Typography
                      sx={{
                        fontWeight: "bold",
                        fontSize: "14px",
                        color: "red",
                      }}
                    >
                      {item.clientName}
                    </Typography>
                  </Box>
                  <Typography
                    sx={{
                      fontWeight: "bold",
                      fontSize: "1rem",
                      color: "red",
                    }}
                  >
                    {item.errCount}
                  </Typography>
                </CardContent>
              </Card>
            ))}
          </Box>
        ) : (
          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              width: "90%",
              alignItems: "center",
            }}
          >
            <Typography
              variant="h5"
              fontWeight={"bold"}
              color={"#003d6b"}
              mb="10px"
            >
              No issues found
            </Typography>
          </Box>
        )}
      </Box>
    </Box>
  );
};

export default ErrorStatuses;
