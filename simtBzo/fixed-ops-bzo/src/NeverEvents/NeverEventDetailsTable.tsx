import { Box, Card, CardContent, Grid, Typography } from "@mui/material";
import { useState } from "react";
import * as React from "react";
import DataFetchQueries from "../service/dataFetchQueries";
import { AgGridReact } from "ag-grid-react";
import { ColDef } from "ag-grid-community";
import { useLocation } from "react-router-dom";
import BackButton from "../Layout/BackButton";
import { Link } from "@mui/material";
import OpenInNewOutlinedIcon from "@mui/icons-material/OpenInNewOutlined";
import { Constants } from "../utils/constants";
import { traceSpan } from "../utils/OTTTracing";

const NeverEventsDetailsTable = () => {
  const { GetGeneralMainNeverEvents, GetGeneralNeverEvents } = DataFetchQueries;
  const [allNeverEvents, setAllNeverEvents] = useState<any>([]);
  const [issueCounts, setIssueCounts] = useState<any>([]);
  const location = useLocation();
  const { clientId, clientName, eventType } = location.state;
  const columnDefs: ColDef[] = [
    {
      headerName: "Tenant Name",
      field: "clientname",
      flex: 3,
    },
    {
      headerName: "Store Name",
      field: "storeName",
      flex: 3,
      wrapText: true,
      autoHeight: true,
    },
    {
      headerName: "Events",
      field: "optionDisplayName",
      flex: 4,
    },
    {
      headerName: "Issue Count",
      field: "statusData",
      flex: 1,
      cellStyle: {
        color: "red",
        fontWeight: "bold",
        textAlign: "right",
      },
      wrapText: false,
      autoHeight: false,
      cellRenderer: (params: any) => {
        let OPurl: any = allNeverEvents[0].subDomain;
        return (
          <>
            {params.value}
            <Link
              href={`https://${OPurl}/`}
              target="_blank"
              rel="noopener noreferrer"
              underline="hover"
            >
              <OpenInNewOutlinedIcon
                htmlColor="rgb(0, 61, 107)"
                style={{ fontSize: "12px", marginLeft: "3px" }}
              />
            </Link>
          </>
        );
      },
    },
  ];
  const defaultColDef = React.useMemo(() => {
    return {
      // cellStyle: { fontSize: "14px" },
      resizable: true,
      suppressMenu: true,
      unSortIcon: true,
      enableValue: true,
      sortable: true,
      floatingFilter: true,
      filter: true,
      wrapText: true,
      autoHeight: true,
    };
  }, []);

  const onGridReady = (params: any) => {
    params?.api?.showLoadingOverlay();
    eventType === "never"
      ? GetGeneralNeverEvents(clientId).then((res: any) => {
          const result: any[] = Object.values(
            res.reduce((acc: any, { optionDisplayName, statusData }: any) => {
              acc[optionDisplayName] = acc[optionDisplayName] || {
                optionDisplayName,
                count: "0",
              };
              acc[optionDisplayName].count = String(
                Number(acc[optionDisplayName].count) + Number(statusData)
              );
              return acc;
            }, {})
          );
          setIssueCounts(result);
          setAllNeverEvents(res);
          params?.api?.hideOverlay();
        })
      : GetGeneralMainNeverEvents(clientId).then((res: any) => {
          const result: any[] = Object.values(
            res.reduce((acc: any, { optionDisplayName, statusData }: any) => {
              acc[optionDisplayName] = acc[optionDisplayName] || {
                optionDisplayName,
                count: "0",
              };
              acc[optionDisplayName].count = String(
                Number(acc[optionDisplayName].count) + Number(statusData)
              );
              return acc;
            }, {})
          );
          setIssueCounts(result);
          setAllNeverEvents(res);
          params?.api?.hideOverlay();
        });
  };
  const getGridWrapper = () => (
    <div  className={Constants.ag_grid_theme} style={{ height: "72vh", width: "81vw" }}>
      <AgGridReact
        columnDefs={columnDefs}
        editType="fullRow"
        rowData={allNeverEvents}
        defaultColDef={defaultColDef}
        rowSelection="single"
        onGridReady={(params: any) => onGridReady(params)}
        // rowHeight={30}
        singleClickEdit={true}
        suppressColumnVirtualisation={true}
        suppressChangeDetection={true}
        stopEditingWhenCellsLoseFocus={true}
      />
    </div>
  );
  const onFilterChanged = (e: any) => {
      const filterValues = e.api.getFilterModel();
      Object.keys(filterValues).forEach((colId) => {
        traceSpan(`filter_grid_${colId}`, {
          event: `filter_grid_${colId}`,
          pageUrl: window.location.pathname,
          timestamp: new Date().toISOString(),
          userId: localStorage.getItem("user") || "",
          column: colId,
          filterValue: JSON.stringify(filterValues[colId])
        });
      });
    };
  const onSortChanged = (params: any) => {
    // Get column states and filter only sorted columns
    const sortModel = params.columnApi.getColumnState()
      .filter((col: any) => col.sort != null)
      .map((col: any) => ({
        colId: col.colId,
        sort: col.sort
      }));
  
    sortModel.forEach((sortItem: { colId: string; sort: 'asc' | 'desc' }) => {
      traceSpan(`sort_grid_${sortItem.colId}`, {
        event: `sort_grid_${sortItem.colId}`,
        pageUrl: window.location.pathname,
        timestamp: new Date().toISOString(),
        userId: localStorage.getItem('user') || '',
        column: sortItem.colId,
        direction: sortItem.sort
      });
    });
  };
  return (
    <Box sx={{ paddingX: "35px", mt: "15px", width: "100%" }}>
      <BackButton
        url={
          eventType === "never"
            ? "/NeverEvents/FOPC"
            : "/NeverEvents/maintainance"
        }
      />
      <Typography variant="h6" sx={{ my: "20px" }}>
        <span style={{ color: "#003d6b", fontWeight: "bold" }}>
          {clientName}
        </span>
      </Typography>
      {issueCounts.length > 0 && (
        <Box
          display="flex"
          flexDirection={"row"}
          flexWrap="wrap"
          width="100%"
          gap={2}
          margin="12px 0"
        >
          {issueCounts.map((item: any) => {
            return (
              <Card
                style={{ textDecoration: "none" }}
                sx={{
                  width: "15rem",
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  backgroundColor: "#e2eefe",
                }}
              >
                <CardContent
                  style={{
                    paddingBottom: "5px",
                  }}
                  sx={{
                    width: "95%",
                    height: "90%",
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    padding: "5px",
                    paddingTop: "5px",
                  }}
                >
                  <Box
                    sx={{
                      display: "flex",
                      justifyContent: "flex-start",
                      alignItems: "center",
                    }}
                  >
                    <Typography
                      sx={{
                        fontSize: "0.8rem",
                        color: "#647d96",
                        // ml: "5px",
                      }}
                    >
                      {item.optionDisplayName}
                    </Typography>
                  </Box>
                  <Typography
                    sx={{
                      fontWeight: "bold",
                      fontSize: "14px",
                      color: "red",
                      ml: "4px",
                    }}
                  >
                    {item.count}
                  </Typography>
                </CardContent>
              </Card>
            );
          })}
        </Box>
      )}
      <Box sx={{ marginY: "10px", width: "100%" }}>
        <Grid
          container
          spacing={2}
          display={"flex"}
          justifyContent={"space-between"}
        >
          <Grid item>{getGridWrapper()}</Grid>
        </Grid>
      </Box>
    </Box>
  );
};

export default NeverEventsDetailsTable;
