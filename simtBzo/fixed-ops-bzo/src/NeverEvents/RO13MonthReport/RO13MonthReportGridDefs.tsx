import { ColDef } from "ag-grid-community";
import moment from "moment";
import React, { useCallback } from "react";
import { GetRO13MonthCount } from "../../service/ro13countReportList";
import dayjs from "dayjs";

const RO13MonthReportGridDefs = (props: any) => {
  const { spec, storeId, tenantId } = props;
  const [reportList, setReportList] = React.useState([]);
  const [showDateSaveButton, setShowDateSaveButton] = React.useState(false);
  const getRO13MonthCount = () => {
    GetRO13MonthCount().then((response: any) => {
      setReportList(response);
    });
  };
  const getEmailListNew = () => {
    gridApiRef.current?.showLoadingOverlay();
    getRO13MonthCount();
  };
  const gridApiRef = React.useRef<any>(null);
  const onGridReady = useCallback((params: any) => {
    gridApiRef.current = params.api;
    params?.api?.showLoadingOverlay();
    getRO13MonthCount();
  }, []);
  const columnDefs = [
    {
      headerName: "Tenant",
      field: "tenantname",
      cellStyle: { textAlign: "left" },
      flex: 1.6,
    },
    {
      headerName: "Store",
      field: "storename",
      cellStyle: { textAlign: "left" },
      flex: 1.6,
    },
    {
      headerName: "Manufacturer",
      tooltipField: "Manufacturer",
      field: "manufacturer",
      cellStyle: { textAlign: "left" },
      flex: 1.7,
    },
    {
      headerName: "Month 1",
      field: "month1",
      cellStyle: { textAlign: "left" },
      flex: 1.1,
    },
    {
      headerName: "Month 2",
      field: "month2",
      cellStyle: { textAlign: "left" },
      flex: 1.1,
    },
    {
      headerName: "Month 3",
      field: "month3",
      cellStyle: { textAlign: "left" },
      flex: 1.1,
    },
    {
      headerName: "Month 4",
      field: "month4",
      cellStyle: { textAlign: "left" },
      flex: 1.1,
    },
    {
      headerName: "Month 5",
      field: "month5",
      cellStyle: { textAlign: "left" },
      flex: 1.1,
    },
    {
      headerName: "Month 6",
      field: "month6",
      cellStyle: { textAlign: "left" },
      flex: 1.1,
    },
    {
      headerName: "Month 7",
      field: "month7",
      cellStyle: { textAlign: "left" },
      flex: 1.1,
    },
    {
      headerName: "Month 8",
      field: "month8",
      cellStyle: { textAlign: "left" },
      flex: 1.1,
    },
    {
      headerName: "Month 9",
      field: "month9",
      cellStyle: { textAlign: "left" },
      flex: 1.1,
    },
    {
      headerName: "Month 10",
      field: "month10",
      cellStyle: { textAlign: "left" },
      flex: 1.1,
    },
    {
      headerName: "Month 11",
      field: "month11",
      cellStyle: { textAlign: "left" },
      flex: 1.1,
    },
    {
      headerName: "Month 12",
      field: "month12",
      cellStyle: { textAlign: "left" },
      flex: 1.1,
    },
    {
      headerName: "Month 13",
      field: "month13",
      cellStyle: { textAlign: "left" },
      flex: 1.1,
    },
    {
      headerName: "13-Month Count",
      field: "thirteenMonthTotRocount",
      cellStyle: { textAlign: "left" },
      flex: 1.5,
    },
  ];
  const defaultColDef = React.useMemo(() => {
    return {
      cellStyle: { fontSize: "12px", textAlign: "left" },
      resizable: true,
      suppressMenu: true,
      unSortIcon: true,
      enableValue: true,
      sortable: true,
      floatingFilter: true,
      filter: true,
      wrapHeaderText: true,
      autoHeaderHeight: true,
      wrapText: true,
      autoHeight: true,
      flex: 3,
    };
  }, []);
  return {
    defaultColDef,
    onGridReady,
    reportList,
    showDateSaveButton,
    setShowDateSaveButton,
    getRO13MonthCount,
    columnDefs,
    getEmailListNew,
  };
};

export default RO13MonthReportGridDefs;
