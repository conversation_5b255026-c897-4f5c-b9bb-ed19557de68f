import { ColDef } from "ag-grid-community";
import moment from "moment";
import React, { useCallback } from "react";
import { GetMissedList } from "../../service/missedReportList";
import dayjs from "dayjs";

const MissedReportGridDefs = (props: any) => {
  const { spec, storeId, tenantId } = props;
  const [missedReport, setMissedReport] = React.useState([]);
  const [showDateSaveButton, setShowDateSaveButton] = React.useState(false);
  const getMissedList = () => {
    GetMissedList(tenantId).then((response: any) => {
      const parsedData = JSON.parse(response) || [];
      setMissedReport(parsedData);
    });
  };
  const getEmailListNew = () => {
    gridApiRef.current?.showLoadingOverlay();
    getMissedList();
  };
  const gridApiRef = React.useRef<any>(null);
  const onGridReady = useCallback((params: any) => {
    gridApiRef.current = params.api;
    params?.api?.showLoadingOverlay();
    getMissedList();
  }, []);
  const columnDefs = [
    {
      field: "missing_dates",
      headerName: "Missing Dates",
      filter: false,
      sortable: false,
      cellRenderer: (params: { node: { group: any }; value: any }) => {
        if (params.node.group) return null;

        if (!params.value) return "";

        let dates: string[] = [];

        if (typeof params.value === "string") {
          dates = params.value.split(",");
        } else if (Array.isArray(params.value)) {
          dates = params.value;
        }

        return <div style={{ whiteSpace: "pre-line" }}>{dates.join("\n")}</div>;
      },
    },
    {
      field: "tenant_name",
      headerName: "Tenant Name",
      enableRowGroup: true,
      rowGroupIndex: 0,
      hide: true,
    },
    {
      field: "store_name",
      headerName: "Store Name",
      enableRowGroup: true,
      rowGroupIndex: 1,
      hide: true,
    },
  ];
  const defaultColDef = React.useMemo(() => {
    return {
      cellStyle: { fontSize: "12px", textAlign: "left" },
      resizable: true,
      suppressMenu: true,
      unSortIcon: true,
      enableValue: true,
      sortable: true,
      floatingFilter: true,
      filter: true,
      wrapHeaderText: true,
      autoHeaderHeight: true,
      wrapText: true,
      autoHeight: true,
      flex: 3,
    };
  }, []);
  return {
    defaultColDef,
    onGridReady,
    missedReport,
    showDateSaveButton,
    setShowDateSaveButton,
    getMissedList,
    columnDefs,
    getEmailListNew,
  };
};

export default MissedReportGridDefs;
