import { useEffect, useState } from "react";
import { Button, DatePicker, Form, Space } from "antd";
import { useRecordContext } from "react-admin";
import MissedReportGridDefs from "../../../src/NeverEvents/MissedReport/MissedReportGridDefs";
import dayjs from "dayjs";
import { AgGridReact } from "ag-grid-react";
import { Box, Typography } from "@mui/material";
import { RangePickerProps } from "antd/es/date-picker";
import { Constants } from "../../utils/constants";
import FileDownloadIcon from "@mui/icons-material/FileDownload";
import "ag-grid-community/styles/ag-grid.css";
import "ag-grid-community/styles/ag-theme-alpine.css"; // or your chosen theme
import { traceSpan } from "../../utils/OTTTracing";

const { RangePicker } = DatePicker;

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};

export default function MissedReport(props?: any) {
  const { spec } = props;
  const record = useRecordContext();
  const [gridApi, setGridApi] = useState<any>(null);

  const {
    missedReport,
    columnDefs,
    defaultColDef,
    onGridReady,
    setShowDateSaveButton,
    getMissedList,
    getEmailListNew,
  } = MissedReportGridDefs({
    spec,
    storeId: record?.storeId,
    tenantId: record?.tenantId,
  });

  const onFinish = () => {
    updateDateRange();
  };

  const updateDateRange = () => {
    getEmailListNew();
    setShowDateSaveButton(false);
  };

  const handleExport = () => {
    traceSpan(`download_excel_missedreports`, {
      event: `download_excel_missedreports`,
      pageUrl: window.location.pathname,
      timestamp: new Date().toISOString(),
      userId: localStorage.getItem("user") || "",
    });
    if (gridApi) {
      const now = new Date();
      const timestamp = `${now.getFullYear()}${String(
        now.getMonth() + 1
      ).padStart(2, "0")}${String(now.getDate()).padStart(2, "0")}${String(
        now.getHours()
      ).padStart(2, "0")}${String(now.getMinutes()).padStart(2, "0")}${String(
        now.getSeconds()
      ).padStart(2, "0")}`;
      const fileName = `Missed_Report_${timestamp}.xlsx`;

      gridApi.exportDataAsExcel({
        prependContent: [
          [
            {
              data: { value: "Missed Report", type: "String" },
              mergeAcross: columnDefs.length - 1,
              styleId: "header",
            },
          ],
          [],
        ],
        sheetName: "Missed Report",
        fileName: fileName,
      });
    }
  };

  const handleRowClick = (event: any) => {
    const rowData = event.data;
    traceSpan(`missedreport_row_click`, {
      event: `missedreport_row_click`,
      pageUrl: window.location.pathname,
      timestamp: new Date().toISOString(),
      userId: localStorage.getItem("user") || "",
      rowIndex: event.rowIndex,
    });
  };
  const onSortChanged = (params: any) => {
    // Get column states and filter only sorted columns
    const sortModel = params.columnApi
      .getColumnState()
      .filter((col: any) => col.sort != null)
      .map((col: any) => ({
        colId: col.colId,
        sort: col.sort,
      }));

    sortModel.forEach((sortItem: { colId: string; sort: "asc" | "desc" }) => {
      traceSpan(`sort_grid_${sortItem.colId}`, {
        event: `sort_grid_${sortItem.colId}`,
        pageUrl: window.location.pathname,
        timestamp: new Date().toISOString(),
        userId: localStorage.getItem("user") || "",
        column: sortItem.colId,
        direction: sortItem.sort,
      });
    });
  };

  const handleRowExpansion = (event: any) => {
    const rowData = event.data;
    const isExpanded = event.expanded;
    traceSpan(`missedreport_row_` + isExpanded ? "expansion" : "collapse", {
      event: `missedreport_row_` + isExpanded ? "expansion" : "collapse",
      pageUrl: window.location.pathname,
      timestamp: new Date().toISOString(),
      userId: localStorage.getItem("user") || "",
      action: isExpanded ? "expand" : "collapse",
      rowIndex: event.rowIndex,
    });
  };

  return (
    <Space
      direction="vertical"
      style={{ marginTop: "16px", width: "85vw !important" }}>
      {/* Header with Export Button */}
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        paddingRight={2}>
        <Typography variant="h6"></Typography>
        <Button
          type="primary"
          icon={<FileDownloadIcon />}
          style={{ backgroundColor: "#1976d2", color: "#fff" }}
          onClick={handleExport}>
          Export
        </Button>
      </Box>

      {/* AG Grid */}
      <Box>
        <div
          className={Constants.ag_grid_theme}
          style={{
            height: "83vh",
            width: "85vw",
          }}>
          <AgGridReact
            columnDefs={columnDefs}
            editType="fullRow"
            rowData={missedReport}
            defaultColDef={defaultColDef}
            rowSelection="single"
            onGridReady={(params: any) => {
              onGridReady(params);
              setGridApi(params.api);
            }}
            singleClickEdit={true}
            suppressColumnVirtualisation={true}
            suppressChangeDetection={true}
            stopEditingWhenCellsLoseFocus={true}
            pagination={true}
            paginationPageSize={100}
            excelStyles={[
              {
                id: "header",
                font: { size: 12, bold: true },
                alignment: { horizontal: "Center" },
              },
            ]}
            onRowClicked={handleRowClick}
            onRowGroupOpened={handleRowExpansion}
            onSortChanged={onSortChanged}
          />
        </div>
      </Box>
    </Space>
  );
}
