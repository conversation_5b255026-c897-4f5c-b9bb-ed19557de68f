import React, { useMemo, useState } from "react";
import { ColDef } from "ag-grid-community";
import TenantQueries from "../../service/DataFetchQueries/tenantQueries";
import { traceSpan } from "../../utils/OTTTracing";

const UsersListGridDefs = () => {
  const { GetUsersListByTenants, GetAllTenantsRealmsQuery } = TenantQueries;
  const [usersList, setUsersList] = React.useState([]);
  const [selectedModule, setSelectedModule] = useState<any>(null);
  const [allTenants, setAllTenants] = useState<any>([]);
  const [agGridApi, setAgGridApi] = useState<any>();

  const fetchAllTenantsList = async () => {
    try {
      const res = await GetAllTenantsRealmsQuery();
      const sortedTenants = [...res].sort((a, b) =>
        a.tenantName.localeCompare(b.tenantName)
      );
      setAllTenants(sortedTenants);
    } catch (error) {
      console.error("Error fetching launch reports list:", error);
    }
  };
  const handleTenantFilter = async (e: any) => {
    traceSpan("tenant_dropdown_changed", {
      event: "tenant_dropdown_changed",
      pageUrl: window.location.pathname,
      timestamp: new Date().toISOString(),
      userId: localStorage.getItem("user") || "",
      value: e.target.value,
    });
    agGridApi?.showLoadingOverlay();
    setSelectedModule(e.target.value);
    getUserslist(e.target.value);
  };

  const getUserslist = (realmName: string) => {
    GetUsersListByTenants(realmName).then((res: any) => {
      setUsersList(res);
    });
  };

  const onGridReady = (params: any) => {
    // params?.api?.showLoadingOverlay();
    setAgGridApi(params.api);
  };

  const columnDefs: ColDef[] = [
    {
      headerName: "Realm Name",
      field: "realmName",
      cellStyle: { textAlign: "left" },
      flex: 3,
      enableRowGroup: true,
    },
    {
      headerName: "User Group",
      field: "userGroup",
      flex: 3,
      cellStyle: { textAlign: "left" },
      enableRowGroup: true,
    },
    {
      headerName: "User Name",
      field: "userName",
      flex: 3,
      cellStyle: { textAlign: "left" },
      enableRowGroup: true,
    },
    {
      headerName: "Mail Id",
      field: "email",
      cellStyle: { textAlign: "left" },
      flex: 4,
    },
    {
      headerName: "User Role",
      field: "userRole",
      flex: 2,
      cellStyle: { textAlign: "left" },
      enableRowGroup: true,
    },
  ];
  const defaultColDef = React.useMemo(() => {
    return {
      cellStyle: { textAlign: "left" },
      resizable: true,
      suppressMenu: true,
      unSortIcon: true,
      enableValue: true,
      sortable: true,
      floatingFilter: true,
      filter: true,
      wrapHeaderText: true,
      autoHeaderHeight: true,
      wrapText: true,
      autoHeight: true,
      flex: 3,
    };
  }, []);
  const sideBar = useMemo(() => {
    return {
      toolPanels: [
        {
          id: "columns",
          labelDefault: "Columns",
          labelKey: "columns",
          iconKey: "columns",
          toolPanel: "agColumnsToolPanel",
          toolPanelParams: {
            suppressValues: true,
            suppressPivotMode: true,
            suppressColumnSelectAll: true,
          },
        },
      ],
    };
  }, []);
  return {
    columnDefs,
    defaultColDef,
    onGridReady,
    usersList,
    sideBar,
    selectedModule,
    handleTenantFilter,
    fetchAllTenantsList,
    allTenants,
  };
};

export default UsersListGridDefs;
