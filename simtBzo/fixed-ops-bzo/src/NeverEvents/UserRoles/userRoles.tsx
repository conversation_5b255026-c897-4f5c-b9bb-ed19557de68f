import { Box, FormControl, InputLabel, MenuItem, Select } from "@mui/material";
import React, { useEffect } from "react";
import { AgGridReact } from "ag-grid-react";
import UsersListGridDefs from "./userRolesGridRefs";
import { Constants } from "../../utils/constants"; 
import { traceSpan } from "../../utils/OTTTracing";

const UsersList = () => {
  const {
    columnDefs,
    defaultColDef,
    onGridReady,
    usersList,
    sideBar,
    selectedModule,
    handleTenantFilter,
    fetchAllTenantsList,
    allTenants,
  } = UsersListGridDefs();
  const onFilterChanged = (e: any) => {
      const filterValues = e.api.getFilterModel();
      Object.keys(filterValues).forEach((colId) => {
        traceSpan(`filter_grid_${colId}`, {
          event: `filter_grid_${colId}`,
          pageUrl: window.location.pathname,
          timestamp: new Date().toISOString(),
          userId: localStorage.getItem("user") || "",
          column: colId,
          filterValue: JSON.stringify(filterValues[colId])
        });
      });
    };
  const onSortChanged = (params: any) => {
    // Get column states and filter only sorted columns
    const sortModel = params.columnApi.getColumnState()
      .filter((col: any) => col.sort != null)
      .map((col: any) => ({
        colId: col.colId,
        sort: col.sort
      }));
  
    sortModel.forEach((sortItem: { colId: string; sort: 'asc' | 'desc' }) => {
      traceSpan(`sort_grid_${sortItem.colId}`, {
         event: `sort_grid_${sortItem.colId}`,
        pageUrl: window.location.pathname,
        timestamp: new Date().toISOString(),
        userId: localStorage.getItem('user') || '',
        column: sortItem.colId,
        direction: sortItem.sort
      });
    });
  };
  useEffect(() => {
    fetchAllTenantsList();
  }, []);
  return (
    <Box sx={{ paddingX: "10px", width: "100%", marginTop: "16px" }}>
      <Box sx={{ marginBottom: "16px", width: "100%" }}>
        <FormControl variant="outlined" 
        sx={{ width: "20%", margin: 0, mr: 2 }}>
          <InputLabel id="demo-simple-select-label" sx={{ fontSize: "14px" }}>
            Tenant*
          </InputLabel>
          <Select
            labelId="demo-simple-select-label"
            id="demo-simple-select"
            value={selectedModule}
            onChange={handleTenantFilter}
            label="Tenant*"
            sx={{ height: "40px", fontSize: "14px" }}
            // displayEmpty // Ensures placeholder behavior when no value is selected
            onFocus={() => {
                 traceSpan('tenant_dropdown_focused', {
                    event: 'tenant_dropdown_focused',
                    pageUrl: window.location.pathname,
                    timestamp: new Date().toISOString(),
                    userId:
                    localStorage.getItem('user') || ''
                  });
            }}
          >
            <MenuItem value="" disabled>
              Select a Tenant
            </MenuItem>
            {allTenants.map((item: any) => (
              <MenuItem key={item.realmName} value={item.realmName}>
                {item.tenantName}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        
      </Box>
      <Box sx={{ marginBottom: "16px", width: "100%" }}>
        <div
          className={Constants.ag_grid_theme}
          style={{ height: "85vh", width: "100%" }}
        >
          <AgGridReact
            columnDefs={columnDefs}
            editType="fullRow"
            rowData={usersList}
            defaultColDef={defaultColDef}
            rowSelection="single"
            onGridReady={(params: any) => onGridReady(params)}
            // rowHeight={30}
            sideBar={sideBar}
            // sideBar={"columns"}
            singleClickEdit={true}
            suppressColumnVirtualisation={true}
            suppressChangeDetection={true}
            stopEditingWhenCellsLoseFocus={true}
            overlayNoRowsTemplate={"Select a tenant to view users list"}
            onFilterChanged={onFilterChanged}
            onSortChanged={onSortChanged}
          />
        </div>
      </Box>
    </Box>
  );
};

export default UsersList;
