import { useEffect } from "react";
import { <PERSON><PERSON>, DatePicker, Form, Space } from "antd";
import { useRecordContext } from "react-admin";
import EmailReportGridDefs from "../../../src/NeverEvents/EmailReport/EmailReportGridDefs";
import dayjs from "dayjs";
import { AgGridReact } from "ag-grid-react";
import { Box, Typography } from "@mui/material";
import { RangePickerProps } from "antd/es/date-picker";
import { Constants } from "../../utils/constants";
import { traceSpan } from "../../utils/OTTTracing";

const { RangePicker } = DatePicker;

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 14 },
  },
};

 const onFilterChanged = (e: any) => {
      const filterValues = e.api.getFilterModel();
      Object.keys(filterValues).forEach((colId) => {
        traceSpan(`filter_grid_${colId}`, {
          event: `filter_grid_${colId}`,
          pageUrl: window.location.pathname,
          timestamp: new Date().toISOString(),
          userId: localStorage.getItem("user") || "",
          column: colId,
          filterValue: JSON.stringify(filterValues[colId])
        });
      });
    };
  const onSortChanged = (params: any) => {
    // Get column states and filter only sorted columns
    const sortModel = params.columnApi.getColumnState()
      .filter((col: any) => col.sort != null)
      .map((col: any) => ({
        colId: col.colId,
        sort: col.sort
      }));
  
    sortModel.forEach((sortItem: { colId: string; sort: 'asc' | 'desc' }) => {
      traceSpan(`sort_grid_${sortItem.colId}`, {
         event: `sort_grid_${sortItem.colId}`,
        pageUrl: window.location.pathname,
        timestamp: new Date().toISOString(),
        userId: localStorage.getItem("user") || "",
        column: sortItem.colId,
        direction: sortItem.sort
      });
    });
  };

export default function EmailReport(props?: any) {
  const { spec } = props;
  const record = useRecordContext();
  const {
    emailReportList,
    columnDefs,
    defaultColDef,
    onGridReady,
    selectedDateRange,
    setSelectedDateRange,
    setShowDateSaveButton,
    getEmailList,
    getEmailListNew,
  } = EmailReportGridDefs({
    spec,
    storeId: record?.storeId,
    tenantId: record?.tenantId,
  });

  const onFinish = () => {
    traceSpan(`click_apply_button`, {
      event: `click_apply_button`,
      pageUrl: window.location.pathname,
      timestamp: new Date().toISOString(),
      userId:localStorage.getItem('user') || ''
    });
    updateDateRange();
  };

  const updateDateRange = () => {
    getEmailListNew();
    setShowDateSaveButton(false);
  };

  // const onDateChange: RangePickerProps["onChange"] = (
  //   dates: any,
  //   dateStrings: [string, string]
  // ) => {
  //   if (dates && dates?.length === 2) {
  //     const startDate = dates[1].endOf("day");
  //     const endDate = dates[1].endOf("day");
  //     setSelectedDateRange([startDate, endDate]);
  //     setShowDateSaveButton(true);
  //   } else {
  //     alert("kk");
  //     setSelectedDateRange(dates);
  //     setShowDateSaveButton(true);
  //   }
  // };

  useEffect(() => {
    const today = dayjs();
    setSelectedDateRange([today.startOf("day"), today.endOf("day")]);
  }, []);

  return (
    <Space direction="vertical" style={{ marginTop: "16px" }}>
      <Box display="flex" alignItems="center">
        <Form
          {...formItemLayout}
          onFinish={onFinish}
          initialValues={{
            Date: dayjs(), // default to today
          }}
          style={{
            maxWidth: 600,
            display: "flex",
            gap: "10px",
          }} // Added gap for spacing
        >
          <Form.Item
            label={
              <span style={{ color: "#003D6B", fontWeight: "bold" }}>Date</span>
            }
            name="Date"
            rules={[{ required: true }]}
            style={{ marginBottom: 30 }} // Removes extra space below item
          >
            <DatePicker
              format="MM/DD/YYYY"
              inputReadOnly={true}
              style={{ width: 180 }} // Set user-friendly width here
              onChange={(date) => {
                if (date) {
                  const start = date.clone().endOf("day");
                  const end = date.clone().endOf("day");
                  traceSpan('select_emailreportdate_datepicker', {
                      event: 'select_emailreportdate_datepicker',
                      pageUrl: window.location.pathname,
                      timestamp: new Date().toISOString(),
                      userId: localStorage.getItem('user') || '',
                      value: date
                  });
                  setSelectedDateRange([start, end]);
                  setShowDateSaveButton(true);
                }
              }}
              onFocus={() => {
                  traceSpan('emailreportdate_datepicker_focused', {
                    event: 'emailreportdate_datepicker_focused',
                    pageUrl: window.location.pathname,
                    timestamp: new Date().toISOString(),
                    userId:
                      localStorage.getItem('user') || ''
                    });
              }}
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0 }}>
            <Button
              style={{ backgroundColor: "#1976D2", borderColor: "#1976D2" }}
              type="primary"
              size="middle"
              htmlType="submit">
              Apply
            </Button>
          </Form.Item>
        </Form>
      </Box>
      <Box>
        <div
          className={Constants.ag_grid_theme}
          style={{
            height: "83vh",
            width:
              spec === "storeSpecific"
                ? "81vw"
                : spec === "tenantSpecific"
                ? "83vw"
                : "85vw",
            marginTop: "-15px",
          }}>
          <AgGridReact
            columnDefs={columnDefs}
            editType="fullRow"
            rowData={emailReportList}
            defaultColDef={defaultColDef}
            rowSelection="single"
            onGridReady={(params: any) => onGridReady(params)}
            singleClickEdit={true}
            suppressColumnVirtualisation={true}
            suppressChangeDetection={true}
            stopEditingWhenCellsLoseFocus={true}
            pagination={true}
            paginationPageSize={100}
            onFilterChanged={onFilterChanged}
            onSortChanged={onSortChanged}
          />
        </div>
      </Box>
    </Space>
  );
}
