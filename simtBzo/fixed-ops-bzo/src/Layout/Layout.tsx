import React, { HtmlHTMLAttributes } from "react";
import { CssBaseline } from "@mui/material";
import { CoreLayoutProps, Layout as RaLayout } from "react-admin";
import { ErrorBoundary } from "react-error-boundary";
import { Error } from "react-admin";
import Header from "./Header";
import SideMenu from "../components/SideMenu";
import AppBar from "./AppBar";

const Layout = ({ children, ...props }: LayoutProps) => (
  <RaLayout appBar={AppBar} menu={SideMenu}>
    <main id="main-content">
      {/* @ts-ignore */}
      <ErrorBoundary FallbackComponent={Error}>{children}</ErrorBoundary>
    </main>
  </RaLayout>
);

export interface LayoutProps
  extends CoreLayoutProps,
    Omit<HtmlHTMLAttributes<HTMLDivElement>, "title"> {}

export default Layout;
