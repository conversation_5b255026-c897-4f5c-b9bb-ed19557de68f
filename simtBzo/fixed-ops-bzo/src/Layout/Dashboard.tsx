import { Box, Divider, Paper, Typography } from "@mui/material";
import React, { useState } from "react";
import { TenantList } from "../tenants/TenantList";
import DashboardCard from "../components/DashboardCard";
import Loading from "../components/Loading";
import TenantQueries from "../service/DataFetchQueries/tenantQueries";
import { useParams } from "react-router-dom";
const Dashboard = () => {
  const { GetDashboardCardQuery } = TenantQueries;
  const [isHovered, setIsHovered] = useState("");
  const { tenantStatus } = useParams();
  const [isCardSelected, setIsCardSelected] = useState(tenantStatus);
  const [cancelled, setCancelled] = React.useState();
  const [testTenant, setTestTenant] = React.useState();
  const [launched, setLaunched] = React.useState();
  const [onboarding, setOnboarding] = React.useState();
  const [qaValidated, setQaValidated] = React.useState();
  const [readyToLaunch, setReadyToLaunch] = React.useState();
  const [review, setReview] = React.useState();
  const [isTenantsLoading, setIsTenantsLoading] = React.useState(true);
  const [tenantNameSearch, setTenantNameSearch] = React.useState("");
  const realmRole = JSON.parse(localStorage.getItem("role") || "");

  React.useEffect(() => {
    setTenantNameSearch("");
    setIsCardSelected(tenantStatus);
  }, [tenantStatus]);

  const getDashboardCardDetails = () => {
    GetDashboardCardQuery()
      .then((data) => {
        const testTenant = data?.filter((obj: any) => obj.isTestTenant).length;
        const cancelled = data?.filter(
          (obj: any) => obj.deletedstoreortenant
        ).length;
        const onboarding = data?.filter((obj: any) => obj.onboarding).length;
        const launched = data?.filter((obj: any) => obj.launched).length;
        const readyToLaunch = data?.filter(
          (obj: any) => obj.readyToLaunch
        ).length;
        const qaValidate = data?.filter(
          (obj: any) => obj.nsQaValidation
        ).length;
        const review = data?.filter((obj: any) => obj.review).length;
        setTestTenant(testTenant);
        setCancelled(cancelled);
        setLaunched(launched);
        setOnboarding(onboarding);
        setQaValidated(qaValidate);
        setReadyToLaunch(readyToLaunch);
        setReview(review);
      })
      .finally(() => {
        setIsTenantsLoading(false);
      });
  };
  React.useEffect(() => {
    getDashboardCardDetails();
  }, []);

  if (isTenantsLoading) return <Loading />;
  return (
    <Box sx={{ display: "flex", flexDirection: "column", margin: "10px 15px" }}>
      <Box
        sx={{
          display: "flex",
          flexWrap: "wrap",
          marginTop: "20px",
          // height: "8vh",
        }}
      >
        <DashboardCard
          cardName="onboarding"
          isHovered={isHovered}
          setIsHovered={setIsHovered}
          isCardSelected={isCardSelected}
          setIsCardSelected={setIsCardSelected}
          count={onboarding}
        />
        <DashboardCard
          cardName="nsQaValidation"
          isHovered={isHovered}
          setIsHovered={setIsHovered}
          isCardSelected={isCardSelected}
          setIsCardSelected={setIsCardSelected}
          count={qaValidated}
        />
        <DashboardCard
          cardName="review"
          isHovered={isHovered}
          setIsHovered={setIsHovered}
          isCardSelected={isCardSelected}
          setIsCardSelected={setIsCardSelected}
          count={review}
        />
        <DashboardCard
          cardName="readyToLaunch"
          isHovered={isHovered}
          setIsHovered={setIsHovered}
          isCardSelected={isCardSelected}
          setIsCardSelected={setIsCardSelected}
          count={readyToLaunch}
        />
        <DashboardCard
          cardName="launched"
          isHovered={isHovered}
          setIsHovered={setIsHovered}
          isCardSelected={isCardSelected}
          setIsCardSelected={setIsCardSelected}
          count={launched}
        />
        <DashboardCard
          cardName="cancelled"
          isHovered={isHovered}
          setIsHovered={setIsHovered}
          isCardSelected={isCardSelected}
          setIsCardSelected={setIsCardSelected}
          count={cancelled}
        />
        {realmRole === "superadmin" && (
          <DashboardCard
            cardName="testTenant"
            isHovered={isHovered}
            setIsHovered={setIsHovered}
            isCardSelected={isCardSelected}
            setIsCardSelected={setIsCardSelected}
            count={testTenant}
          />
        )}
      </Box>
      <Paper
        elevation={3}
        sx={{ mt: "4px", padding: "10px 30px", paddingBottom: "20px" }}
      >
        <TenantList
          isSelected={isCardSelected}
          tenantNameSearch={tenantNameSearch}
          setTenantNameSearch={setTenantNameSearch}
          getDashboardCardDetails={getDashboardCardDetails}
        />
      </Paper>
    </Box>
  );
};

export default Dashboard;
