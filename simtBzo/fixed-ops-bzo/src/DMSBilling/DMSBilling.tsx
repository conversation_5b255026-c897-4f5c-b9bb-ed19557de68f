import React, { useMemo } from "react";
import {
  Card,
  CardContent,
  MenuItem,
  Button,
  TextField,
  Select,
  FormControlLabel,
  RadioGroup,
  Radio,
  Typography,
  Grid,
} from "@mui/material";
import { Formik, Form, Field, ErrorMessage, FormikHelpers } from "formik";
import * as Yup from "yup";
import styled from "styled-components";
import { DatePicker } from "antd";
import dayjs, { Dayjs } from "dayjs";
import { AgGridReact } from "ag-grid-react";
import "ag-grid-community/styles/ag-grid.css";
import "ag-grid-community/styles/ag-theme-alpine.css";
import { InputAdornment } from "@mui/material";
import AttachMoneyIcon from "@mui/icons-material/AttachMoney";
const FormErrorMsg = styled.div`
  color: red;
  font-size: 12px;
`;

interface FormValues {
  dms: string;
  dmsFee: string;
  feesEffectiveFrom: string;
  effectiveDate: Dayjs | null;
  applyToAll: string;
  reason: string;
}

export default function DMSBilling() {
  const defaultColDef = useMemo(() => {
    return {
      sortable: true,
      filter: true,
      resizable: true,
    };
  }, []);

  const columnDefs: any = [
    {
      headerCheckboxSelection: true,
      checkboxSelection: true,
      width: 50,
    },
    {
      headerName: "Dealerships",
      field: "dealerships",
      flex: 3,
    },
    {
      headerName: "Number of active Subscriptions",
      field: "subscriptions",
      flex: 2,
    },
  ];

  const rowData = [
    { dealerships: "Diek Smith", subscriptions: 5 },
    { dealerships: "Deyarman Auto", subscriptions: 3 },
    { dealerships: "Nationwide Motors", subscriptions: 3 },
  ];

  const initialValues: FormValues = {
    dms: "",
    dmsFee: "",
    feesEffectiveFrom: "Later",
    effectiveDate: dayjs(),
    applyToAll: "No",
    reason: "",
  };

  const validationSchema = Yup.object({
    dms: Yup.string().required("Required"),
    dmsFee: Yup.string().required("Required"),
    feesEffectiveFrom: Yup.string().required("Required"),
    effectiveDate: Yup.date().required("Required").nullable(),
    applyToAll: Yup.string().required("Required"),
    reason: Yup.string().required("Required"),
  });

  const handleSubmit = async (
    values: FormValues,
    { setSubmitting, resetForm }: FormikHelpers<FormValues>
  ) => {
    try {
      console.log(JSON.stringify(values, null, 2));
      resetForm();
    } catch (error) {
      console.error("Failed to submit form:", error);
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <Card>
      <CardContent style={{ maxHeight: "500px", overflowY: "auto" }}>
        <Formik
          initialValues={initialValues}
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
        >
          {({ isSubmitting, values, setFieldValue }) => (
            <Form>
              <Grid container spacing={1}>
                <Grid item xs={12}>
                  <Typography variant="h5" gutterBottom>
                    DMS Billing
                  </Typography>
                </Grid>

                <Grid item xs={5.5} style={{ paddingRight: "20px" }}>
                  <Grid container spacing={2} alignItems="center">
                    <Grid item xs={5}>
                      <Typography variant="body1">Choose DMS</Typography>
                    </Grid>
                    <Grid item xs={7}>
                      <Field
                        name="dms"
                        as={Select}
                        variant="outlined"
                        size="small"
                        fullWidth
                        displayEmpty
                        sx={{ height: 40 }}
                      >
                        <MenuItem value="">Select DMS</MenuItem>
                        <MenuItem value="CDK">CDK</MenuItem>
                        <MenuItem value="Reynolds">Reynolds</MenuItem>
                        <MenuItem value="Dealertrack">Dealertrack</MenuItem>
                      </Field>
                      <ErrorMessage name="dms" component={FormErrorMsg} />
                    </Grid>

                    <Grid item xs={5}>
                      <Typography variant="body1">DMS Fees</Typography>
                    </Grid>
                    <Grid item xs={7}>
                      <Field
                        name="dmsFee"
                        as={TextField}
                        variant="outlined"
                        size="small"
                        fullWidth
                        sx={{ height: 40 }}
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <AttachMoneyIcon />
                            </InputAdornment>
                          ),
                        }}
                      />
                      <ErrorMessage name="dmsFee" component={FormErrorMsg} />
                    </Grid>

                    <Grid item xs={5}>
                      <Typography variant="body1">
                        Fees Effective From
                      </Typography>
                    </Grid>
                    <Grid item xs={7}>
                      <Field
                        name="feesEffectiveFrom"
                        as={RadioGroup}
                        row
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                          setFieldValue("feesEffectiveFrom", e.target.value);
                        }}
                      >
                        <FormControlLabel
                          value="Now"
                          control={<Radio />}
                          label="Now"
                        />
                        <FormControlLabel
                          value="Later"
                          control={<Radio />}
                          label="Later"
                        />
                      </Field>
                      <ErrorMessage
                        name="feesEffectiveFrom"
                        component={FormErrorMsg}
                      />
                    </Grid>

                    {values.feesEffectiveFrom === "Later" && (
                      <>
                        <Grid item xs={5}>
                          <Typography variant="body1">
                            Effective Date
                          </Typography>
                        </Grid>
                        <Grid item xs={7}>
                          <DatePicker
                            format={"MM/DD/YYYY"}
                            value={values.effectiveDate}
                            onChange={(date) =>
                              setFieldValue("effectiveDate", date)
                            }
                            style={{ width: "100%", height: 40 }}
                          />
                          <ErrorMessage
                            name="effectiveDate"
                            component={FormErrorMsg}
                          />
                        </Grid>
                      </>
                    )}

                    <Grid item xs={5}>
                      <Typography variant="body1">
                        Apply DMS fees to all dealerships
                      </Typography>
                    </Grid>
                    <Grid item xs={7}>
                      <Field
                        name="applyToAll"
                        as={RadioGroup}
                        row
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                          setFieldValue("applyToAll", e.target.value);
                        }}
                      >
                        <FormControlLabel
                          value="No"
                          control={<Radio />}
                          label="No"
                        />
                        <FormControlLabel
                          value="Yes"
                          control={<Radio />}
                          label="Yes"
                        />
                      </Field>
                      <ErrorMessage
                        name="applyToAll"
                        component={FormErrorMsg}
                      />
                    </Grid>
                  </Grid>
                </Grid>
                <Grid item xs={6.5}>
                  <Grid container spacing={2}>
                    <Grid item xs={12}>
                      <div
                        className="ag-theme-alpine "
                        style={{ height: 222, width: "100%" }}
                      >
                        <AgGridReact
                          rowSelection="multiple"
                          columnDefs={columnDefs}
                          defaultColDef={defaultColDef}
                          rowData={rowData}
                        />
                      </div>
                    </Grid>

                    <Grid
                      item
                      xs={3}
                      style={{ display: "flex", alignItems: "center" }}
                    >
                      <Typography variant="body1">Reason</Typography>
                    </Grid>
                    <Grid item xs={5}>
                      <Field
                        name="reason"
                        as={TextField}
                        variant="outlined"
                        size="small"
                        fullWidth
                        sx={{ height: 40, marginTop: "1px" }}
                      />
                      <ErrorMessage name="reason" component={FormErrorMsg} />
                    </Grid>
                    <Grid
                      item
                      xs={4}
                      style={{ display: "flex", justifyContent: "flex-end" }}
                    >
                      <Button
                        variant="contained"
                        type="submit"
                        disabled={isSubmitting}
                        sx={{
                          textTransform: "none",
                          fontFamily: "Montserrat",
                          backgroundColor: "#59aaaa",
                          color: "white",
                          height: 40,
                          width: "180px",
                          padding: "5px",
                          "&:hover": {
                            backgroundColor: "#208f8f",
                          },
                        }}
                      >
                        Save
                      </Button>
                    </Grid>
                  </Grid>
                </Grid> 
              </Grid>
            </Form>
          )}
        </Formik>
      </CardContent>
    </Card>
  );
}
