import FetchConfig from "./fetchConfig";
import { IFetchConfigReturn } from "../types";

//Query
export const GetLaunchReportsListDetails = () => {
  return FetchConfig({
    anonymous: false,
    Realm: String(process.env.REACT_APP_KEYCLOAK_REALM),
    query: `query GetLaunchReportsListDetails {
      statelessServiceReportsWorkpackageCounts {
        nodes {
         onboardingTotal
        needsObMeeting
        dmsNotActive
        opsNotCategorized
        gridNotEntered
        modelsNotMapped
        matrixNotEntered
        usersNotCreated
        launchNotScheduled
        launchCompleted
        allStores

        }
      }
    }
    `,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceReportsWorkpackageCounts.nodes;
  });
};

//Muutation
export const GetAduOpenStoreWorkpackages = () => {
  return FetchConfig({
    anonymous: false,
    query: `mutation GetAduOpenStoreWorkpackages {
      statelessServiceBillingGetAduStoreWorkpackages(input: {}) {
       results {
        agreementReceived
        agreementReceivedComments
        coach
        coachComments
        coachValue
        dms
        dmsActiveDate
        dmsActiveDateComments
        dmsComments
        dmsValue
        groupName
        groupNameComments
        laborPricingGridEnteredInBzo
        laborPricingGridEnteredInBzoComments
        laborPricingGridReceived
        laborPricingGridReceivedComments
        launchCompleted
        launchCompletedComments
        modelMapping
        modelMappingComments
        obAge
        obAgeComments
        obMeetingCompleted
        obMeetingCompletedComments
        onboardingCoordinator
        onboardingCoordinatorComments
        onboardingCoordinatorValue
        opCodesAvailable
        opCodesAvailableComments
        opCodesCategorized
        opCodesCategorizedComments
        partsMatrixEnteredInBzo
        partsMatrixEnteredInBzoComments
        partsMatrixReceived
        partsMatrixReceivedComments
        readyForReview
        readyForReviewComments
        requestedDmsAccess
        requestedDmsAccessComments
        requestedObMeeting
        requestedObMeetingComments
        reviewCompleted
        reviewCompletedComments
        scheduledLaunch
        scheduledLaunchComments
        salesperson
        salespersonComments
        salespersonValue
        slno
        smokeTestCompleted
        smokeTestCompletedComments
        storeName
        storeNameComments
        storeProjectId
        tagAgreementReceivedAction
        tagCoachAction
        tagDmsAction
        tagDmsActiveDateAction
        tagGroupPay
        tagGroupPayComments
        tagGroupPayValue
        tagLaborPricingGridEnteredInBzoAction
        tagLaborPricingGridReceived
        tagLaunchCompletedAction
        tagModelMappingAction
        tagObAgeAction
        tagObMeetingCompletedAction
        tagOnboardingCoordinatorAction
        tagOpCodesAvailableAction
        tagOpCodesCategorizedAction
        tagPartsMatrixEnteredInBzoAction
        tagPartsMatrixReceivedFollowup
        tagReadyForReviewAction
        tagRequestedDmsAccessFollowup
        tagRequestedObMeetingFollowup
        tagReviewCompletedAction
        tagSalespersonAction
        tagScheduledLaunchActionNeeded
        tagSmokeTestCompletedAction
        tagStoreHold
        tagTagGroupPayAction
        tagTotalDaysToLaunchAction
        tagUsersCreatedAction
        taggrouphold
        tenantProjectId
        totalDaysToLaunch
        totalDaysToLaunchComments
        usersCreated
        usersCreatedComments
        launchDate
        launchDateComments
        tagStoreLaunchDate
        tagStoreReadyToLaunch


    }
      }
    }
  `,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBillingGetAduStoreWorkpackages.results;
  });
};
export const UpdateTextFieldComments = (
  fieldName: string,
  fieldValue: string,
  workPackageId: string | number,
  storename: string,
  groupname: string,
  activity: string
) => {
  return FetchConfig({
    anonymous: false,
    query: `
      mutation UpdateTextFieldComments(
        $fieldName: String!
        $fieldValue: String!
        $workPackageId: String!
        $storename: String!
        $groupname: String!
        $activity: String!
      ) {
        statelessServiceReportsUpdateTextField(
          input: {
            fieldName: $fieldName
            fieldValue: $fieldValue
            workPackageId: $workPackageId
            storename: $storename
            groupname: $groupname
            activity: $activity
          }
        ) {
          clientMutationId
          string
        }
      }
    `,
    variables: {
      fieldName,
      fieldValue,
      workPackageId,
      storename,
      groupname,
      activity,
    },
  }).then((response: IFetchConfigReturn) => {
    return response?.data?.statelessServiceReportsUpdateTextField;
  });
};

export const GetDropDownValues = (fieldName: string) => {
  const variables = {
    fieldName: fieldName,
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation GetDropDownValues($fieldName: String) {
         statelessServiceReportsGetAllowedValuesForCustomField(input: {fieldName: $fieldName}) {
                    results {
                            customOptionId
                            customOptionValue
                                }
                            }
                        }
                    `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceReportsGetAllowedValuesForCustomField
      .results;
  });
};
export const UpdateDropdownField = (
  fieldName: string,
  fieldValue: string,
  workPackageId: number,
  storename: string,
  groupname: string,
  activity: string
) => {
  return FetchConfig({
    anonymous: false,
    query: `
      mutation UpdateDropdownField(
        $fieldName: String!
        $fieldValue: String!
        $workPackageId: Int
        $storename: String!
        $groupname: String!
        $activity: String!
      ) {
        updateOpenprojectCustomField(
          input: {
            fieldName: $fieldName
            fieldValue: $fieldValue
            workPackageId: $workPackageId
            storename: $storename
            groupname: $groupname
            activity: $activity
          }
        ) {
          clientMutationId
          string
        }
      }
    `,
    variables: {
      fieldName,
      fieldValue,
      workPackageId,
      storename,
      groupname,
      activity,
    },
  }).then((response: IFetchConfigReturn) => {
    return response?.data?.updateOpenprojectCustomField;
  });
};
export const fetchMailField = () => {
  const variables = {};
  return FetchConfig({
    anonymous: false,
    Realm: String(process.env.REACT_APP_KEYCLOAK_REALM),
    query: `mutation getMailField {
      statelessServiceReportsGetLrpMail(input: {}) {
        results {
      logDate
      message
      toEmail
    }
      }
    }
    `,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceReportsGetLrpMail.results;
  });
};
export const updateScheduledMail = (emails: string, message: string) => {
  const variables = {
    pEmails: emails,
    pMessage: message,
  };

  return FetchConfig({
    anonymous: false,
    query: `
      mutation updateScheduledMail($pEmails: String, $pMessage: String) {
        statelessServiceReportsLprReportEmails( input: {pEmails: $pEmails, pMessage: $pMessage}) {
         string
        }
      }
    `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response?.data?.statelessServiceReportsLprReportEmails.string;
  });
};
