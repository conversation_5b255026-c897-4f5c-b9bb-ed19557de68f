import FetchConfig from "./fetchConfig";
import { IFetchConfigReturn } from "../types";

// Mutations

export const GetSimtTraces = (input: {
  startDate: String;
  endDate: String;
  reportType: String;
}) => {
  const variables = {
    startDate: input.startDate,
    endDate: input.endDate,
    reportType: input.reportType,
  };
  return FetchConfig({
    anonymous: false,
    Realm: String(process.env.REACT_APP_KEYCLOAK_REALM),
    query: `mutation GetSimtTraces($startDate: String, $endDate: String,$reportType:String) {
      statelessServiceReportsGetSimtTraces(input: {startDate: $startDate,endDate: $endDate,reportType: $reportType}) {
        results {
     

        estTime
      eventTime
      isFrom
      istTime
      name
      pageUrl
      realmId
      serviceName
      storeName
      userIp
      userLocation
      userName
      utcTime
      value
      isData

    }
      }
    }
    `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return (
      response &&
      response.data &&
      response.data.statelessServiceReportsGetSimtTraces &&
      response.data.statelessServiceReportsGetSimtTraces.results
    );
  });
};

export const GetEventDetails = (input: {
  realmId: String;
  storeId: String;
  dateEvent: String;
  username: String;
  traceid: String;
}) => {
  const variables = {
    realmId: input.realmId,
    storeId: input.storeId,
    dateEvent: input.dateEvent,
    username: input.username,
    traceid: input.traceid,
  };
  return FetchConfig({
    anonymous: false,
    Realm: String(process.env.REACT_APP_KEYCLOAK_REALM),
    query: `mutation GetEventDetails($realmId: String, $storeId: String,$dateEvent:String,$username:String,$traceid:String) {
      statelessServiceReportsGetEventDetails(input: {realmId: $realmId,storeId: $storeId,dateEvent: $dateEvent,username: $username,traceid: $traceid}) {
        results {
     

      estTime
      eventTime
      isFrom
      istTime
      name
      pageUrl
      realmId
      serviceName
      storeName
      userIp
      userLocation
      userName
      utcTime
      value
      isData

    }
      }
    }
    `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return (
      response &&
      response.data &&
      response.data.statelessServiceReportsGetEventDetails &&
      response.data.statelessServiceReportsGetEventDetails.results
    );
  });
};

export const GetUserAccessData = (input: {
  startDate: String;
  endDate: String;
}) => {
  const variables = { startDate: input.startDate, endDate: input.endDate };
  return FetchConfig({
    anonymous: false,
    Realm: String(process.env.REACT_APP_KEYCLOAK_REALM),
    query: `mutation GetUserAccessData ($startDate: String, $endDate: String){
      statelessServiceReportsGetUserAccessData( input: {startDate: $startDate,endDate: $endDate}) {
        results {
      accessDate
      accessDuration
      accessDurationSeconds
      realmId
      storeName
      userName
      traceid
    }
      }
    }
    `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceReportsGetUserAccessData.results;
  });
};

//Query
export const FopcTelemetryReports = () => {
  const variables = {};

  return FetchConfig({
    anonymous: false,
    query: `
      query FopcTelemetryReports {
        statelessServiceReportsFopcTelemetryReports {
      
 edges {
      node {
        reportName
        reportDisplayname
        displayOrder
      }
    }

        }
      }
    `,
    variables: variables,
  }).then((response) => {
    return response.data.statelessServiceReportsFopcTelemetryReports.edges.map(
      (edge: any) => edge.node
    );
  });
};

const DataFetchQueries = {
  FopcTelemetryReports,
  GetUserAccessData,
  GetSimtTraces,
  GetEventDetails,
};
export default DataFetchQueries;
