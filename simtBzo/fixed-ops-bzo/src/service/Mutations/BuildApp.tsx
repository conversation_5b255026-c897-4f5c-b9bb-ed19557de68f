import FetchConfig from "../fetchConfig";
import { IFetchConfigReturn } from "../../types";

const GetBuildRun = () => {
  return FetchConfig({
    anonymous: false,
    query: `mutation GetBuildRun {
  statelessServiceBzoBuildSimtUiWithStatus(input: {}) 
     {
      string
    }
  
}      
    `,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoBuildSimtUiWithStatus;
  });
};

const GetApplicationActions = () => {
  const variables = {};
  return FetchConfig({
    anonymous: false,
    query: `query GetApplicationActions {
        statelessServiceBzoApplicationActions {
          nodes {
           action
      application
      createdAt
      environment
      id
      isEnabled
      keyValue
      updatedAt
          }
        }
      }`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoApplicationActions.nodes;
  });
};
const GetBuildRestartSimtEnv = (input: any) => {
  const variables = {
    pAction: input.pAction,
    pEnvironment: input.pEnvironment,
    pService: input.pService,
  };

  return FetchConfig({
    anonymous: false,
    query: `
      mutation GetBuildRun($pAction: String!, $pEnvironment: String!, $pService: String!) {
        statelessServiceBzoBuildRestartSimtEnvironment(input: {
          pAction: $pAction,
          pEnvironment: $pEnvironment,
          pService: $pService
        }) {
          string
        }
      }
    `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoBuildRestartSimtEnvironment;
  });
};

const BuildQuerries = {
  GetBuildRun,
  GetApplicationActions,
  GetBuildRestartSimtEnv,
};

export default BuildQuerries;
