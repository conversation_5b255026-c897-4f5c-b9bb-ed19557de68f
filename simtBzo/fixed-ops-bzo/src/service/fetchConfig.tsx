import { traceSpan } from "../utils/OTTTracing";

interface IHeader {
  anonymous: boolean;
  authHeader: string;
}

interface IFetchConfig {
  anonymous: boolean;
  Realm?: any;
  storeid?: any;
  tenantid?: any;
  inStoreId?: any;
  inTenantId?: any;
  method?: string;
  query: string;
  variables?: any;
}

interface IFetchConfigReturn {
  status: number;
  data?: any;
  error?: any;
}

const { REACT_APP_API_URL } = process.env;
const Header = ({ anonymous, authHeader }: IHeader) => {
  const minimal = {
    "Content-Type": "application/json",
    Accept: "application/json",
    appid: "bzo",
    //   appid: "contact-orchestrator",
    //   realm: "Medigy",
    //   "auth-strategy": "next",
  };
  return anonymous
    ? minimal
    : { ...minimal, Authorization: `Bearer ${authHeader}` };
};

function extractApiName(query: string): string {
  try {
    // Remove comments and extra whitespace
    const cleanQuery = query.replace(/\s+/g, " ").trim();

    // Extract operation name for named queries (e.g., "query GetUsers" or "mutation CreateUser")
    const namedOperationMatch = cleanQuery.match(
      /(?:query|mutation|subscription)\s+([a-zA-Z_][a-zA-Z0-9_]*)/i
    );
    if (namedOperationMatch) {
      return namedOperationMatch[1];
    }

    // Extract first field name for unnamed queries
    const fieldMatch = cleanQuery.match(
      /(?:query|mutation|subscription)?\s*\{\s*([a-zA-Z_][a-zA-Z0-9_]*)/i
    );
    if (fieldMatch) {
      return fieldMatch[1];
    }

    // Fallback
    return "UnknownOperation";
  } catch (error) {
    return "UnknownOperation";
  }
}

async function FetchConfig({
  anonymous,
  Realm,
  storeid,
  tenantid,
  inTenantId,
  inStoreId,
  method = "POST",
  query,
  variables = {},
}: IFetchConfig): Promise<IFetchConfigReturn> {
  return await new Promise((resolve, reject) => {
    const authHeader = String(localStorage.getItem("access_token"));
    const url: any = anonymous ? REACT_APP_API_URL : REACT_APP_API_URL;
    const headers: HeadersInit = Header({ anonymous, authHeader });
    //const actualRealm = Realm ? Realm : String(process.env.REACT_APP_KEYCLOAK_REALM);
    const actualRealm = String(process.env.REACT_APP_KEYCLOAK_REALM);
    headers["Realm"] = actualRealm;
    headers["Storeid"] = storeid ? storeid : "";
    headers["tenantid"] = tenantid ? tenantid : "";
    headers["inTenantId"] = inTenantId ? inTenantId : "";
    headers["inStoreId"] = inStoreId ? inStoreId : "";
    const apiName = extractApiName(query);
    fetch(url, {
      method: method,
      headers: headers,

      body: JSON.stringify({
        query,
        variables,
      }),
    })
      .then((response) => response.json())
      .then((data) => {
        const spanAttribute = {
          event: "Menu Load",
          pageUrl: window.location.pathname,
          timestamp: new Date().toISOString(),
          userId: localStorage.getItem("user") || "",
          is_from: apiName,
        };
        traceSpan("Menu Load", spanAttribute);
        resolve({ status: 200, data: data.data, error: data.errors || [] });
      })
      .catch((error) => {
        console.log("error fetch", error);
        reject({ status: 400, error });
      });
  });
}

export default FetchConfig;
