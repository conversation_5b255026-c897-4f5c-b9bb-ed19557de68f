import FetchConfig from "../fetchConfig";
import { IFetchConfigReturn } from "../../types";

// const GetGridTypeMaster = (storeId: string, tenantId: string) => {
//   const variables = {
//     storeId,
//     tenantId,
//   };

//   return FetchConfig({
//     anonymous: false,
//     query: `query GetGridTypeMaster(
//       $storeId: String!
//       $tenantId: String!
//     ) {
//       statelessServiceBzoGetLaborGridTypeMasterDetails(
//         inStoreId: $storeId
//         inTenantId: $tenantId
//       ) {
//          laborGridTypeMasterDetails {
//           classificationType
//           gridCount
//           isDefaultGridType
//           storeId
//           tenantId
//           value
//         }
//       }
//     }`,
//     variables,
//   }).then((response: IFetchConfigReturn) => {
//     return response.data.statelessServiceBzoGetLaborGridTypeMasterDetails
//       .laborGridTypeMasterDetails;
//   });
// };

const GetGridTypeMaster = (storeId: string, tenantId: string) => {
  const variables = {
    storeId,
    tenantId,
  };

  return FetchConfig({
    anonymous: false,
    query: `mutation GetGridTypeMaster(
      $storeId: String!
      $tenantId: String!
      ) {
  statelessServiceBzoGetLaborGridTypeMasterDetails(input: {
     inStoreId: $storeId
        inTenantId: $tenantId
  }) {
    laborGridTypeMasterDetails {
     classificationType
          gridCount
          isDefaultGridType
          storeId
          tenantId
          value
    }
  }
}`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoGetLaborGridTypeMasterDetails
      .laborGridTypeMasterDetails;
  });
};

const LaborGridQueries = {
  GetGridTypeMaster,
};

export default LaborGridQueries;
