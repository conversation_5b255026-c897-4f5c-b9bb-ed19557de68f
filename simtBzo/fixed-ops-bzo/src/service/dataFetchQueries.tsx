import FetchConfig from "./fetchConfig";
import { IFetchConfigReturn } from "../types";
import { traceSpan } from "../utils/OTTTracing";

export const GetDmsList = () => {
  return FetchConfig({
    anonymous: false,
    query: `query GetDmsList() {
      dmsMasters() {
        nodes {
          id
          dms
          dmsImg
          createdTime
          lastUpdatedOn
          lastUpdatedBy
        }
      }
    }`,
  }).then((response: IFetchConfigReturn) => {
    //  const spanAttribute = {
    //     pageUrl:window.location.pathname,
    //     event: 'DMS List',
    //     is_from: 'GetDmsList',
    //     timestamp: new Date().toISOString(),
    //   };
    //   traceSpan('DMS List', spanAttribute);
    return response.data.dmsMasters.nodes[0];
  });
};

export const GetGroupingDetailsQuery = (tenantId: string) => {
  const variables = {
    tenantId: tenantId,
  };
  return FetchConfig({
    anonymous: false,
    query: `query statelessServiceBzoGroupingMasterDetails($tenantId: String!) {
      statelessServiceBzoGroupingMasterDetails(
        condition: { tenantId: $tenantId }
      ) {
        nodes {
          groupDesc
          groupId
          groupName
          id
          storeId
          storeName
          tenantId
        }
      }
    }`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    // const spanAttribute = {
    //     pageUrl:window.location.pathname,
    //     event: 'Menu Load',
    //     is_from: 'GetGroupingDetailsQuery',
    //     timestamp: new Date().toISOString(),
    //   };
    //   traceSpan('Menu Load', spanAttribute);
    return response.data.statelessServiceBzoGroupingMasterDetails.nodes;
  });
};
export const GetOpcodeQuery = (input: any) => {
  const variables = {
    pDms: input.dms,
    pTenantId: input.tenantId,
    pStoreId: input.storeId,
  };
  return FetchConfig({
    anonymous: false,
    Realm: String(process.env.REACT_APP_KEYCLOAK_REALM),
    query: `mutation statelessServiceBzoGetOnboardingOpcodeCategorization(
      $pDms: String!
      $pStoreId: String!
      $pTenantId: String!
    ) {
      statelessServiceBzoGetOnboardingOpcodeCategorization(
        input: { pDms: $pDms, pStoreId: $pStoreId, pTenantId: $pTenantId }
      ) {
        statelessServiceBzoOnboardingOpcodeCategorizations {
          additionalDescription
          cprocount
          elr
          id
          lbrlabortype
          lbropcode
          lbropcodedesc
          opcategory
          retailqualhours
          retailqualsale
          totalhours
          totallabordollars
          department
        }
      }
    }`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    // const spanAttribute = {
    //     pageUrl:window.location.pathname,
    //     event: 'Opcode',
    //     is_from: 'GetOpcodeQuery',
    //     timestamp: new Date().toISOString(),
    //   };
    //   traceSpan('Opcode', spanAttribute);
    return response.data.statelessServiceBzoGetOnboardingOpcodeCategorization
      .statelessServiceBzoOnboardingOpcodeCategorizations;
  });
};
export const GetOpcodeChoiceQuery = (pType: string) => {
  const variables = {
    pType: pType,
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation statelessServiceBzoGetClassificationMasterDetails($pType: String!) {
      statelessServiceBzoGetClassificationMasterDetails(
        input: { pType: $pType }
      ) {
        statelessServiceBzoClassificationMasterDetails {
          value
        }
      }
    }`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    // const spanAttribute = {
    //     pageUrl:window.location.pathname,
    //     event: 'Opcode Choice',
    //     is_from: 'GetOpcodeChoiceQuery',
    //     timestamp: new Date().toISOString(),
    //   };
    //   traceSpan('Opcode Choice', spanAttribute);
    return response.data.statelessServiceBzoGetClassificationMasterDetails
      .statelessServiceBzoClassificationMasterDetails;
  });
};
export const GetManufacturersList = () => {
  return FetchConfig({
    anonymous: false,
    query: `query statefulServiceBzoValidMakes {
      statefulServiceBzoValidMakes {
        nodes {
          manufacturer
          nodeId
        }
      }
    }`,
  }).then((response: IFetchConfigReturn) => {
    // const spanAttribute = {
    //     pageUrl:window.location.pathname,
    //     event: 'Menu Load',
    //     is_from: 'GetManufacturersList',
    //     timestamp: new Date().toISOString(),
    //   };
    //   traceSpan('Menu Load', spanAttribute);
    return response.data.statefulServiceBzoValidMakes.nodes;
  });
};
export const GetOnboardingChecklist = (input: any) => {
  const variables = {
    pReqType: input.reqType,
    pTenant: input.tenantId,
    pStore: input.store,
  };
  return FetchConfig({
    anonymous: false,
    query: `query statelessServiceBzoGetOnboardingChecklist($pReqType: String!, $pTenant: String!, $pStore: String) {
      statelessServiceBzoGetOnboardingChecklist(
        pReqType: $pReqType
        pTenant: $pTenant
        pStore: $pStore
      ) {
        nodes {
          checklist
          createdOn
          displayName
          displayOrder
          lastupdatedBy
          lastupdatedOn
          processName
          storeId
          status
          storeName
          tenantId
          tenantName
        }
      }
    }`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    // const spanAttribute = {
    //     pageUrl:window.location.pathname,
    //     event: 'Menu Load',
    //     is_from: 'GetOnboardingChecklist',
    //     timestamp: new Date().toISOString(),
    //   };
    //   traceSpan('Menu Load', spanAttribute);
    return response.data.statelessServiceBzoGetOnboardingChecklist.nodes;
  });
};

export const GetAllTechnicians = (tenantid: any, storeid: any) => {
  const variables = {
    inTenantId: tenantid,
    inStoreId: storeid,
  };
  return FetchConfig({
    anonymous: false,
    query: `query getAllTechnicians($inTenantId: String!, $inStoreId: String!) {
      statelessServiceBzoStoreTechnicianDetails(
        inTenantId: $inTenantId
        inStoreId: $inStoreId
      ) {
        nodes {
          id
          active
          lbrtechno
          name
          nickname
          categorized
          
        }
      }
    }`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    // const spanAttribute = {
    //     pageUrl:window.location.pathname,
    //     event: 'All Technicians',
    //     is_from: 'GetAllTechnicians',
    //     timestamp: new Date().toISOString(),
    //   };
    //   traceSpan('All Technicians', spanAttribute);
    return response.data.statelessServiceBzoStoreTechnicianDetails.nodes;
  });
};

export const GetPayTypeMasterDetails = (tenantId: any, storeId: any) => {
  const variables = {
    inStoreId: storeId,
    inTenantId: tenantId,
  };
  return FetchConfig({
    anonymous: false,
    storeid: storeId,
    query: `query getPayTypeMaster($inStoreId: String!,$inTenantId: String!) {
      statelessServiceBzoGetPayTypeMaster (inTenantId: $inTenantId, inStoreId: $inStoreId
      ){
        nodes {
          department
          id
          lbrOrPrts
          payType
          payTypeCode
        }
      }
    }
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    // const spanAttribute = {
    //     pageUrl:window.location.pathname,
    //     event: 'Menu Load',
    //     is_from: 'GetPayTypeMasterDetails',
    //     timestamp: new Date().toISOString(),
    //   };
    //   traceSpan('Menu Load', spanAttribute);
    return response.data.statelessServiceBzoGetPayTypeMaster.nodes;
  });
};
const GetAllServiceAdvDetails = (tenantid: string, storeid: string) => {
  const variables = {
    inTenantId: tenantid,
    inStoreId: storeid,
  };
  return FetchConfig({
    anonymous: false,
    query: `query statelessServiceBzoStoreAdvisorDetails($inTenantId: String!, $inStoreId: String!) {
      statelessServiceBzoStoreAdvisorDetails(
        inTenantId: $inTenantId
        inStoreId: $inStoreId
      ) {
        nodes {
          id
          name
          serviceadvisor
          active
          nickname
          categorized
        }
      }
    }`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    // const spanAttribute = {
    //     pageUrl:window.location.pathname,
    //     event: 'All Service Adv Details',
    //     is_from: 'GetAllServiceAdvDetails',
    //     timestamp: new Date().toISOString(),
    //   };
    //   traceSpan('All Service Adv Details', spanAttribute);
    return response.data.statelessServiceBzoStoreAdvisorDetails.nodes;
  });
};
const GetRealmStatus = (id: any) => {
  const variables = {
    tenantMasterId: id,
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation GetRealmStatus($tenantMasterId: Int!) {
      getRealmStatus(input: {tenantMasterId: $tenantMasterId}) {
        clientMutationId
        string
      }
    }
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    // const spanAttribute = {
    //     pageUrl:window.location.pathname,
    //     event: 'Realm Status',
    //     is_from: 'GetRealmStatus',
    //     timestamp: new Date().toISOString(),
    //   };
    //   traceSpan('Realm Status', spanAttribute);
    return response.data.getRealmStatus.string;
  });
};
const GetLaborGridList = (input: any) => {
  const variables = {
    tenantId: input.tenantId,
    pCallType: input.callType,
    pStore: input.storeId,
    pGridType: input.gridType ? input.gridType : null,
    pGridFor: input.gridFor ? input.gridFor : null,
    pCreatedDate: input.createdDate ? input.createdDate : null,
    inTenantId: input.tenantId,
  };
  return FetchConfig({
    anonymous: false,
    Realm: input.realm,
    storeid: input.storeId,
    query: `query GetLaborGridList($pCallType: String!
      $pStore: String!
      $pGridType: String
      $pCreatedDate: Date
      $pGridFor: String
      $tenantId: String
      ) {
      statelessServiceBzoGetKpiScorecardLaborGrid(
        pCallType: $pCallType
        pStore: $pStore
        pGridType: $pGridType
        pCreatedDate: $pCreatedDate
        pGridFor: $pGridFor
        inTenantId: $tenantId
      ) {
        nodes ${
          input.callType === "Grid_Type"
            ? `{
          createdDate
          gridType
          storeInstallDate
          gridOrder
          gridFor

        }`
            : `{
          col0
          col1
          col2
          col3
          col4
          col5
          col6
          col7
          col8
          col9
          hours
        }`
        }
      }
    }
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    // const spanAttribute = {
    //     pageUrl:window.location.pathname,
    //     event: 'Menu Load',
    //     is_from: 'GetLaborGridList',
    //     timestamp: new Date().toISOString(),
    //   };
    //   traceSpan('Menu Load', spanAttribute);
    return response.data.statelessServiceBzoGetKpiScorecardLaborGrid.nodes;
  });
};

export const GetOnboardingLaunchReports = (tenantId: any) => {
  const variables = {
    tenantId: tenantId,
  };
  return FetchConfig({
    anonymous: false,
    query: `query MyQuery ($tenantId: String) {
      statelessServiceBzoTenantOnboardingLaunchReports (condition: { tenantId: $tenantId }){
        nodes {
          bulkdatapull
          bulkdatapullcomments
          credentialemailaddedtostorefolderforlaunch
          credentialemailaddedtostorefolderforlaunchcomments
          credentialsverifiedwithstore
          credentialsverifiedwithstorecomments
          daysdealerbeginssendingenrollmentitemstoallitemsin
          daysdealerbeginssendingenrollmentitemstoallitemsincomments
          daysfromdealsignedtostorelaunched
          daysfromdealsignedtostorelaunchedcomments
          dealerbeginstosenditems
          dealerbeginstosenditemscomments
          dms
          dmsproviderconfirms
          dmsproviderconfirmscomments
          enrollmentemailtostore
          enrollmentemailtostorecomments
          enrollmentrecieved
          enrollmentrecievedcomments
          fileuploadvalidationresultonbzo
          fileuploadvalidationresultonbzocomments
          fopcadminaccess
          fopcadminaccesscomments
          fopcsiteaccessreadytolaunch
          fopcsiteaccessreadytolaunchcomments
          labourgridfromstore
          labourgridfromstorecomments
          launchdatetarget
          launchdatetargetcomments
          leadcredit
          monthlysalesreportsbystoreandadvisor
          monthlysalesreportsbystoreandadvisorcomments
          notes
          opcodescategorizedinbzo
          opcodescategorizedinbzocomments
          partspricingmatrixaddedinbzo
          partspricingmatrixaddedinbzocomments
          partspricingmatrixrecievedfromstore
          partspricingmatrixrecievedfromstorecomments
          repairpricingpolicylabourgridupdatedinbzo
          repairpricingpolicylabourgridupdatedinbzocomments
          rooftops
          salesreportprovidedtoengineeringteamfordatavalidation
          salesreportprovidedtoengineeringteamfordatavalidationcomments
          signedagreement
          signedagreementcomments
          sitereadyforreview
          sitereadyforreviewcomments
          store
          storelaunched
          storelaunchedcomments
          storenamingdropdownordersenttoengineeringteam
          storenamingdropdownordersenttoengineeringteamcomments
          storesubmitsfordmsaccess
          storesubmitsfordmsaccesscomments
          tenant
          tenantandstoredetailsaddedinbzo
          tenantandstoredetailsaddedinbzocomments
          updatecrmfopcinfoagreement
          updatecrmfopcinfoagreementcomments
        }
      }
    }
    
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    // const spanAttribute = {
    //     pageUrl:window.location.pathname,
    //     event: 'Menu Load',
    //     is_from: 'GetOnboardingLaunchReports',
    //     timestamp: new Date().toISOString(),
    //   };
    //   traceSpan('Menu Load', spanAttribute);
    return response.data.statelessServiceBzoTenantOnboardingLaunchReports.nodes;
  });
};
export const GetLaunchReport = () => {
  return FetchConfig({
    anonymous: false,
    query: `query MyQuery {
      statelessServiceBzoTenantsLauncheds {
        nodes {
          count
          tenantId
          tenantName
          tenantImg
        }
      }
    }    
  `,
  }).then((response: IFetchConfigReturn) => {
    // const spanAttribute = {
    //     pageUrl:window.location.pathname,
    //     event: 'Menu Load',
    //     is_from: 'GetLaunchReport',
    //     timestamp: new Date().toISOString(),
    //   };
    //   traceSpan('Menu Load', spanAttribute);
    return response.data.statelessServiceBzoTenantsLauncheds.nodes;
  });
};

export const GetGridTypeOptions = (
  tenantId: any,
  storeId: any,
  realmName: any
) => {
  const variables = {
    tenantId: tenantId,
    storeId: storeId,
  };
  return FetchConfig({
    anonymous: false,
    query: `query MyQuery ($tenantId:String!,$storeId:String!) {
      statelessServiceBzoPartsMatrixTypeMasterDetails(inStoreId: $storeId, inTenantId: $tenantId) {
        nodes {
          classificationType
          storeId
          tenantId
          value
        }
      }
    } 
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    // const spanAttribute = {
    //     pageUrl:window.location.pathname,
    //     event: 'Menu Load',
    //     is_from: 'GetGridTypeOptions',
    //     timestamp: new Date().toISOString(),
    //   };
    //   traceSpan('Menu Load', spanAttribute);
    return response.data.statelessServiceBzoPartsMatrixTypeMasterDetails.nodes;
  });
};
const GetActivityLog = (input: any) => {
  const variables =
    input.spec === "storeSpecific"
      ? {
          startDate: input.startDate,
          endDate: input.endDate,
          storeId: input.storeId,
        }
      : input.spec === "tenantSpecific"
      ? {
          startDate: input.startDate,
          endDate: input.endDate,
          tenantId: input.tenantId,
        }
      : {
          startDate: input.startDate,
          endDate: input.endDate,
        };
  return FetchConfig({
    anonymous: false,
    Realm: String(process.env.REACT_APP_KEYCLOAK_REALM),
    query:
      input.spec === "storeSpecific"
        ? `query GetActivityLog($startDate: Datetime, $endDate: Datetime, $storeId: String) {
      statelessServiceBzoActivityLogs(
        filter: {createdTime: {greaterThanOrEqualTo: $startDate, lessThanOrEqualTo: $endDate}}
        condition: {storeId: $storeId}
      ) {
        nodes {
          activity
          createdTime
          createdUser
          status
          module
        }
      } 
    }
    `
        : input.spec === "tenantSpecific"
        ? `query GetActivityLog($startDate: Datetime, $endDate: Datetime, $tenantId: String) {
    statelessServiceBzoActivityLogs(
      filter: {createdTime: {greaterThanOrEqualTo: $startDate, lessThanOrEqualTo: $endDate}}
      condition: {tenantId: $tenantId}
    ) {
      nodes {
        activity
        createdTime
        createdUser
        status
        storeName
        module
      }
    } 
  }
  `
        : `query GetActivityLog($startDate: Datetime, $endDate: Datetime) {
      statelessServiceBzoActivityLogs(
        filter: {createdTime: {greaterThanOrEqualTo: $startDate, lessThanOrEqualTo: $endDate}}
      ) {
        nodes {
          activity
          createdTime
          createdUser
          slno
          status
          storeName
          tenantName
          module
        }
      } 
    }
    `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    // const spanAttribute = {
    //     pageUrl:input.spec,
    //     origin: '',
    //     event: 'Menu Load',
    //     is_from: 'GetActivityLog',
    //     timestamp: new Date().toISOString(),
    //   };
    //   traceSpan('Menu Load', spanAttribute);
    return response.data.statelessServiceBzoActivityLogs.nodes;
  });
};
export const loadMenuModelData = (tenantId: any, storeId: any) => {
  const variables = {
    inTenantId: tenantId,
    inStoreId: storeId,
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation getMenuModalData(
      $inTenantId: String
      $inStoreId: String
       ) {
      statelessServiceBzoGetMenuModels(input: {
        inStoreId: $inStoreId
         inTenantId: $inTenantId
      }) {
        getModels {
         make
        menuName
          modelName
        }
      }
    }
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    // const spanAttribute = {
    //     pageUrl:window.location.pathname,
    //     event: 'Menu Load',
    //     is_from: 'GetActivityLog',
    //     timestamp: new Date().toISOString(),
    //   };
    //   traceSpan('Menu Load', spanAttribute);
    return response.data.statelessServiceBzoGetMenuModels.getModels;
  });
};

const GetGeneralMainNeverEvents = (clientId: string) => {
  const variables = {
    clientId: clientId,
  };
  return FetchConfig({
    anonymous: false,
    Realm: String(process.env.REACT_APP_KEYCLOAK_REALM),
    query: `query getGeneralMainNeverEvents($clientId: String!) {
      getGeneralMainNeverEvents(condition: {clientId: $clientId}) {
        nodes {
          clientname
          optionName
          statusData
          statusDate
          storeId
          storeName
          clientId
          optionDisplayName
          subDomain
        }
      }
    } 
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    // const spanAttribute = {
    //     pageUrl:window.location.pathname,
    //     event: 'Menu Load',
    //     is_from: 'GetGeneralMainNeverEvents',
    //     timestamp: new Date().toISOString(),
    //   };
    //   traceSpan('Menu Load', spanAttribute);
    return response.data.getGeneralMainNeverEvents.nodes;
  });
};
const GetGeneralMainErrorStatuses = () => {
  return FetchConfig({
    anonymous: false,
    Realm: String(process.env.REACT_APP_KEYCLOAK_REALM),
    query: `query getGeneralMainErrorStatuses {
      getGeneralMainErrorStatuses {
        nodes {
          clientId
          clientName
          errCount
        }
      }
    }
     
  `,
  }).then((response: IFetchConfigReturn) => {
    // const spanAttribute = {
    //     pageUrl:window.location.pathname,
    //     event: 'Menu Load',
    //     is_from: 'GetGeneralMainErrorStatuses',
    //     timestamp: new Date().toISOString(),
    //   };
    //   traceSpan('Menu Load', spanAttribute);
    return response.data.getGeneralMainErrorStatuses.nodes;
  });
};
const GetGeneralNeverEvents = (clientId: string) => {
  const variables = {
    clientId: clientId,
  };
  return FetchConfig({
    anonymous: false,
    Realm: String(process.env.REACT_APP_KEYCLOAK_REALM),
    query: `query getGeneralNeverEvents($clientId: String!) {
      getGeneralNeverEvents(condition: {clientId: $clientId}) {
        nodes {
          clientname
          optionName
          statusData
          statusDate
          storeId
          storeName
          clientId
          optionDisplayName
          subDomain
        }
      }
    } 
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    // const spanAttribute = {
    //     pageUrl:window.location.pathname,
    //     event: 'Menu Load',
    //     is_from: 'GetGeneralNeverEvents',
    //     timestamp: new Date().toISOString(),
    //   };
    //   traceSpan('Menu Load', spanAttribute);
    return response.data.getGeneralNeverEvents.nodes;
  });
};
const GetGeneralNeverErrorStatuses = () => {
  return FetchConfig({
    anonymous: false,
    Realm: String(process.env.REACT_APP_KEYCLOAK_REALM),
    query: `query getGeneralNeverErrorStatuses {
      getGeneralNeverErrorStatuses {
        nodes {
          clientId
          clientName
          errCount
        }
      }
    }
     
  `,
  }).then((response: IFetchConfigReturn) => {
    // const spanAttribute = {
    //     pageUrl:window.location.pathname,
    //     event: 'Menu Load',
    //     is_from: 'GetGeneralNeverErrorStatuses',
    //     timestamp: new Date().toISOString(),
    //     userId:localStorage.getItem('user') || ''
    //   };
    //   traceSpan('Menu Load', spanAttribute);
    return response.data.getGeneralNeverErrorStatuses.nodes;
  });
};

const GetDailyLogins = () => {
  return FetchConfig({
    anonymous: false,
    Realm: String(process.env.REACT_APP_KEYCLOAK_REALM),
    query: `query GetDailyLogins{
      dailyLoginDetails{
        nodes {
            details
          }
      }
    }
  `,
  }).then((response: IFetchConfigReturn) => {
    // const spanAttribute = {
    //     pageUrl:window.location.pathname,
    //     event: 'Menu Load',
    //     is_from: 'GetDailyLogins',
    //     timestamp: new Date().toISOString(),
    //   };
    //   traceSpan('Menu Load', spanAttribute);
    return JSON.parse(response.data.dailyLoginDetails.nodes[0].details);
  });
};

const GetDailyDataImportStatusFailed = () => {
  return FetchConfig({
    anonymous: false,
    Realm: String(process.env.REACT_APP_KEYCLOAK_REALM),
    query: `query GetDailyDataImportStatusFailed{
      dailyDataimportStatusFaileds{
        nodes {
          clientname
          dataLoadDate
          enddate
          errMessage
          startdate
          statuss
          storeId
          storeName
          dms
        }
      }
    }
  `,
  }).then((response: IFetchConfigReturn) => {
    // const spanAttribute = {
    //     pageUrl:window.location.pathname,
    //     event: 'Menu Load',
    //     is_from: 'GetDailyDataImportStatusFailed',
    //     timestamp: new Date().toISOString(),
    //     userId:localStorage.getItem('user') || ''
    //   };
    //   traceSpan('Menu Load', spanAttribute);
    return response.data.dailyDataimportStatusFaileds.nodes;
  });
};

const GetDataAsOf = () => {
  return FetchConfig({
    anonymous: false,
    Realm: String(process.env.REACT_APP_KEYCLOAK_REALM),
    query: `query GetDataAsOf{
      statelessServiceBzoDataAsOfs {
    nodes {
      storeName
      tenantName
      tenant
      value
      dms
      subDomain
    }
  }
    }
  `,
  }).then((response: IFetchConfigReturn) => {
    // const spanAttribute = {
    //     pageUrl:window.location.pathname,
    //     event: 'Menu Load',
    //     is_from: 'GetDataAsOf',
    //     timestamp: new Date().toISOString(),
    //   };
    //   traceSpan('Menu Load', spanAttribute);
    return response.data.statelessServiceBzoDataAsOfs.nodes;
  });
};

const GetAduUsersList = () => {
  return FetchConfig({
    anonymous: false,
    Realm: String(process.env.REACT_APP_KEYCLOAK_REALM),
    query: `query GetAduUsersList {
  statelessServiceBzoAduUserslists {
    nodes {
      createdAt
      firstName
      lastname
      passwords
      role
      userName
    }
  }
}
  `,
  }).then((response: IFetchConfigReturn) => {
    // const spanAttribute = {
    //     pageUrl:window.location.pathname,
    //     event: 'Menu Load',
    //     is_from: 'GetAduUsersList',
    //     timestamp: new Date().toISOString(),
    //   };
    //   traceSpan('Menu Load', spanAttribute);
    return response.data.statelessServiceBzoAduUserslists.nodes;
  });
};

const GetLaunchReportsList = () => {
  return FetchConfig({
    anonymous: false,
    Realm: String(process.env.REACT_APP_KEYCLOAK_REALM),
    query: `query GetLaunchReportsList {
      tenantOnboardingLaunchReports {
        nodes {
          addEnrollments
          addEnrollmentsId
          addRepairPricingPolicyLaborGridToBzo
          addRepairPricingPolicyLaborGridToBzoId
          addStoreMetaData
          addStoreMetaDataId
          bulkDataPullFromDmsToDb
          bulkDataPullFromDmsToDbId
          configureBzoForStore
          configureBzoForStoreId
          configureGroupFromBzo
          configureGroupFromBzoId
          configureIdentityProvider
          configureIdentityProviderId
          configureKeycloak
          configureKeycloakId
          credentialsVerifiedWithStore
          credentialsVerifiedWithStoreId
          dmsValue
          fileUploadValidationResultOnBzo
          fileUploadValidationResultOnBzoId
          fopcSiteReadyForLaunchAws
          fopcSiteReadyForLaunchAwsId
          fopcSiteReadyForReviewDev
          fopcSiteReadyForReviewDevId
          fopcSiteReadyForReviewUat
          fopcSiteReadyForReviewUatId
          launchDateTarget
          launchDateTargetId
          launchStoreName
          leadCredit
          middlewareConfiguration
          middlewareConfigurationId
          monthlySalesReportTotalShop
          monthlySalesReportTotalShopId
          monthlySalesReportsByStoreAndAdvisor
          monthlySalesReportsByStoreAndAdvisorId
          opcodeCategorization
          opcodeCategorizationId
          partsPricingMatrixAddedToBzo
          partsPricingMatrixAddedToBzoId
          postOnboardingData
          postOnboardingDataId
          recieveConfirmationFromDmsProvider
          recieveConfirmationFromDmsProviderId
          recieveEnrollmentItemsFromStore
          recieveEnrollmentItemsFromStoreId
          requestForDmsAccess
          requestForDmsAccessId
          requestForEnrollmentItemsFromStore
          requestForEnrollmentItemsFromStoreId
          roofTops
          settingUpDailyLoad
          settingUpDailyLoadId
          setupKeycloak
          setupKeycloakId
          setupSiteAccessForStoreAdminAndGroupUsers
          setupSiteAccessForStoreAdminAndGroupUsersId
          signedAgreement
          signedAgreementId
          siteReadyForReview
          siteReadyForReviewId
          slno
          storeNaming
          storeNamingId
          storeProjectId
          storeProjectName
          tenantProjectId
          tenantProjectName
          totalStoreProjectCount
          updateCrmAgreement
          updateCrmAgreementId
        }
      }
    }
    `,
  }).then((response: IFetchConfigReturn) => {
    // const spanAttribute = {
    //     pageUrl:window.location.pathname,
    //     event: 'Menu Load',
    //     is_from: 'GetLaunchReportsList',
    //     timestamp: new Date().toISOString(),
    //   };
    //   traceSpan('Menu Load', spanAttribute);
    return response.data.tenantOnboardingLaunchReports.nodes;
  });
};

export const loadMenuNames = (storeId: any, tenantId: any) => {
  const variables = {
    inStoreId: storeId,
    inTenantId: tenantId,
  };
  return FetchConfig({
    anonymous: false,
    storeid: storeId,
    query: ` mutation getMenuNames($inStoreId: String, $inTenantId: String) {
      statelessServiceBzoGetMenunames(input: { inStoreId: $inStoreId, inTenantId:  $inTenantId}) {
        getMenuNames {
          menuEnable
          menuName
          menuIsdefault
        }
      }
    }
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    // const spanAttribute = {
    //     pageUrl:window.location.pathname,
    //     event: 'Menu Load',
    //     is_from: 'loadMenuNames',
    //     timestamp: new Date().toISOString(),
    //   };
    //   traceSpan('Menu Load', spanAttribute);
    return response?.data?.statelessServiceBzoGetMenunames?.getMenuNames;
  });
};

export const loadMenuDetails = (menuName: any, storeId: any, tenantId: any) => {
  const variables = {
    storeid: storeId,
    menuname: menuName,
    inTenantId: tenantId,
  };
  return FetchConfig({
    anonymous: false,
    storeid: storeId,
    query: `mutation menuDetails($storeid: String, $menuname: String, $inTenantId: String) {
      statelessServiceBzoGetMenuDetails(
        input: { storeid: $storeid, menuname: $menuname, inTenantId: $inTenantId }
      ) {
        results {
          intervalList
          jsonGeneral
          jsonOpcodeList
        }
      }
    }
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    // const spanAttribute = {
    //     pageUrl:window.location.pathname,
    //     event: 'Menu Load',
    //     is_from: 'menuDetails',
    //     timestamp: new Date().toISOString(),
    //   };
    //   traceSpan('Menu Load', spanAttribute);
    return response?.data?.statelessServiceBzoGetMenuDetails?.results;
  });
};

export const fetchMenuNames = (input: any, realmName: any) => {
  const variables = {
    inStoreId: input.storeid,
    username: input.username,
    inMenuname: input.inMenuname,
    inServicetype: input.inServicetype,
    inTenantId: input.tenantId,
  };
  return FetchConfig({
    anonymous: false,
    storeid: input.storeid,
    query: `mutation getMenuData(
      $inStoreId: String
      $username: String
      $inMenuname: String
      $inServicetype: Int
      $inTenantId: String
    ) {
      statelessServiceBzoGetMenuData(
        input: {
          inStoreId: $inStoreId
          username: $username
          inMenuname: $inMenuname
          inServicetype: $inServicetype
           inTenantId: $inTenantId
        }
      ) {
        getMdata {
          categoryList
          mName
          mInterval
          mServicetype
          mPrice
          mFrh
          mItems
          mOpcodes
          mSeries
        }
      }
    }
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    // const spanAttribute = {
    //     pageUrl:window.location.pathname,
    //     event: 'Menu Load',
    //     is_from: 'getMenuData',
    //     timestamp: new Date().toISOString(),
    //   };
    //   traceSpan('Menu Load', spanAttribute);
    return response.data.statelessServiceBzoGetMenuData.getMdata;
  });
};
export const getFilteredOpcode = (input: any, realmName: any) => {
  const variables = {
    inStoreId: input.storeId,
    menuName: input.inMenuname,
    inTenantId: input.tenantId,
  };
  return FetchConfig({
    anonymous: false,
    storeid: input.storeId,
    query: `mutation getFilteredOpcode($inStoreId: String, $menuName: String, $inTenantId: String) {
      statelessServiceBzoGetFilteredMenuopcodes(
        input: { menuName: $menuName, inStoreId: $inStoreId, inTenantId: $inTenantId }
      ) {
        getFilteredMenuopcodesViews {
          opcodeList
        }
      }
    }
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    // const spanAttribute = {
    //     pageUrl:window.location.pathname,
    //     event: 'Menu Load',
    //     is_from: 'getFilteredOpcode',
    //     timestamp: new Date().toISOString(),
    //   };
    //   traceSpan('Menu Load', spanAttribute);
    return response.data.statelessServiceBzoGetFilteredMenuopcodes
      .getFilteredMenuopcodesViews;
  });
};
export const getMenuPopUp = (input: any, realmName: any) => {
  const variables = {
    storeid: input.storeid,
    menuName: input.inMenuname,
    inTenantId: input.tenantId,
  };
  return FetchConfig({
    anonymous: false,
    storeid: input.storeid,
    query: `mutation menuPopup($storeid: String, $menuName: String,  $inTenantId: String) {
      statelessServiceBzoGetMenuPopup(
        input: { storeid: $storeid, argmenuName: $menuName, inTenantId: $inTenantId }
      ) {
        results {
          afterMiles
          beforeMiles
          maxMiles
          menuName
          milegaeInterval
        }
      }
    }
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    // const spanAttribute = {
    //     pageUrl:window.location.pathname,
    //     event: 'Menu Load',
    //     is_from: 'menuPopup',
    //     timestamp: new Date().toISOString(),
    //   };
    //   traceSpan('Menu Load', spanAttribute);
    return response.data.statelessServiceBzoGetMenuPopup.results;
  });
};

export const getMenuServiceType = (input: any, realmName: any) => {
  const variables = {
    inTenantId: input.tenantId,
    inStoreId: input.storeId,
  };
  return FetchConfig({
    anonymous: false,
    query: `query getMenuServiceType($inTenantId: String!, $inStoreId: String!) {
      statelessServiceBzoGetMenuServiceType(inTenantId: $inTenantId,inStoreId: $inStoreId) {
         nodes {
          id
          serviceType
        }
      }
    } 
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    // const spanAttribute = {
    //     pageUrl:window.location.pathname,
    //     event: 'Menu Load',
    //     is_from: 'getMenuServiceType',
    //     timestamp: new Date().toISOString(),
    //   };
    //   traceSpan('Menu Load', spanAttribute);
    return response.data.statelessServiceBzoGetMenuServiceType.nodes;
  });
};

const GetQaTestContent = (testDate: string) => {
  const variables = {
    testDate: testDate,
  };
  return FetchConfig({
    anonymous: false,
    Realm: String(process.env.REACT_APP_KEYCLOAK_REALM),
    // storeid: input.storeid,
    query: `query GetQaTestContent($testDate: Date!) {
      statelessServiceReportsSmokeTestResults(condition: {testDate: $testDate}) {
          nodes {
            tenant
            store
            content
            type
            date
            userRole
          }
        }
    }
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    // const spanAttribute = {
    //     pageUrl:window.location.pathname,
    //     event: 'Menu Load',
    //     is_from: 'GetQaTestContent',
    //     timestamp: new Date().toISOString(),
    //   };
    //   traceSpan('Menu Load', spanAttribute);
    return response?.data?.statelessServiceReportsSmokeTestResults?.nodes;
  });
};

export const getRevenueSummaryRevenueByPaytype = () => {
  return fetch("/data/RevenueSummaryGetAnalysisFsRevenueByPaytype.json")
    .then((res) => res.json())
    .then((data) => {
      // const spanAttribute = {
      //   pageUrl:window.location.pathname,
      //   event: 'Menu Load',
      //   is_from: 'getRevenueSummaryRevenueByPaytype',
      //   timestamp: new Date().toISOString(),
      // };
      // traceSpan('Menu Load', spanAttribute);
      return data.data.statelessDbdRevenueSummaryGetAnalysisFsRevenueByPaytype
        .nodes;
    })
    .catch((err) => {
      console.error(err);
    });
};
export const loadMonthYears = () => {
  return fetch("/data/rdDrilldownGetMonthYears.json")
    .then((res) => res.json())
    .then((data) => {
      // const spanAttribute = {
      //   pageUrl:window.location.pathname,
      //   event: 'Menu Load',
      //   is_from: 'loadMonthYears',
      //   timestamp: new Date().toISOString(),
      // };
      // traceSpan('Menu Load', spanAttribute);
      return data.data.statelessCcDrilldownGetMonthYears.nodes;
    })
    .catch((err) => {
      console.error(err);
    });
};

export const getRevenueSummaryLaborRevenueByCategory = () => {
  return fetch(
    "/data/RevenueSummaryGetAnalysisFsCustomerLbrRevenueByCategory.json"
  )
    .then((res) => res.json())
    .then((data) => {
      // const spanAttribute = {
      //   pageUrl:window.location.pathname,
      //   event: 'Menu Load',
      //   is_from: 'getRevenueSummaryLaborRevenueByCategory',
      //   timestamp: new Date().toISOString(),
      // };
      // traceSpan('Menu Load', spanAttribute);
      return data.data
        .statelessDbdRevenueSummaryGetAnalysisFsCustomerLbrRevenueByCategory
        .nodes;
    })
    .catch((err) => {
      console.error(err);
    });
};

export const getRevenueSummaryPartsRevenueByCategory = () => {
  return fetch(
    "/data/RevenueSummaryGetAnalysisFsCustomerPrtsRevenueByCategory.json"
  )
    .then((res) => res.json())
    .then((data) => {
      // const spanAttribute = {
      //   pageUrl:window.location.pathname,
      //   event: 'Menu Load',
      //   is_from: 'getRevenueSummaryPartsRevenueByCategory',
      //   timestamp: new Date().toISOString(),
      // };
      // traceSpan('Menu Load', spanAttribute);
      return data.data
        .statelessDbdRevenueSummaryGetAnalysisFsCustomerPrtsRevenueByCategory
        .nodes;
    })
    .catch((err) => {
      console.error(err);
    });
};

export const getRevenueSummaryLaborRevenueByComponent = () => {
  return fetch(
    "/data/RevenueSummaryGetAnalysisFsCustomerLbrRevenueByComponents.json"
  )
    .then((res) => res.json())
    .then((data) => {
      // const spanAttribute = {
      //   pageUrl:window.location.pathname,
      //   event: 'Menu Load',
      //   is_from: 'getRevenueSummaryLaborRevenueByComponent',
      //   timestamp: new Date().toISOString(),
      // };
      // traceSpan('Menu Load', spanAttribute);
      return data.data
        .statelessDbdRevenueSummaryGetAnalysisFsCustomerLbrRevenueByComponents
        .nodes;
    })
    .catch((err) => {
      console.error(err);
    });
};

export const getRevenueSummaryPartsRevenueByComponent = () => {
  return fetch(
    "/data/RevenueSummaryGetAnalysisFsCustomerPrtsRevenueByComponents.json"
  )
    .then((res) => res.json())
    .then((data) => {
      // const spanAttribute = {
      //   pageUrl:window.location.pathname,
      //   event: 'Menu Load',
      //   is_from: 'getRevenueSummaryPartsRevenueByComponent',
      //   timestamp: new Date().toISOString(),
      // };
      // traceSpan('Menu Load', spanAttribute);
      return data.data
        .statelessDbdRevenueSummaryGetAnalysisFsCustomerPrtsRevenueByComponents
        .nodes;
    })
    .catch((err) => {
      console.error(err);
    });
};

export const getRevenueSummaryCustomerJobLevelBkdown = () => {
  return fetch("/data/RevenueSummaryGetAnalysisFsCustomerJobLevelBkdown.json")
    .then((res) => res.json())
    .then((data) => {
      // const spanAttribute = {
      //   pageUrl:window.location.pathname,
      //   event: 'Menu Load',
      //   is_from: 'getRevenueSummaryCustomerJobLevelBkdown',
      //   timestamp: new Date().toISOString(),
      // };
      // traceSpan('Menu Load', spanAttribute);
      return data.data
        .statelessDbdRevenueSummaryGetAnalysisFsCustomerJobLevelBkdown.nodes;
    })
    .catch((err) => {
      console.error(err);
    });
};

export const getRevenueSummaryCustomerJobLevelBkdownPerc = () => {
  return fetch(
    "/data/RevenueSummaryGetAnalysisFsCustomerJobLevelBkdownPerc.json"
  )
    .then((res) => res.json())
    .then((data) => {
      // const spanAttribute = {
      //   pageUrl:window.location.pathname,
      //   event: 'Menu Load',
      //   is_from: 'getRevenueSummaryCustomerJobLevelBkdownPerc',
      //   timestamp: new Date().toISOString(),
      // };
      // traceSpan('Menu Load', spanAttribute);
      return data.data
        .statelessDbdRevenueSummaryGetAnalysisFsCustomerJobLevelBkdownPerc
        .nodes;
    })
    .catch((err) => {
      console.error(err);
    });
};

export const getRevenueSummaryWarrantyVolumesLabor = () => {
  return fetch("/data/getRevenueSummaryWarrantyVolumesLabor.json")
    .then((res) => res.json())
    .then((data) => {
      // const spanAttribute = {
      //   pageUrl:window.location.pathname,
      //   event: 'Menu Load',
      //   is_from: 'getRevenueSummaryWarrantyVolumesLabor',
      //   timestamp: new Date().toISOString(),
      // };
      // traceSpan('Menu Load', spanAttribute);
      return data.data
        .statelessDbdRevenueSummaryGetAnalysisFsWarrantyVolumesLaborByMonth
        .nodes;
    })
    .catch((err) => {
      console.error(err);
    });
};

export const getRevenueSummaryWarrantyVolumesParts = () => {
  return fetch("/data/getRevenueSummaryWarrantyVolumesParts.json")
    .then((res) => res.json())
    .then((data) => {
      // const spanAttribute = {
      //   pageUrl:window.location.pathname,
      //   event: 'Menu Load',
      //   is_from: 'getRevenueSummaryWarrantyVolumesParts',
      //   timestamp: new Date().toISOString(),
      // };
      // traceSpan('Menu Load', spanAttribute);
      return data.data
        .statelessDbdRevenueSummaryGetAnalysisFsWarrantyVolumesPartsByMonth
        .nodes;
    })
    .catch((err) => {
      console.error(err);
    });
};

const DataFetchQueries = {
  GetGeneralMainNeverEvents,
  GetGeneralMainErrorStatuses,
  GetGeneralNeverEvents,
  GetGeneralNeverErrorStatuses,
  GetGroupingDetailsQuery,
  GetOpcodeQuery,
  GetOpcodeChoiceQuery,
  GetAllServiceAdvDetails,
  GetAllTechnicians,
  GetPayTypeMasterDetails,
  GetRealmStatus,
  GetLaborGridList,
  GetOnboardingLaunchReports,
  GetLaunchReport,
  GetGridTypeOptions,
  GetActivityLog,
  GetDailyDataImportStatusFailed,
  loadMenuModelData,
  GetLaunchReportsList,
  GetQaTestContent,
  GetDataAsOf,
  GetDailyLogins,
  GetAduUsersList,
};

export default DataFetchQueries;
