import { ApolloClient, InMemoryCache, HttpLink, from, ApolloLink } from "@apollo/client";
import { onError } from "@apollo/client/link/error";

const apolloConfig = () => {
    const authMiddleware = new ApolloLink((operation, forward) => {
      operation.setContext({
        headers: {
          authorization: localStorage.getItem("access_token")
            ? `Bearer ${localStorage.getItem("access_token")}`
            : "",
          "auth-strategy": "next",
          appid: "bzo",
          realm: String(process.env.REACT_APP_KEYCLOAK_REALM),
        },
      });
  
      return forward(operation);
    });
  
  const httpLink = new HttpLink({
    uri: process.env.REACT_APP_API_URL,
  });

  const errorLink = onError(({ graphQLErrors, networkError }) => {
    if (graphQLErrors)
      graphQLErrors.forEach(({ message, locations, path }) =>
        console.log(
          `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
        )
      );

    if (networkError) console.log(`[Network error]: ${networkError}`);
  });

  // If you provide a link chain to ApolloClient, you
  // don't provide the `uri` option.
  const client = new ApolloClient({
    // The `from` function combines an array of individual links
    // into a link chain
    link: from([errorLink, authMiddleware, httpLink]),
    cache: new InMemoryCache(),
  });
  return client;
};
export default apolloConfig;
