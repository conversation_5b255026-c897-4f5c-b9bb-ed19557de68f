import React from "react";
import { <PERSON>, Tooltip, Typography, useTheme } from "@mui/material";
import { AgGridReact } from "ag-grid-react";
import FileDownloadIcon from "@mui/icons-material/FileDownload";
import { Constants } from "../../utils/constants";
import AllTenantsGridDefs from "./AllTenantsGridDefs";
import useAllTenantsList from "../../CustomHooks/useAllTenantsList";
import { traceSpan } from "../../utils/OTTTracing";
import { SortChangedEvent } from "ag-grid-community";

const AllTenantsList = () => {
  const { columnDefs, defaultColDef, onBtnExport, setApi } =
    AllTenantsGridDefs();
  const { onGridReady, allTenantsList } = useAllTenantsList();
  const theme = useTheme();
  const handleGridReady = (params: { api: any }) => {
    setApi(params.api);
    onGridReady(params);
  };

  const onFilterChanged = (e: any) => {
    const filterValues = e.api.getFilterModel();
    Object.keys(filterValues).forEach((colId) => {
      traceSpan(`filter_grid_${colId}`, {
        event: `filter_grid_${colId}`,
        pageUrl: window.location.pathname,
        timestamp: new Date().toISOString(),
        userId: localStorage.getItem("user") || "",
        column: colId,
        filterValue: JSON.stringify(filterValues[colId]),
      });
    });
  };
const onSortChanged = (params: any) => {
  // Get column states and filter only sorted columns
  const sortModel = params.columnApi.getColumnState()
    .filter((col: any) => col.sort != null)
    .map((col: any) => ({
      colId: col.colId,
      sort: col.sort
    }));
  sortModel.forEach((sortItem: { colId: string; sort: 'asc' | 'desc' }) => {
    traceSpan(`sort_grid_${sortItem.colId}`, {
      pageUrl: window.location.pathname,
      event: `sort_grid_${sortItem.colId}`,
      column: sortItem.colId,
      direction: sortItem.sort,
      timestamp: new Date().toISOString(),
      userId: localStorage.getItem('user') || ''
    });
  });
};



  return (
    <Box sx={{ paddingX: "10px", mt: "15px", width: "100%" }}>
      <Box sx={{ display: "flex", justifyContent: "space-between" }}>
        <Box></Box>
        <Typography
          sx={{
            fontSize: "18px",
            fontWeight: "bold",
            color: Constants.colors.modalHeading,
          }}>
          All Tenants
        </Typography>
        <Tooltip title="Export To Excel">
          <div>
            <FileDownloadIcon
              onClick={() => {
                traceSpan(`download_excel_alltenants`, {
                  event: `download_excel_alltenants`,
                  pageUrl: window.location.pathname,
                  timestamp: new Date().toISOString(),
                  userId: localStorage.getItem("user") || "",
                });
                onBtnExport();
              }}
              style={{ color: theme.palette.primary.main, cursor: "pointer" }}
            />
          </div>
        </Tooltip>
      </Box>

      <Box sx={{ width: "100%", marginTop: "15px" }}>
        <div
          className={Constants.ag_grid_theme}
          style={{ height: "85vh", marginTop: 2, maxWidth: "100vw" }}>
          <AgGridReact
            columnDefs={columnDefs}
            rowData={allTenantsList}
            defaultColDef={defaultColDef}
            onSortChanged={onSortChanged}
            onGridReady={handleGridReady}
            pagination={true}
            onFilterChanged={onFilterChanged}
          />
        </div>
      </Box>
    </Box>
  );
};

export default AllTenantsList;
