import { Link as MuiLink } from "@mui/material";
import { ColDef } from "ag-grid-community";
import React, { useMemo, useState } from "react";
import { ICellRendererParams } from "ag-grid-community";
import CancelIcon from "@mui/icons-material/Cancel";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import moment from "moment";
import { exportMultipleSheetsAsExcel } from "ag-grid-enterprise";

const AllTenantsGridDefs = () => {
  const [api, setApi] = useState<any>(null);

  const statusFont = (params: any) => {

    
    return {
      color:
        params.value == "Completed"
          ? "green"
          : params.value == "Pending"
          ? "red"
          : params.value == "In Progress"
          ? "orange"
          : "black",
      fontWeight: "bold",
    };
  };

  const columnDefs = useMemo<ColDef[]>(
    () => [
      {
        field: "tenantName",
        headerName: "Tenant",
        flex: 4,
        headerStyle: {
          backgroundColor: "#2196f3",
          color: "white",
          fontWeight: "bold",
        },
      },
      {
        field: "agreementDate",
        headerName: "Contract Signed Date",
        flex: 2,
        valueFormatter: (params: any) =>
          params.value ? moment(params.value).format("MM/DD/YYYY") : "---",
      },
      {
        field: "activeStoreCount",
        headerName: "No. of Stores",
        flex: 1,
      },
      {
        field: "infrastructureConfiguration",
        headerName: "Infrastructure Setup",
        flex: 2,
        cellStyle: statusFont,
      },
      {
        field: "subDomain",
        headerName: "FOPC Domain",
        flex: 5,
        cellRenderer: (params: any) => {
          if (!params.data?.subDomain) return null;
          return (
            <MuiLink
              href={`https://${params.data.subDomain}/`}
              target="_blank"
              rel="noopener noreferrer"
            >
              {params.data.subDomain}
            </MuiLink>
          );
        },
      },
    ],
    []
  );

  const defaultColDef = useMemo<ColDef>(
    () => ({
      cellStyle: { textAlign: "left" },
      resizable: true,
      suppressMenu: true,
      unSortIcon: true,
      enableValue: true,
      sortable: true,
      floatingFilter: true,
      filter: true,
      wrapHeaderText: true,
      autoHeaderHeight: true,
      wrapText: true,
      autoHeight: true,
      width: 180,
      suppressSizeToFit: true,
    }),
    []
  );

  const handleExport = (api: any, fileName: string, sheetName: string) => {
    const spreadsheets: any[] = [];
    spreadsheets.push(api.getSheetDataForExcel({ sheetName }));

    exportMultipleSheetsAsExcel({
      data: spreadsheets,
      fileName,
    });
  };

  const onBtnExport = () => {
    handleExport(api!, "All Tenants.xlsx", "All Tenant List");
  };

  return {
    columnDefs,
    defaultColDef,
    onBtnExport,
    setApi
  };
};

export default AllTenantsGridDefs;
