import { <PERSON>, <PERSON><PERSON>, Modal, Typography } from "@mui/material";
import { DatePicker } from "antd";
import React from "react";
import dayjs from "dayjs";
import { DatesObject } from "../../types";

interface Props {
  openConfirmModal: boolean;
  setOpenConfirmModal: (value: boolean) => void;
  cancelId: string;
  cancelledDate: DatesObject;
  setCancelledDate: (value: DatesObject) => void;
  handleInputChange: (
    value: string | string[],
    input: string,
    id: string | number
  ) => void;
  handleSave: (action: string) => void;
  modal: string;
}
const CancelModal: React.FC<Props> = ({
  openConfirmModal,
  setOpenConfirmModal,
  cancelId,
  cancelledDate,
  setCancelledDate,
  handleInputChange,
  handleSave,
  modal,
}) => {
  return (
    <Modal
      open={openConfirmModal}
      onClose={() => setOpenConfirmModal(false)}
      aria-labelledby="modal-modal-title"
      aria-describedby="modal-modal-description"
    >
      <Box
        sx={{
          display: "flex",
          position: "absolute" as "absolute",
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)",
          bgcolor: "background.paper",
          border: "1px solid #6a6a6a",
          boxShadow: 24,
          p: "16px 24px",
          borderRadius: "4px",
          flexDirection: "column",
        }}
      >
        <Typography
          sx={{ fontSize: "14px", fontFamily: "Montserrat", fontWeight: 800 }}
        >
          {modal === "cancel"
            ? "Cancel Subscription"
            : modal === "cancelReview"
            ? "Cancel Review"
            : modal === "cancelApprove"
            ? "Approve cancellation"
            : modal === "cancelDeny"
            ? "Deny cancellation"
            : ""}
        </Typography>
        <Typography
          sx={{
            fontSize: "12px",
            fontFamily: "Montserrat",
            fontWeight: 500,
            margin: "8px 0",
          }}
        >
          {modal === "cancel"
            ? "Are you sure you want to cancel this subscription?"
            : modal === "cancelReview"
            ? "Are you sure you want to cancel the review?"
            : modal === "cancelApprove"
            ? "Are you sure you want to approve this cancellation?"
            : modal === "cancelDeny"
            ? "Are you sure you want to deny this cancellation?"
            : ""}
        </Typography>
        {modal === "cancel" && (
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              flex: 1,
              width: "211px",
            }}
          >
            <Typography
              fontWeight={500}
              fontSize={"12px"}
              fontFamily={"Montserrat"}
            >
              Cancellation Effective
            </Typography>
            <DatePicker
              popupStyle={{ zIndex: 9999 }}
              placeholder="Select Date"
              format={"MM/DD/YYYY"}
              value={cancelledDate[cancelId] && cancelledDate[cancelId]}
              onChange={(date, dateString) => {
                setCancelledDate({
                  [cancelId]: dayjs(date),
                });
                handleInputChange(dateString, "cancelledDate", cancelId);
              }}
            ></DatePicker>
          </Box>
        )}
        <Box sx={{ display: "flex", justifyContent: "end", marginTop: "16px" }}>
          <Button
            sx={{
              marginX: "4px",
              textTransform: "none",
              fontFamily: "Montserrat",
              backgroundColor:
                modal === "cancel" && !cancelledDate[cancelId]
                  ? "#e1e1e1"
                  : "#59aaaa",
              color: "white",
              width: "100px",
              padding: "5px",
              "&:hover": {
                backgroundColor: "#208f8f",
              },
            }}
            disabled={modal === "cancel" && !cancelledDate[cancelId]}
            onClick={() => handleSave(modal)}
          >
            Confirm
          </Button>
          <Button
            sx={{
              marginX: "4px",
              textTransform: "none",
              fontFamily: "Montserrat",
              backgroundColor: "#59aaaa",
              color: "white",
              width: "100px",
              padding: "5px",
              "&:hover": {
                backgroundColor: "#208f8f",
              },
            }}
            onClick={() => {
              setOpenConfirmModal(false);
              (modal === "cancel" || modal === "cancelDeny") && setCancelledDate({});
            }}
          >
            Cancel
          </Button>
        </Box>
      </Box>
    </Modal>
  );
};

export default CancelModal;
