import { Box, Typography } from "@mui/material";
import { <PERSON><PERSON><PERSON> } from "react-minimal-pie-chart";

export const PieChartComponent = (props: any) => {
  const { data, allBugs, category } = props;

  return (
    <Box
      sx={{
        display: "flex",
        // flex: 1,
        width: "28%",
        flexDirection: "column",
        height: "fit-content",
        mt: "15px",
      }}
    >
      <Typography
        variant="h6"
        sx={{
          fontWeight: "bold",
          display: "flex",
          justifyContent: "center",
          color: "rgb(0, 61, 107)",
        }}
        marginBottom={-15}
      >
        {category === "open"
          ? "Open Bugs"
          : category === "closed"
          ? "Closed Bugs"
          : category === "priority"
          ? "Bug Priority"
          : category === "data"
          ? "Data Related Bugs"
          : category === "performance"
          ? "Performance Related Bugs"
          : "UI Related Bugs"}
      </Typography>
      <PieChart
        animate
        radius={20}
        lineWidth={65}
        paddingAngle={3}
        data={data}
      />
      <Box
        className="legend"
        display="flex"
        flexWrap="wrap"
        gap={2}
        justifyContent="space-evenly"
        marginX={10}
        marginTop={-15}
      >
        {(category === "open" || category === "closed") &&
          allBugs.map(
            (entry: any) =>
              ((category === "open" && entry.label === "Total Open") ||
                (category === "closed" && entry.label === "Total Closed")) && (
                <Typography
                  style={{
                    textAlign: "left",
                    fontWeight: "bold",
                    fontSize: "14px",
                    width: "100%",
                  }}
                >
                  {entry.label} Bugs: {entry.value}
                </Typography>
              )
          )}

        {data.map((entry: any, index: any) => (
          <Box
            display="flex"
            alignItems="center"
            gap={1}
            width="47%"
            justifyContent={"center"}
          >
            <Box
              width={16}
              height={16}
              bgcolor={entry.color}
              borderRadius={1}
              flex={1}
            >
              {" "}
            </Box>
            <Box flex={8}>
              {" "}
              <Typography style={{ fontWeight: "bold", fontSize: "12px" }}>
                {entry.label}
              </Typography>{" "}
              <Typography style={{ fontSize: "12px" }}>
                Count: {entry.value}
              </Typography>{" "}
            </Box>
          </Box>
        ))}
      </Box>
    </Box>
  );
};
