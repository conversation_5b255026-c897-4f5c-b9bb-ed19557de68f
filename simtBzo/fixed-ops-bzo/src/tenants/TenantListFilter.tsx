/* eslint-disable import/no-anonymous-default-export */
import * as React from "react";
import {
  FilterList,
  FilterLiveSearch,
  FilterListItem,
  useGetIdentity,
  Button,
  useGetList,
} from "react-admin";
import { Box, Grid } from "@mui/material";
import BusinessIcon from "@mui/icons-material/Business";
import { Link as RouterLink } from "react-router-dom";
import EditIcon from "@mui/icons-material/Edit";
import { PageRoutes } from "../utils/pageRoutes";

export const TenantListFilter = () => {
  const { data, isLoading } = useGetList("dmsMasters");
  if (isLoading) return null;

  return (
    <Box width="13em" minWidth="13em" order={-1} mr={2} mt={7}>
      {/* <FilterLiveSearch source="tenant_name" /> */}
      <Grid container>
        <Grid item xs={9}>
          <FilterList label="DMS" sx={{"& .css-b3fch5-MuiTypography-root": {fontSize: "0.95rem", fontWeight: "bold"} }} icon={<BusinessIcon />}>
            {/* {data?.map((item: any) => (
              <FilterListItem
                key={item?.dms}
                label={item?.dms}
                value={{ dms: item?.dms }}
              />
            ))} */}
          </FilterList>
        </Grid>
        <Grid item xs={3} mt={2}>
          <Button component={RouterLink} to={PageRoutes.dmsMasters}>
            <EditIcon />
          </Button>
        </Grid>
      </Grid>
    </Box>
  );
};
