import React, { useState } from "react";
import IconContentAdd from "@material-ui/icons/Add";
import EditIcon from "@mui/icons-material/Edit";
import {
  required,
  TextInput,
  useNotify,
  ImageInput,
  ImageField,
  Form,
  useRecordContext,
  useRefresh,
  useTranslate,
  SaveButton,
} from "react-admin";
import Dialog from "@material-ui/core/Dialog";
import { DialogContent, DialogTitle } from "@material-ui/core";
import { Box, Button } from "@mui/material";
import CancelIcon from "@mui/icons-material/Cancel";
import { MutateDms } from "../service/mutations";
import { DmsListRecord } from "../types";
import { Constants } from "../utils/constants";
import SnackBarMessage from "../components/SnackBarMessage";
import { traceSpan } from "../utils/OTTTracing";

export default function DmsQuickCreate(props: { record?: DmsListRecord }) {
  const [showDialog, setShowDialog] = useState(false);
  const record = useRecordContext<DmsListRecord>(props);
  const notify = useNotify();
  const refresh = useRefresh();
  const translate = useTranslate();
  const [base64Image, setBase64Image] = useState(
    record?.dmsImg ? record.dmsImg : ""
  );
  const [imagePreviewSource, setImagePreviewSource] = useState(
    record?.dmsImg ? Constants.url : Constants.src
  );
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [statusMessage, setStatusMessage] = useState<any>("");
  const updateDmsMessage = translate("SUCCESS_MESSAGES.UPDATE_MESSAGE", {
    entityName: Constants.dms,
  });
  const createDmsMessage = translate("SUCCESS_MESSAGES.CREATE_MESSAGE", {
    entityName: Constants.dms,
  });
  const handleClick = () => {
    setShowDialog(true);
  };

  const handleCloseClick = () => {
    setShowDialog(false);
    setImagePreviewSource(Constants.url);
  };
  const onSubmit = async (data?: any) => {
    try {
      const updatedData = { ...data, dmsImg: base64Image };
      const action = record
        ? Constants.actions.update
        : Constants.actions.insert;
      const result = await MutateDms(
        action,
        updatedData.dms,
        updatedData.dmsImg
      );

      if (result.string === Constants.success) {
        refresh();
        const message = record ? updateDmsMessage : createDmsMessage;
        setStatusMessage(message);
        setOpenSnackbar(true);
      }

      handleCloseClick();
    } catch (error) {
      console.error(error);
      notify(translate("ERROR_MESSAGES.SOMETHING_WENT_WRONG"));
    }
  };

  const convertImageToBase64 = (file: any) => {
    const reader: any = new FileReader();
    if (file) {
      reader.readAsDataURL(file);
      reader.onloadend = () => {
        setBase64Image(reader.result);
      };
    } else {
      setBase64Image("");
    }
    setImagePreviewSource(Constants.src);
  };

  function formatLogo(value: any) {
    if (!value || typeof value === Constants.type.string) {
      // Value is null or the url string from the backend, wrap it in an object so the form input can handle it
      return { url: value };
    } else {
      // Else a new image is selected which results in a value object already having a preview link under the url key
      return value;
    }
  }

  const validateStoreCreation = (values: any) => {
    const errors: any = {};
    Object.entries(values).map(([field, value]: any) => {
      if (!value && field !== "dmsImg")
        errors[field] = translate("VALIDATION_MESSAGES.REQUIRED");
      else if (
        field === "dms" &&
        !Constants.patterns.alphanumericStoreNameRegex.test(value)
      )
        errors[field] = translate(
          "VALIDATION_MESSAGES.ALPHANUMERIC_WITH_HYPHEN"
        );
      return errors;
    });
    return errors;
  };
  return (
    <>
      <Box sx={{ "& button": { m: 1 } }}>
        <Button
          onClick={handleClick}
          startIcon={record ? <EditIcon /> : <IconContentAdd />}>
          {record ? translate("BUTTONS.EDIT") : translate("BUTTONS.CREATE")}
        </Button>
      </Box>
      <Dialog
        fullWidth
        open={showDialog}
        aria-label={record ? "Edit dms" : "Create dms"}>
        <DialogTitle>
          {record
            ? translate("TITLES.Edit_Dms")
            : translate("TITLES.Create_Dms")}
        </DialogTitle>
        <Form onSubmit={onSubmit} validate={validateStoreCreation}>
          <DialogContent>
            <TextInput
              label={translate("LABELS.DMS")}
              source="dms"
              validate={required()}
              fullWidth
              disabled={record ? true : false}
              onBlur={(e) => {
                traceSpan("dms_input_field", {
                  event: "dms_input_field",
                  pageUrl: window.location.pathname,
                  timestamp: new Date().toISOString(),
                  userId: localStorage.getItem("user") || "",
                  value: e.target.value,
                });
              }}
            />
            <ImageInput
              format={formatLogo}
              source="dmsImg"
              label={translate("LABELS.LOGO")}
              accept="image/*"
              onChange={(e: any) => {
                convertImageToBase64(e);
              }}>
              <ImageField source={imagePreviewSource} />
            </ImageInput>
          </DialogContent>
          <Box sx={{ p: 2 }}>
            <SaveButton />
            <Button
              color="primary"
              variant="contained"
              startIcon={<CancelIcon />}
              sx={{ m: 2 }}
              onClick={() => handleCloseClick()}>
              {translate("BUTTONS.CANCEL")}
            </Button>
          </Box>
        </Form>
      </Dialog>
      <SnackBarMessage
        onClose={() => setOpenSnackbar(false)}
        open={openSnackbar}
        message={statusMessage}
      />
    </>
  );
}
