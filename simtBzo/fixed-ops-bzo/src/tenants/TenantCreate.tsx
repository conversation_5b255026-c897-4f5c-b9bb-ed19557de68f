import * as React from "react";
import { Create, Form, SaveButton, useTranslate } from "react-admin";
import { Card<PERSON>ontent, Stack, Box, Button } from "@mui/material";
import { TenantForm } from "./TenantForm";
import { useNavigate } from "react-router-dom";
import DataMutationQueries from "../service/mutations";
import { Link as RouterLink } from "react-router-dom";
import CancelIcon from "@mui/icons-material/Cancel";
import { Constants } from "../utils/constants";
import { useParams } from "react-router-dom";
import { useState, useEffect } from "react";
import { TenantRecord } from "../types";
import { PageRoutes } from "../utils/pageRoutes";
import SnackBarMessage from "../components/SnackBarMessage";
import Loading from "../components/Loading";
import useTenantDetails from "../CustomHooks/useTenantDetails";
import dayjs from "dayjs";
import TenantQueries from "../service/DataFetchQueries/tenantQueries";
import { traceSpan } from "../utils/OTTTracing";
export const TenantCreate = () => {
  const { id } = useParams();
  const {
    formatLogo,
    agreementDate,
    setAgreementDate,
    updateDate,
    setUpdateDate,
    base64Image,
    setBase64Image,
    openSnackbar,
    setOpenSnackbar,
    statusMessage,
    setStatusMessage,
    onTenantFormSubmit,
    agreementDateMsg,
    setAgreementDateMsg,
  } = useTenantDetails(id);
  const translate = useTranslate();
  const navigate = useNavigate();
  const [tenantData, setTenantData] = useState<any>();
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [imagePreviewSource, setImagePreviewSource] = useState(
    id ? Constants.url : Constants.src
  );
  const { GetTenantQuery } = TenantQueries;
  const { InsertWorkPackage, InsertWorkPackageHierarchies } =
    DataMutationQueries;
  useEffect(() => {
    if (id) {
      GetTenantQuery(Number(id)).then((data: any) => {
        data.agreementDate && setAgreementDate(dayjs(data.agreementDate));
        setTenantData(data);
        setBase64Image(data.tenantImg ? data.tenantImg : "");
        setIsLoading(false);
      });
    }
  }, []);

  const convertImageToBase64 = (file: any) => {
    const reader: any = new FileReader();
    if (file) {
      reader.readAsDataURL(file);
      reader.onloadend = () => {
        setBase64Image(reader.result);
      };
    } else {
      setBase64Image("");
    }
    setImagePreviewSource(Constants.src);
  };

  const validateTenantCreation = (values: any) => {
    const errors: any = {};
    Object.entries(values).map(([field, value]: any) => {
      const trimValue =
        typeof value === "string" ? `${value.replace(/ /g, "")}` : "";
      if (!value || trimValue.length === 0) {
        errors[field] =
          field === "tenantImg" || (field === "sub_domain" && values.tenant_id)
            ? {}
            : translate("VALIDATION_MESSAGES.REQUIRED");
      } else {
        errors[field] =
          field === "tenant_name" &&
          !Constants.patterns.alphanumericStoreNameRegex.test(value)
            ? translate("VALIDATION_MESSAGES.ALPHANUMERIC_WITH_HYPHEN")
            : field === "sub_domain" &&
              !Constants.patterns.lowercaseAlphabetRegex.test(value) &&
              !values.tenant_id
            ? (errors[field] = translate(
                "VALIDATION_MESSAGES.LOWERCASE_ALPHABETS_ONLY"
              ))
            : {};
      }
      return errors;
    });
    return errors;
  };

  if (isLoading && id) return <Loading />;
  return (
    <>
      <Create
        actions={false}
        redirect="show"
        sx={{ display: "flex", justifyContent: "space-around" }}>
        <Box width={750}>
          <Form
            defaultValues={
              id
                ? {
                    tenant_name: tenantData?.tenantName,
                    display_name: tenantData?.displayName,
                    tenant_id: tenantData?.tenantId,
                    dms: tenantData?.dms,
                    sub_domain: tenantData?.subDomain,
                    tenantImg: tenantData?.tenantImg,
                  }
                : {}
            }
            onSubmit={onTenantFormSubmit}
            validate={validateTenantCreation}>
            <CardContent>
              <Stack direction="row">
                <Box flex="1">
                  <TenantForm
                    convertImageToBase64={convertImageToBase64}
                    imagePreviewSource={imagePreviewSource}
                    base64Image={base64Image}
                    tenantData={tenantData}
                    agreementDate={agreementDate}
                    setAgreementDate={setAgreementDate}
                    updateDate={updateDate}
                    setUpdateDate={setUpdateDate}
                    agreementDateMsg={agreementDateMsg}
                    setAgreementDateMsg={setAgreementDateMsg}
                    formatLogo={formatLogo}
                  />
                </Box>
              </Stack>
            </CardContent>
            <Box sx={{ p: 2 }}>
              <SaveButton
                alwaysEnable
                onClick={() => {
                  !agreementDate
                    ? setAgreementDateMsg("Required Field")
                    : setAgreementDateMsg("");
                  traceSpan(`click_newtenantsave_button`, {
                    event: `click_newtenantsave_button`,
                    pageUrl: window.location.pathname,
                    timestamp: new Date().toISOString(),
                    userId: localStorage.getItem("user") || "",
                  });
                }}
              />
              <Button
                color="primary"
                variant="contained"
                startIcon={<CancelIcon />}
                sx={{ m: 2 }}
                component={RouterLink}
                to={PageRoutes.statelessServiceBzoTenants}
                onClick={() => {
                  setImagePreviewSource(Constants.url);
                  traceSpan(`click_newtenantcancel_button`, {
                    event: `click_newtenantcancel_button`,
                    pageUrl: window.location.pathname,
                    timestamp: new Date().toISOString(),
                    userId: localStorage.getItem("user") || "",
                  });
                }}>
                {translate("BUTTONS.CANCEL")}
              </Button>
            </Box>
          </Form>
        </Box>
      </Create>
      <SnackBarMessage
        onClose={() => setOpenSnackbar(false)}
        open={openSnackbar}
        message={statusMessage}
      />
    </>
  );
};
