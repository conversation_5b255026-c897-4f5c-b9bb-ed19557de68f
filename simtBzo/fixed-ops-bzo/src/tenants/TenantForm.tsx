import * as React from "react";
import {
  TextInput,
  required,
  useTranslate,
  ImageInput,
  ImageField,
} from "react-admin";
import { InputAdornment, Typography } from "@mui/material";
import { useParams } from "react-router-dom";
import { Constants } from "../utils/constants";
import { DatePicker, DatePickerProps } from "antd";
import dayjs from "dayjs";
import { traceSpan } from "../utils/OTTTracing";

export const TenantForm = (props: any) => {
  const { id } = useParams();
  const {
    formatLogo,
    convertImageToBase64,
    imagePreviewSource,
    agreementDate,
    setAgreementDate,
    updateDate,
    setUpdateDate,
    tenantData,
    agreementDateMsg,
    setAgreementDateMsg,
  } = props;
  const translate = useTranslate();
  const fixedDomain = Constants.FOPCsubDomain;
  const customInputProps = {
    endAdornment: <InputAdornment position="end">{fixedDomain}</InputAdornment>,
  };

  const onDateChange: DatePickerProps["onChange"] = (date, dateString) => {
    traceSpan("select_agreementdate_datepicker", {
      event: "select_agreementdate_datepicker",
      pageUrl: window.location.pathname,
      timestamp: new Date().toISOString(),
      userId: localStorage.getItem("user") || "",
      value: dateString,
    });
    setAgreementDate(date);
    setUpdateDate(true);
    date && setAgreementDateMsg("");
  };

  return (
    <>
      <TextInput
        label={translate("LABELS.DEALERSHIP_NAME")}
        source="tenant_name"
        validate={required()}
        fullWidth
        disabled={id ? true : false}
        onBlur={(e) => {
          traceSpan("input_tenantname_text", {
            event: "input_tenantname_text",
            pageUrl: window.location.pathname,
            timestamp: new Date().toISOString(),
            userId: localStorage.getItem("user") || "",
            value: e.target.value,
          });
        }}
      />
      <TextInput
        label={translate("LABELS.DISPLAY_NAME")}
        source="display_name"
        validate={required()}
        fullWidth
        //disabled={id ? true : false}
        onBlur={(e) => {
          traceSpan("input_displayname_text", {
            event: "input_displayname_text",
            pageUrl: window.location.pathname,
            timestamp: new Date().toISOString(),
            userId: localStorage.getItem("user") || "",
            value: e.target.value,
          });
        }}
      />
      <TextInput
        label="FOPC site address"
        source="sub_domain"
        validate={required()}
        fullWidth
        InputProps={id ? <></> : customInputProps}
        helperText={
          id
            ? translate("MESSAGES.CANNOT_EDIT")
            : translate("MESSAGES.SubDomain_HelperText")
        }
        sx={{
          "& .css-k4qjio-MuiFormHelperText-root": {
            // marginLeft: 0,
          },
        }}
        disabled={id ? true : false}
        onBlur={(e) => {
          traceSpan("input_siteaddress_text", {
            event: "input_siteaddress_text",
            pageUrl: window.location.pathname,
            timestamp: new Date().toISOString(),
            userId: localStorage.getItem("user") || "",
            value: e.target.value,
          });
        }}
      />
      <p
        style={{
          fontSize: 12,
          marginLeft: "4px",
          marginBottom: 0,
          opacity: 0.8,
          color: agreementDateMsg ? "#d32f2f" : "grey",
        }}>
        Agreement Date*
      </p>
      <DatePicker
        defaultValue={
          tenantData?.agreementDate && dayjs(tenantData?.agreementDate)
        }
        size={"large"}
        onChange={onDateChange}
        onFocus={() => {
          traceSpan("agreementdate_datepicker_focused", {
            event: "agreementdate_datepicker_focused",
            pageUrl: window.location.pathname,
            timestamp: new Date().toISOString(),
            userId: localStorage.getItem("user") || "",
          });
        }}
        placeholder="Select Date"
        format={"MM/DD/YYYY"}
        value={agreementDate}
        variant="filled"
        style={{
          width: "100%",
          border: "none",
          borderBottom: agreementDateMsg
            ? "1px solid #d32f2f"
            : "1px solid #958a8a",
          borderRadius: 0,
          padding: "11px",
        }}></DatePicker>
      {agreementDateMsg && (
        <p
          style={{
            fontSize: 12,
            color: "#d32f2f",
            margin: "6px 0 0 14px",
          }}>
          {agreementDateMsg}
        </p>
      )}
      <ImageInput
        format={formatLogo}
        source="tenantImg"
        label={translate("LABELS.TENANT_LOGO")}
        accept="image/*"
        onChange={(e: any) => {
          traceSpan("click_tenantlogo_upload", {
            event: "click_tenantlogo_upload",
            pageUrl: window.location.pathname,
            timestamp: new Date().toISOString(),
            userId: localStorage.getItem("user") || "",
          });

          convertImageToBase64(e);
        }}>
        <ImageField source={imagePreviewSource} />
      </ImageInput>
    </>
  );
};
