import * as React from "react";
import { Avatar } from "@mui/material";
import { useRecordContext } from "react-admin";
import { Tenant } from "../types";

export const TenantAvatar = (props: {
  record?: Tenant;
  size?: "small" | "large";
}) => {
  const { size = "large" } = props;
  const record = useRecordContext<Tenant>(props);
  if (!record) return null;

  return (
    <Avatar
      src={record?.tenantImg}
      alt={record?.name}
      sx={{
        bgcolor: "aliceblue",
        "& img": { objectFit: "contain" },
      }}
      imgProps={{ className: size }}
    />
  );
};
