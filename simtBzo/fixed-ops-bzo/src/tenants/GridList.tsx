import * as React from "react";
import { Box, Card, CardContent, Paper } from "@mui/material";
import { RecordContextProvider, useListContext, useTranslate } from "react-admin";
import { TenantCard } from "./TenantCard";
import { Tenant } from "../types";

const times = (nbChildren: number, fn: (key: number) => any) =>
  Array.from({ length: nbChildren }, (_, key) => fn(key));

const LoadingGridList = () => (
  <Box display="flex" flexWrap="wrap" width="100%" gap={1} >
    {times(15, (key) => (
      <Paper
        sx={{
          height: 230,
          width: 255,
          display: "flex",
          flexDirection: "column",
          backgroundColor: "grey[200]",
        }}
        key={key}
      />
    ))}
  </Box>
);

const LoadedGridList = (props: any) => {
  const {getDashboardCardDetails}= props;
  const translate = useTranslate();
  const { data, isLoading } = useListContext<Tenant>();
  if (isLoading) return null;
  return (
    <Box display="flex" flexWrap="wrap" width="100%" gap={2}>
      {data.length > 0 ? (
        data.map((record) => (
          <RecordContextProvider key={record.id} value={record}>
            <TenantCard getDashboardCardDetails={getDashboardCardDetails}/>
          </RecordContextProvider>
        ))
      ) : (
        <Card
          sx={{
            width: "100%",
            display: "flex",
            justifyContent: "left",
            alignItems: "left",          
          }}
        >
          <CardContent
            sx={{
              color: "grey[500]",
              fontSize: "15px",
              height: "100px",
            }}
          >
            {translate("MESSAGES.NO_TENANTS")}
          </CardContent>
        </Card>
      )}
    </Box>
  );
};

export const ImageList = (props: any) => {
  const {getDashboardCardDetails}= props;
  const { isLoading } = useListContext();
  return isLoading ? <LoadingGridList /> : <LoadedGridList getDashboardCardDetails={getDashboardCardDetails}/>;
};
