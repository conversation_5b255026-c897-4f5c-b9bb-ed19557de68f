import Keycloak from "keycloak-js";

const keycloak = new Keycloak({
  url: process.env.REACT_APP_KEYCLOAK_URL,
  realm: String(process.env.REACT_APP_KEYCLOAK_REALM),
  clientId: String(process.env.REACT_APP_KEYCLOAK_CLIENT_ID),
});

const onToken = () => {
  if (keycloak.token && keycloak.refreshToken) {
    localStorage.setItem(
      "role",
      JSON.stringify(keycloak.realmAccess?.roles[0])
    );    
    localStorage.setItem("access_token", keycloak.token);
    localStorage.setItem("refresh-token", keycloak.refreshToken);
    localStorage.setItem("user", keycloak.idTokenParsed?.preferred_username);
    localStorage.setItem("provenance","bzo")
    localStorage.setItem("userEmail", keycloak.idTokenParsed?.email);
    localStorage.setItem("dashboard", JSON.stringify({ filter: "onboarding" }));
  }
};

const onKeycloakEvent = (event: any, error: any) => {
  if (event === "onTokenExpired") {
    keycloak.updateToken(60);
  }
  // if (event === "onAuthLogout") {
  //   keycloak.authenticated = true;
  // }
  if (event === "onReady" && keycloak.token === undefined) {
    keycloak.updateToken(60);
  }
  if (
    event === "onAuthRefreshError" ||
    event === "onAuthError" ||
    event === "onInitError"
  ) {
  }
};

export { keycloak, onToken, onKeycloakEvent };
