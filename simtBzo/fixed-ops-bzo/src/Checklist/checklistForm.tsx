import Box from "@mui/material/Box";
import Stepper from "@mui/material/Stepper";
import Step from "@mui/material/Step";
import StepLabel from "@mui/material/StepLabel";
import StepContent from "@mui/material/StepContent";
import Button from "@mui/material/Button";
import Typography from "@mui/material/Typography";
import React from "react";
import { GetOnboardingChecklist } from "../service/dataFetchQueries";
import { useNavigate, useParams } from "react-router-dom";
import { useState } from "react";
import { Status } from "../components/Status";
import DataMutationQueries, { SetChecklistStatus } from "../service/mutations";
import { Button as BackButton, Tooltip } from "@mui/material";
import { ArrowBack } from "@mui/icons-material";
import { Constants } from "../utils/constants";
import { PageRoutes } from "../utils/pageRoutes";

export const ChecklistForm = () => {
  const [activeStep, setActiveStep] = React.useState(-1);
  const { id, tenantId, storeId, viewStatus } = useParams();
  const [checklistItems, setChecklistItems] = useState<any>([]);
  const { ConfigureTenant } = DataMutationQueries;
  const navigate = useNavigate();

  const getChecklist = () => {
    return new Promise((resolve) => {
      GetOnboardingChecklist({
        reqType: storeId ? "store" : "common",
        tenantId: tenantId,
        store: storeId ? storeId : "NULL",
      }).then((res: any) => {
        const sortDisplayorder = [...res].sort(
          (a, b) => a.sortOrder - b.sortOrder
        );
        setChecklistItems(sortDisplayorder);
        resolve(sortDisplayorder);
      });
    });
  };
  React.useEffect(() => {
    getChecklist().then((res: any) => {
      if (res) {
        const activeIndex = res.findIndex(
          (step: any) =>
            step.status === Constants.checklistStatus.ongoing ||
            step.status === Constants.checklistStatus.pending
        );
        setActiveStep(activeIndex !== -1 ? activeIndex : 0);
      }
    });
  }, []);

  const handleChecklistStatus = (checklist: string, status: string) => {
    const input = {
      pProcess: Constants.actions.update,
      pStatus: status,
      pChecklist: checklist,
      pTenant: tenantId,
      pStore: storeId ? storeId : "common",
    };
    SetChecklistStatus(input).then(() => {
      getChecklist().then(() => {
        status === Constants.checklistStatus.completed &&
          setActiveStep((prevActiveStep) => prevActiveStep + 1);
      });
    });
  };

  const IsStepActive = (index: string, step: any) => {
    if (!storeId) {
      return Number(index) === activeStep ? true : false;
    } else if (storeId && step.status !== Constants.checklistStatus.completed) {
      return index === "0" ||
        (checklistItems[0].status === Constants.checklistStatus.completed &&
          (index === "1" || index === "2" || index === "4")) ||
        (checklistItems[2].status === Constants.checklistStatus.completed &&
          index === "3") ||
        (Number(index) >= 5 &&
          checklistItems[Number(index) - 1].status ===
            Constants.checklistStatus.completed)
        ? true
        : false;
    } else return false;
  };
  const handleTenantConfigure = () => {
    ConfigureTenant(Number(id)).then((res) => {});
  };
  return (
    <Box>
      <Stepper activeStep={activeStep} orientation="vertical">
        {checklistItems &&
          Object.entries(checklistItems).map(([index, step]: any) => (
            <Step
              key={index}
              active={IsStepActive(index, step)}
              completed={step.status === Constants.checklistStatus.completed}
            >
              <Box display={"flex"} justifyContent={"space-between"}>
                <Tooltip
                  title={
                    storeId &&
                    (index === "3" || index === "5") &&
                    checklistItems[Number(index) - 1].status !==
                      Constants.checklistStatus.completed
                      ? `Step ${
                          Number(index) + 1
                        }: Locked. Complete Step ${index} to proceed.`
                      : ""
                  }
                >
                  <StepLabel
                    sx={{
                      "& .MuiStepLabel-iconContainer": {
                        marginRight: "15px",
                      },
                      "& .MuiStepLabel-labelContainer": {
                        fontSize: "1rem",
                        paddingLeft: "25px",
                      },
                    }}
                  >
                    {step.displayName}
                  </StepLabel>
                </Tooltip>
                <Box
                  display={"flex"}
                  sx={{
                    alignItems: "center",
                    textAlign: "right",
                    padding: "8px 0",
                  }}
                >
                  <Typography
                    sx={{ marginRight: "10px" }}
                    fontSize={"0.875rem"}
                  >
                    {step.status}
                  </Typography>
                  <Status status={step.status} />
                </Box>
              </Box>
              {viewStatus !== "view" && (
                <StepContent
                  sx={{
                    "& .MuiCollapse-root": {
                      marginLeft: "15px",
                      paddingLeft: 5,
                    },
                  }}
                >
                  <Box sx={{ mb: 2 }}>
                    <div>
                      {step.checklist === "tenant_wise_keyclock_config" && (
                        <Button
                          disabled={
                            step.status !== Constants.checklistStatus.pending
                          }
                          onClick={() => handleTenantConfigure()}
                          sx={{ mt: 1, mr: 1 }}
                        >
                          Configure
                        </Button>
                      )}
                      <Button
                        disabled={
                          step.status !== Constants.checklistStatus.pending
                        }
                        onClick={() =>
                          handleChecklistStatus(
                            step.checklist,
                            Constants.checklistStatus.ongoing
                          )
                        }
                        sx={{ mt: 1, mr: 1 }}
                      >
                        Initiate
                      </Button>
                      <Button
                        variant="contained"
                        onClick={() =>
                          handleChecklistStatus(
                            step.checklist,
                            Constants.checklistStatus.completed
                          )
                        }
                        sx={{ mt: 1, mr: 1, padding: "6px 8px" }}
                        disabled={
                          step.status === Constants.checklistStatus.pending
                        }
                      >
                        {index === checklistItems.length - 1
                          ? "Finish"
                          : "Complete"}
                      </Button>
                    </div>
                  </Box>
                </StepContent>
              )}
            </Step>
          ))}
      </Stepper>
    </Box>
  );
};
