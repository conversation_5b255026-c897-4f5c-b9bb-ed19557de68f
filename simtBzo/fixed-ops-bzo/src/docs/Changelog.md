<p>All Notable Enhancements.  Functionality Improvements Not Listed Here</p>

## <h1>Version 1.2.0 Release Date - 08/20/2024</h1>

<Grid >
<div>

### <span>Feature Enhancements</span>

- Replaced the top menu bar with a new side menu for improved navigation. 
- Disabled input fields in billing page, except Invoice Date, to prevent users from entering values from UI.
- Added pivoting functionality in User Roles table.
- Implemented sorting options to allow tenants to be sorted by Tenant Name and Signed On Date in ascending or descending order.

### <span>Bug Fixes</span>

- <b>Smoke Test Run:</b> The Run button is now enabled only when at least one row is selected to be tested.
- <b>Bulk Load API:</b>  Resolved an issue with variables passed in the bulk load API.
- <b>Dashboard Search Bar:</b>  Fixed case sensitivity issues in the search bar. Improved handling for scenarios where the search bar is reset or returns no results.

</div>

</Grid>

## <h1>Version 1.1.0 Release Date - 08/07/2024</h1>

<Grid >
<div>

### <span>Feature Enhancements</span>

- Implemented a new feature for store billing updates, allowing users to update billing details or cancel a specific subscription. Cancellations must be confirmed or denied by a different user.
- Implemented inserting of Parts Matrix based on opcodes.
- Added a new feature to run individual or group tests for each tenant with all of user roles. Upto 10 selected stores can be run for test at a time.
- New input added for 'Store Id' in store creating form.

### <span>Bug Fixes</span>

- Date format changed from 'MM-DD-YYYY' to 'MM/DD/YYYY' in all pages.
- Fixed an issue with regression and smoke tests where the HTML content of test results were not displaying properly in the UI and tabs were not selectable. The issue has been resolved by opening the results in a new browser tab, ensuring proper navigation.
- Delete button is removed for both tenants and stores from UI.

</div>

</Grid>

## <h1>Version 1.0.0 Release Date - 01/11/2024</h1>

<Grid>
<div>

**DealerShips**

- View existing DealerShips
- Create New DealerShips
- Delete Unwanted DealerShips
- Update DealerShips
- Configure to keycloak as Realm
- Get realm created status
- Enable/Disable DealerShips
- Provision to add tenant logo in tenant insert

**Store**

- View existing Stores
- Create New Stores
- Delete Stores
- Update Stores settings
- Intimation Email when new store is created
- Configure to keycloak

**Store Group**

- Get Store groups
- Create groups based on stores
- Delete Store groups
- Update Store groups
- Data Feed Details
- View Data Feed Based Entries
- Update Data Feed Based Entries

**Manufacturers List**

- View all Manufactures List

**Bulk Upload**

- Phase 1 - pre onboarding
- Phase 2 - post onboarding
- Daily Load
- Store Launched

**Opcode**

- Get Opcode Data
- Get Opcode Departments
- Update Opcode Data in each Department
- Opcode Top to Bottom Drag and Drop
- Multiple Opcode Drag and drop

**Pay Type**

- View Pay Types data
- Update Pay Types data

**Service Advisors**

- View Advisors data
- Update Advisors data

**Technicians**

- View Technicians Data
- Update Technicians Data

**Labor Grid**

- View Labor Grid data
- Insert Labor Grid data
- Update Labor Grid data
- Delete Labor Grid data
- Insert Labor Grid with Fixed Rate (File Upload)

**Parts Matrix**

- View Parts Matrix data
- Update Parts matrix data
- Insert Parts matrix data
- Delete Parts Matrix data
- File Upload

**Store Settings**

- View store setting details
- Update store setting details

## Other Updates:

- Daily Load:
  - Daily data load setup
  - Create Scheduler script

<body>

<h4>Please Note:</h4>

<span>These enhancements listed above are a small sample of what changed in this new build. Thank you, and we hope you enjoy this new version.</span>

</body>

</div>
</Grid>

## <h1>Version 0.0.1 Release Date - 06/18/2023</h1>

<Grid >
<div>

### <span>Features</span>

- Keycloak integration for login
- CRUD operations for tenant, stores, dms and store groups.
- Update data feed details for stores.
- Tenant onboarding automation
- Opcode - Drag and drop

</div>

</Grid>
