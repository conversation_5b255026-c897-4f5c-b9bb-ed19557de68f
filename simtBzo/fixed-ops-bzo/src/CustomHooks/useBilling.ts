import { useState } from "react";
import TenantQueries from "../service/DataFetchQueries/tenantQueries";
import { DatesObject, NotificationType, Subscription, Tenant } from "../types";
import TenantMutations from "../service/Mutations/tenantMutations";
import dayjs, { Dayjs } from "dayjs";

const useBilling = (record: Tenant) => {
  const { GetBillingSubsciptionList } = TenantQueries;
  const { UpdateBillingDetails } = TenantMutations;
  const [isLoading, setIsloading] = useState<boolean>(true);
  const [modal, setModal] = useState<string>("");
  const [openConfirmModal, setOpenConfirmModal] = useState<boolean>(false);
  const [statusChanged, setStatusChanged] = useState<boolean>(false);
  const [subscriptionDetails, setSubscriptionDetails] = useState<
    Subscription[]
  >([]);
  const [invoiceDate, setInvoiceDate] = useState<DatesObject>({});
  const [cancelledDate, setCancelledDate] = useState<DatesObject>({});
  const [updatedSubscriptions, setUpdatedSubscriptions] = useState<{
    [key: string]: Partial<Subscription>;
  }>({});
  const [openSnackbar, setOpenSnackbar] = useState<boolean>(false);
  const [statusMessage, setStatusMessage] = useState<string>("");
  const [billingEdited, setBillingEdited] = useState<boolean>(false);

  const [statusMessageType, setStatusMessageType] =
    useState<NotificationType>();

  const getSubscriptionList = () => {
    record.tenantId &&
      GetBillingSubsciptionList(record.tenantId)
        .then((res: Subscription[]) => {
          const invoiceDates = res.reduce<DatesObject>((acc, curr) => {
            acc[curr.subscriptionId] = curr.invoiceDate
              ? dayjs(curr.invoiceDate)
              : null;
            return acc;
          }, {});
          const cancelledDates = res.reduce<DatesObject>((acc, curr) => {
            acc[curr.subscriptionId] = curr.cancelledDate
              ? dayjs(curr.cancelledDate)
              : null;
            return acc;
          }, {});
          setInvoiceDate(invoiceDates);
          setCancelledDate(cancelledDates);
          setSubscriptionDetails(res);
          setIsloading(false);
        })
        .catch((err) => {
          setIsloading(false);
        });
  };

  const disableDates = (current: Dayjs) => {
    // Disable dates after the 28th of each month
    return current && current.date() > 15;
  };

  const handleSave = (action: string) => {
    const sample = Object.entries(updatedSubscriptions).map(([id, details]) => {
      const otherDetails: Subscription | undefined = subscriptionDetails.find(
        (item: Subscription) => item.subscriptionId === id
      );
      return {
        tenant_id: otherDetails?.tenantId,
        subscription_id: otherDetails?.subscriptionId,
        invoice_date: details.invoiceDate
          ? details.invoiceDate
          : otherDetails?.invoiceDate,
        subscription_status:
          action === "cancel"
            ? "Review"
            : action === "cancelApprove"
            ? "Cancelled"
            : action === "cancelReview" || action === "cancelDeny"
            ? "Active"
            : otherDetails?.subscriptionStatus,
        dms_one_time_fee: details.dmsOneTimeFee
          ? details.dmsOneTimeFee
          : otherDetails?.dmsOneTimeFee,
        dms_monthly_recurring_fee: details.dmsMonthlyRecurringFee
          ? details.dmsMonthlyRecurringFee
          : otherDetails?.dmsMonthlyRecurringFee,
        fopc_monthly_recurring_fee: details.fopcMonthlyRecurringFee
          ? details.fopcMonthlyRecurringFee
          : otherDetails?.fopcMonthlyRecurringFee,
        cancelled_date: details.cancelledDate
          ? details.cancelledDate
          : action === "cancelReview" || action === "cancelDeny"
          ? null
          : otherDetails?.cancelledDate,
        cancelled_by:
          action === "update" || action === "cancelApprove"
            ? otherDetails?.cancelledBy
            : action === "cancelReview" || action === "cancelDeny"
            ? null
            : localStorage.getItem("user"),
        cancelled_by_date:
          action === "cancel"
            ? dayjs()
            : action === "cancelApprove"
            ? otherDetails?.cancelledByDate
            : null,
        cancellation_confirmed_by:
          action === "cancelApprove" ? localStorage.getItem("user") : null,
        cancellation_confirmed_by_date:
          action === "cancelApprove" ? dayjs() : null,
      };
    });
    UpdateBillingDetails(record.tenantId, sample)
      .then((res) => {
        setBillingEdited(false);
        setOpenConfirmModal(false);
        setStatusChanged(!statusChanged);
        setUpdatedSubscriptions({});
        action === "update" && setOpenSnackbar(true);
        if (res.status === 1) {
          setStatusMessage(
            action === "update"
              ? "Billing details updated successfully"
              : action === "cancel"
              ? "Subscription set for review to cancel"
              : action === "cancelApprove"
              ? "Subscription cancelled successfully"
              : action === "cancelDeny"
              ? "Cancellation of this subscription is denied"
              : // : action === "cancelReview"
                // ? "Cancellation of this subscription is denied"
                ""
          );
          setStatusMessageType("success");
        } else {
          setStatusMessageType("error");
          setStatusMessage("Something went wrong");
        }
      })
      .catch((err) => {
        setStatusMessage("Error in billing details updation");
        setOpenSnackbar(true);
        setStatusMessageType("error");

        console.log("err", err);
      });
  };

  const handleInputChange = (
    value: number | string | null | string[],
    input: string,
    id: string | number
  ) => {
    value === "cancelReview" ||
    value === "cancelApprove" ||
    value === "cancelDeny"
      ? setUpdatedSubscriptions((prevState) => ({
          ...prevState,
          [id]: {
            ...prevState[id],
          },
        }))
      : setUpdatedSubscriptions((prevState) => ({
          ...prevState,
          [id]: {
            ...prevState[id],
            [input]: value,
          },
        }));
  };

  return {
    getSubscriptionList,
    isLoading,
    subscriptionDetails,
    disableDates,
    handleSave,
    handleInputChange,
    setInvoiceDate,
    cancelledDate,
    setCancelledDate,
    invoiceDate,
    statusChanged,
    openConfirmModal,
    setOpenConfirmModal,
    modal,
    setModal,
    setOpenSnackbar,
    openSnackbar,
    statusMessage,
    statusMessageType,
    billingEdited,
    setBillingEdited,
  };
};

export default useBilling;
