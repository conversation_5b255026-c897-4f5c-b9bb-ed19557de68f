import { useState } from "react";
import { InsertOrUpdateTenant } from "../service/mutations";
import { Constants } from "../utils/constants";
import dayjs from "dayjs";
import { useTranslate } from "react-admin";
import { PageRoutes } from "../utils/pageRoutes";
import { useNavigate } from "react-router-dom";

const useTenantDetails: any = (id?: any) => {
  const translate = useTranslate();
  const [agreementDate, setAgreementDate] = useState("");
  const [updateDate, setUpdateDate] = useState<Date>();
  const [base64Image, setBase64Image] = useState<any>();
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [statusMessage, setStatusMessage] = useState<any>("");
  const [agreementDateMsg, setAgreementDateMsg] = useState<any>("");
  const navigate = useNavigate();

  function formatLogo(value: any) {
    if (!value || typeof value === Constants.type.string) {
      // Value is null or the url string from the backend, wrap it in an object so the form input can handle it
      return { url: value };
    } else {
      // Else a new image is selected which results in a value object already having a preview link under the url key
      return value;
    }
  }

  const onTenantFormSubmit = (data: any) => {
    if (agreementDate) {
      const fixedDomain = ".fixedopspc.com";
      const tenantImg = base64Image;
      const fullDomainAddress = id
        ? data.sub_domain
        : data.sub_domain + fixedDomain;
      const pAction = id ? Constants.actions.update : Constants.actions.insert;
      data.tenant_name = data?.tenant_name?.replace(/\s+/g, " ").trim();
      const tenantId = id
        ? data.tenant_id
        : `${data.tenant_name
            .toLowerCase()
            .replace(Constants.patterns.generateId, "")}`;
      const tenantInput = {
        pAction,
        tenantId,
        // tenantName: data.tenant_name,
        // tenantDesc: data.tenant_name,
        // dms: data.dms,
        // subDomain: fullDomainAddress,
        tenantImg: tenantImg,
        agreementDate: dayjs(agreementDate).format("MM/DD/YYYY"),
        displayName: data.display_name ? data.display_name : "",
      };

      InsertOrUpdateTenant(tenantInput).then((result: any) => {
        const isSuccess = result.string === Constants.success;
        if (isSuccess) {
          // !id && InsertWorkPackage(tenantId).then((res: any)=>{
          //   res.status === 1 &&
          //   InsertWorkPackageHierarchies(tenantId);
          // })
          setStatusMessage(
            translate(
              id
                ? "SUCCESS_MESSAGES.UPDATE_MESSAGE"
                : "SUCCESS_MESSAGES.CREATE_MESSAGE",
              {
                entityName: "Tenant",
              }
            )
          );
          // ToDo: Snack Bar
          setOpenSnackbar(true);
          setTimeout(() => {
            setOpenSnackbar(false);
            navigate(PageRoutes.statelessServiceBzoTenants);
          }, 1000);
        } else {
          // ToDo: Need show Error Message
        }
      });
    }
  };

  return {
    formatLogo,
    agreementDate,
    setAgreementDate,
    updateDate,
    setUpdateDate,
    base64Image,
    setBase64Image,
    openSnackbar,
    setOpenSnackbar,
    statusMessage,
    setStatusMessage,
    onTenantFormSubmit,
    agreementDateMsg,
    setAgreementDateMsg,
  };
};

export default useTenantDetails;
