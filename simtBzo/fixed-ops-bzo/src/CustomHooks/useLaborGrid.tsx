import { useEffect, useState } from "react";
import DataFetchQueries from "../service/dataFetchQueries";
import DataMutationQueries from "../service/mutations";
import { Constants } from "../utils/constants";
import dayjs from "dayjs";
import LaborGridQueries from "../service/LaborGrid/LaborGridQueries";
import { GridTypeMaster, SelectInputChoices } from "../types";
import LaborGridMutations from "../service/LaborGrid/LaborGridMutations";
import { useTranslate } from "react-admin";

const useLaborGrid = () => {
  const [tempData, setTempData] = useState<any>([]);
  const [rowData, setRowData] = useState<any>(null);
  const [addGrid, setAddGrid] = useState<string>("");
  const [gridTypeList, setGridTypeList] = useState<any>([]);
  const [saveClickedFlag, setSaveClickedFlag] = useState<boolean>(false);
  const [openSnackbar, setOpenSnackbar] = useState<boolean>(false);
  const [statusMessage, setStatusMessage] = useState<string>("");
  const [snackbarMessages, setSnackbarMessages] = useState<any>();
  const [statusMessageType, setStatusMessageType] = useState<any>("");
  const [enableSave, setEnableSave] = useState<boolean>(false);
  const [incompleteError, setIncompleteError] = useState<boolean>(false);
  const [invalidError, setInvalidError] = useState<boolean>(false);
  const [invalidLength, setInvalidLength] = useState<boolean>(false);
  const [installDate, setInstallDate] = useState<any>();
  const [gridType, setGridType] = useState<any>();
  const [listLoading, setListLoading] = useState<boolean>(true);
  const [installDateValue, setInstallDateValue] = useState<any>();
  const [updateDate, setUpdateDate] = useState(false);
  const [hasDefaultType, setHasDefaultType] = useState<boolean>(false);
  const [reassignGridType, setReassignGridType] = useState<boolean>(false);
  const [defaultGridType, setDefaultGridType] = useState<string>("");
  const [openDelete, setOpenDelete] = useState<boolean>(false);
  const [newDefaultGridType, setNewDefaultGridType] = useState<string | null>(
    null
  );
  const [noOfRows, setNoOfRows] = useState<number>(16);
  const [gridTypeChoice, setGridTypeChoice] = useState<any[]>([]);
  const [btnEnable, setBtnEnable] = useState<boolean>(false);
  const [updatedDefaultGridType, setUpdatedDefaultGridType] =
    useState<string>("");
  const translate = useTranslate();
  const { GetLaborGridList } = DataFetchQueries;
  const { GetGridTypeMaster } = LaborGridQueries;
  const { InsertUpdateGridType } = LaborGridMutations;
  const { InsertUpdateLaborGrid, InsertGriddataDtl, FileUpload } =
    DataMutationQueries;
  const [isSuccessful, setIsSuccessful] = useState<boolean>(true);
  const [openGridTypeModal, setOpenGridTypeModal] = useState<boolean>(false);
  const [gridTypeChanged, setGridTypeChanged] = useState<boolean>(false);
  const [submitLoader, setSubmitLoader] = useState<boolean>(false);

  const onRowEditingStopped = (params: any) => {
    let newArray = [...tempData];
    newArray[0].col0 = "0";
    tempData.map((obj: any, index: any) => {
      if (params.data.hours === obj.hours) newArray[index] = params.data;
    });
    setTempData(newArray);
  };

  const getLaborGridList = (getInput: any) => {
    setEnableSave(false);
    GetLaborGridList(getInput).then((res: any) => {
      const sortedData = res.sort((a: any, b: any) => {
        if (a.gridOrder === 1 && b.gridOrder !== 1) return -1;
        if (a.gridOrder !== 1 && b.gridOrder === 1) return 1;
        return 0;
      });
      if (getInput.callType === "Grid_Type") {
        setGridTypeList(sortedData);
      } else {
        setNoOfRows(sortedData.length);
        setRowData(sortedData);
      }
      setListLoading(false);
    });
  };

  const getSnackbarForGrid = async (results: any) => {
    let res = null;
    results.forEach((result: any) => {
      if (result.code === 1) {
        setIsSuccessful(true);
        setStatusMessageType(Constants.statusType.success);
        setStatusMessage(
          result.message
            ? result.message
            : result.msg
            ? result.msg
            : "Operation completed"
        );
        setOpenSnackbar(true);
        res = 1;
      } else {
        setIsSuccessful(false);
        setStatusMessageType(Constants.statusType.error);
        setStatusMessage(
          result.message
            ? result.message
            : result.msg
            ? result.msg
            : "Operation completed"
        );
        setOpenSnackbar(true);
        res = 0;
      }
    });
    return res;
  };
  const handleSubmitLaborGrid = async (values?: any, pCall?: string) => {
    console.log("pCall====",pCall)
    let isSuccess = false;
    if (pCall === "fixed_rate") {
      const pValData: any = {
        inCreatedDate: dayjs(installDateValue).format("MM/DD/YYYY"),
        inGridType:
          values.gridFor === "opcode" ? values.opcode : values.gridType,
        inStoreId: values.storeId,
        inDoorRate:
          //   Math.floor(parseFloat(values.fixedRate) * 100) / 100
          // ).toFixed(2),
          (Math.round(parseFloat(values.fixedRate) * 100) / 100).toFixed(2),
        realm: values.realmName,
        inGridFor: values.gridFor,
        inTenantId: values.tenantId,
      };

      InsertGriddataDtl(pValData)
        .then(async (res: any) => {
          getGridTypesList({
            realmName: pValData.realm,
            storeId: pValData.inStoreId,
            tenantId: pValData.inTenantId,
          });
          if (res && Array.isArray(res)) {
            setSnackbarMessages(res);
            getLaborGridList({
              realm: values.realmName,
              storeId: values.storeId,
              tenantId: values.tenantId,
              callType: "Grid_Type",
            });
            setAddGrid("");
          } else {
            // Handle unexpected response structure
            setIsSuccessful(false);
            setStatusMessageType(Constants.statusType.error);
            setStatusMessage("Unexpected response format");
            setOpenSnackbar(true);
          }
        })
        .finally(() => {
          setSubmitLoader(false);
        });
    } else if (pCall === "upload") {
      const reader: any = new FileReader();
      if (values.csvFile.rawFile) {
        reader.readAsDataURL(values.csvFile.rawFile);
        reader.onloadend = () => {
          const base64Content = reader.result.split(",")[1]; // remove prefix value from base64 string
          const pValData: any = {
            base64Data: base64Content,
            inFileName: values.csvFile.title,
            inGridType:
              values.gridFor === "opcode" ? values.opcode : values.gridType,
            inInstallationDate: dayjs(installDateValue).format("MM/DD/YYYY"),
            inStoreId: values.storeId,
            inTenantId: values.tenantId,
            realm: values.realmName,
            inGridOrFleet: values.gridFor,
          };
          FileUpload(pValData)
            .then(async (res: any) => {
              getGridTypesList({
                realmName: pValData.realm,
                storeId: pValData.inStoreId,
                tenantId: pValData.inTenantId,
              });
              if (res && Array.isArray(res)) {
                setSnackbarMessages(res);
                getLaborGridList({
                  realm: values.realmName,
                  storeId: values.storeId,
                  callType: "Grid_Type",
                  tenantId: values.tenantId,
                });
                setAddGrid("");
              } else {
                // Handle unexpected response structure
                setIsSuccessful(false);
                setStatusMessageType(Constants.statusType.error);
                setStatusMessage("Unexpected response format");
                setOpenSnackbar(true);
              }
            })
            .finally(() => {
              setSubmitLoader(false);
            });
        };
      }
    } else {
      setIncompleteError(false);
      setInvalidLength(false);
      setInvalidError(false);
      setSaveClickedFlag(true);
      const tempInput = {
        realm: values.realmName,
        storeId: values.storeId,
        tenantId: values.tenantId,
        pGridFor: values.gridFor,
        pOldGridType:
          pCall === "delete" || pCall === "update"
            ? values.gridType
            : values.gridFor === "opcode"
            ? values.opcode
            : values.gridType,
        // if edit for grid type is needed, refer the code below pNewGridType
        pNewGridType:
          pCall === "delete" || pCall === "update"
            ? values.gridType
            : values.gridFor === "opcode"
            ? values.opcode
            : values.gridType,
        // pCall === "update"
        //   ? values.newGridType.trim()
        //   : values.gridName.trim(),
        storeInstallDate:
          pCall === "delete"
            ? values.storeInstallDate
            : pCall === "update"
            ? dayjs(values.storeInstallDate).format("MM/DD/YYYY")
            : dayjs(installDateValue).format("MM/DD/YYYY"),
        gridData: pCall === "delete" ? rowData : tempData,
        createdDate: values.createdDate
          ? values.createdDate
          : dayjs(installDateValue).format("MM/DD/YYYY"),
        pLaborMissType: pCall === "insert" ? addGrid : "grid",
        pCall: pCall,
        pConfirmFlag: pCall === "delete" && newDefaultGridType ? "Y" : "N",
        pIsDefault:
          pCall === "delete" && newDefaultGridType ? newDefaultGridType : "",
      };

      let incomplete = false;
      let inValid = false;
      let gridValueLength = false;
      incomplete = tempData.some((obj: any) =>
        Object.values(obj).some((value: any) => {
          return pCall !== "delete" && value === "";
        })
      );
      inValid = tempData.some((obj: any) =>
        Object.values(obj).some(
          (value: any) =>
            value !== "" && !Constants.patterns.numericalRegex.test(value)
        )
      );

      const regex = /^\d{1,5}(\.\d{1,2})?$/;
      gridValueLength = tempData.some((obj: any) =>
        Object.values(obj).some(
          (value: any) => value !== "" && !regex.test(value)
        )
      );

      if (!incomplete && !inValid && !gridValueLength) {
        await InsertUpdateLaborGrid(tempInput)
          .then(async (res: any) => {
            getGridTypesList({
              realmName: tempInput.realm,
              storeId: tempInput.storeId,
              tenantId: tempInput.tenantId,
            });
            if (pCall === "delete") {
              if (res.rStatus === 2) {
                setReassignGridType(true);
              } else if (res.rStatus !== 2) {
                if (res.rStatus === 1) {
                  setIsSuccessful(true);
                  setStatusMessageType(Constants.statusType.success);
                  setStatusMessage(res.msg);
                  setOpenSnackbar(true);
                } else {
                  setIsSuccessful(false);
                  setStatusMessageType(Constants.statusType.error);
                  setStatusMessage(res.msg);
                  setOpenSnackbar(true);
                }
                setOpenDelete(false);
                setIsSuccessful(true);
                setInvalidLength(false);
                setIncompleteError(false);
                setInvalidError(false);
                getLaborGridList({
                  realm: values.realmName,
                  storeId: values.storeId,
                  callType: "Grid_Type",
                  tenantId: values.tenantId,
                });
                setAddGrid("");
                setReassignGridType(false);
                isSuccess = true;
              }
            } else if (pCall === "update") {
              if (res.rStatus === 1) {
                setIsSuccessful(true);
                setStatusMessageType(Constants.statusType.success);
                setEnableSave(false);
                getLaborGridList({
                  realm: values.realmName,
                  storeId: values.storeId,
                  callType: "Grid_Type",
                  tenantId: values.tenantId,
                });
              } else {
                setIsSuccessful(false);
                setStatusMessageType(Constants.statusType.error);
              }
              setStatusMessage(`${res.msg}`);
              setOpenSnackbar(true);
            } else {
              if (res && Array.isArray(res)) {
                setSnackbarMessages(res);
                setIsSuccessful(true);
                setInvalidLength(false);
                setIncompleteError(false);
                setInvalidError(false);
                getLaborGridList({
                  realm: values.realmName,
                  storeId: values.storeId,
                  callType: "Grid_Type",
                  tenantId: values.tenantId,
                });
                setAddGrid("");
              } else {
                // Handle unexpected response structure
                setIsSuccessful(false);
                setStatusMessageType(Constants.statusType.error);
                setStatusMessage("Unexpected response format");
                setOpenSnackbar(true);
              }
            }
          })
          .catch((error: any) => {
            console.log(error);
          })
          .finally(() => {
            setSubmitLoader(false);
          });
      } else {
        setSubmitLoader(false);
        gridValueLength && setInvalidLength(true);
        incomplete && setIncompleteError(true);
        inValid && setInvalidError(true);
      }
    }
    return isSuccess;
  };
  const getGridTypesList = (record: any) => {
    GetGridTypeMaster(record.storeId, record.tenantId).then((res) => {
      const gridChoice: SelectInputChoices[] = res.map(
        (item: GridTypeMaster) => ({
          id: item.value,
          name: item.value,
          isDefaultGridType: item.isDefaultGridType,
          count: item.gridCount,
        })
      );
      setGridTypeChoice(gridChoice);
      setBtnEnable(true);
      if (res.length !== 0) {
        const defaultGridType = res.find(
          (item: GridTypeMaster) => item.isDefaultGridType
        );
        setHasDefaultType(defaultGridType?.isDefaultGridType);
        setDefaultGridType(defaultGridType?.value);
      }
    });
  };
  const saveDefaultGridType = async (record: any) => {
    const input = {
      inNewGridName: updatedDefaultGridType,
      inOldGridName: updatedDefaultGridType,
      inStoreId: record.storeId,
      inTenantId: record.tenantId,
      Realm: record.realmName,
      inIsDefaultGridName: true,
      inActivity: "Update",
    };
    try {
      const result: any = await InsertUpdateGridType(input);
      if (result[0].status === 1) {
        const statusMessage = translate(result[0]?.msg);
        setStatusMessage(statusMessage);
        setOpenSnackbar(true);
        getGridTypesList(record);
        setStatusMessageType(Constants.statusType.success);
        setOpenGridTypeModal(false);
      } else {
        const statusMessage = translate(result[0]?.msg);
        setStatusMessageType(Constants.statusType.error);
        setStatusMessage(statusMessage);
        setOpenSnackbar(true);
      }
    } catch (error) {
      const statusMessage = translate("ERROR_MESSAGES.SOMETHING_WENT_WRONG");
      setStatusMessageType(Constants.statusType.error);
      setStatusMessage(statusMessage);
      setOpenSnackbar(true);
      setOpenGridTypeModal(false);
      console.log(error);
    } finally {
      setGridTypeChanged(false);
      setUpdatedDefaultGridType("");
    }
  };

  return {
    saveDefaultGridType,
    setUpdatedDefaultGridType,
    updatedDefaultGridType,
    openGridTypeModal,
    setOpenGridTypeModal,
    setInstallDate,
    setGridType,
    setEnableSave,
    setSaveClickedFlag,
    getLaborGridList,
    onRowEditingStopped,
    setTempData,
    handleSubmitLaborGrid,
    setAddGrid,
    setRowData,
    setOpenSnackbar,
    setIncompleteError,
    setInvalidLength,
    setInvalidError,
    isSuccessful,
    invalidError,
    invalidLength,
    incompleteError,
    tempData,
    rowData,
    addGrid,
    gridTypeList,
    saveClickedFlag,
    openSnackbar,
    statusMessage,
    enableSave,
    installDate,
    listLoading,
    gridType,
    installDateValue,
    setInstallDateValue,
    updateDate,
    setUpdateDate,
    setStatusMessage,
    statusMessageType,
    setStatusMessageType,
    getGridTypesList,
    defaultGridType,
    hasDefaultType,
    gridTypeChoice,
    btnEnable,
    setGridTypeChanged,
    gridTypeChanged,
    newDefaultGridType,
    setNewDefaultGridType,
    reassignGridType,
    setReassignGridType,
    openDelete,
    setOpenDelete,
    noOfRows,
    setNoOfRows,
    snackbarMessages,
    setSnackbarMessages,
    submitLoader,
    setSubmitLoader,
  };
};

export default useLaborGrid;
