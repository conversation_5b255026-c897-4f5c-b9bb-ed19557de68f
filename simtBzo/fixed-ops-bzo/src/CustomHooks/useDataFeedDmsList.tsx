import { useRef, useState } from "react";
import { Constants } from "../utils/constants";
import { InsertUpdateMultipleDmsDetails } from "../service/mutations";
import StoreQueries from "../service/DataFetchQueries/storeQueries";
import dayjs from "dayjs";
import { traceSpan } from "../utils/OTTTracing";

const useDataFeedDmsList = (props: any) => {
  const {
    record,
    getStoreQuery,
    getDataFeed,
    setOpenSnackbar,
    setStatusMessage,
    setStatusMessageType,
    getDataFeedDmsList,
  } = props;
  console.log("rrrtttttt", record);
  const [originalRowData, setOriginalRowData] = useState<any>(null);
  const [saveNewValue, setsaveNewValue] = useState(false);
  const [openDeleteModal, setOpenDeleteModal] = useState(false);
  const [deleteNode, setDeleteNode] = useState<any>();
  const gridApiRef: any = useRef();
  const [rowErrors, setRowErrors] = useState<{
    [rowId: string]: { [field: string]: string };
  }>({});

  let gridRef = useRef<any>();
  const gridColumnApiRef: any = useRef();
  const cancelClickedRef = useRef(false);

  const onRowEditingStarted = (params: any) => {
    traceSpan(`click_datafeededit_icon`, {
      event: `click_datafeededit_icon`,
      pageUrl: window.location.pathname,
      timestamp: new Date().toISOString(),
      userId: localStorage.getItem("user") || "",
    });
    params.api.refreshCells({
      columns: ["action"],
      rowNodes: [params.node],
      force: true,
    });
  };

  const onRowEditingStopped = (params: any) => {
    console.log("ppppppppppppppppppppppppppppp", params);

    // traceSpan(`click_datafeedsave_icon`, {
    //   event: `click_datafeedsave_icon`,
    //   pageUrl: window.location.pathname,
    //   timestamp: new Date().toISOString(),
    //   userId:localStorage.getItem('user') || ''
    // });
    const { api, node } = params;

    const editedData = node.data;
    if (cancelClickedRef.current) {
      cancelClickedRef.current = false;
      node.setData(originalRowData); // reset
      return; // skip validation
    }
    console.log("eeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee", editedData);
    const originalData: any = originalRowData;
    console.log("originalDataaaaaaaaaaaaaaaaaaaaaaaaaa", originalData);

    if (originalData) {
      if (!saveNewValue) {
        node.setData(originalData);
      } else {
        const updatedData = { ...originalData, ...editedData };
        console.log("row editing updatedData", params);
        node.setData(updatedData);
        const input = {
          ...record,
          id: updatedData.id,
          dms: updatedData.dms,
          enterpriseCode: updatedData.enterpriseCode,
          serverName: updatedData.serverName,
          companyNumber: updatedData.companyNumber,
          dealerId: updatedData.dealerId,
          effectiveDate: updatedData.effectiveDate
            ? dayjs(updatedData.effectiveDate).format("MM/DD/YYYY")
            : null,
          effectiveDate: updatedData.effectiveDate
            ? dayjs(updatedData.effectiveDate).format("MM/DD/YYYY")
            : null,
          action: "update",
          ftlsSubscriptionId: updatedData.ftlsSubscriptionId,
          ftlsDepartment: updatedData.ftlsDepartment,
          displayName: record.displayName,
        };
        if (
          updatedData.dms == "Tekion" ||
          updatedData.dms == "CDK Global" ||
          updatedData.dms == "Automate" ||
          updatedData.dms == "Reynolds"
        ) {
          input.ftlsSubscriptionId = "";
          input.ftlsDepartment = "";
          input.companyNumber = "";
          input.enterpriseCode = "";
        }
        if (updatedData.dms == "Fortellis") {
          input.companyNumber = "";
          input.enterpriseCode = "";
          input.dealerId = "";
        }
        if (updatedData.dms == "Dealertrack") {
          input.ftlsSubscriptionId = "";
          input.ftlsDepartment = "";
          input.dealerId = "";
        }

        if (
          input.dms == "Tekion" ||
          input.dms == "CDK Global" ||
          input.dms == "Automate" ||
          input.dms == "Reynolds"
        ) {
          if (!input.dealerId) {
            // alert("Please enter dealer id");
            console.log("index", params.node.rowIndex);
            params.api.startEditingCell({
              rowIndex: params.node.rowIndex,
              colKey:
                gridColumnApiRef.current.getDisplayedCenterColumns()[0].colId,
            });

            return;
          }
          if (originalData.effectiveDate != null && !input.effectiveDate) {
            //alert("Please enter effective date");
            params.api.startEditingCell({
              rowIndex: params.node.rowIndex,
              colKey:
                gridColumnApiRef.current.getDisplayedCenterColumns()[0].colId,
            });

            return;
          }
        }

        if (input.dms == "Fortellis") {
          if (!input.ftlsSubscriptionId) {
            //  alert("Please enter subscription id");
            params.api.startEditingCell({
              rowIndex: params.node.rowIndex,
              colKey:
                gridColumnApiRef.current.getDisplayedCenterColumns()[0].colId,
            });

            return;
          }
          if (!input.ftlsDepartment) {
            //  alert("Please enter department");
            params.api.startEditingCell({
              rowIndex: params.node.rowIndex,
              colKey:
                gridColumnApiRef.current.getDisplayedCenterColumns()[0].colId,
            });

            return;
          }
          if (originalData.effectiveDate != null && !input.effectiveDate) {
            //  alert("Please enter effective date");
            params.api.startEditingCell({
              rowIndex: params.node.rowIndex,
              colKey:
                gridColumnApiRef.current.getDisplayedCenterColumns()[0].colId,
            });

            return;
          }
        }
        if (input.dms == "Dealertrack") {
          if (!input.companyNumber) {
            //  alert("Please enter company number");
            params.api.startEditingCell({
              rowIndex: params.node.rowIndex,
              colKey:
                gridColumnApiRef.current.getDisplayedCenterColumns()[0].colId,
            });

            return;
          }
          if (!input.enterpriseCode) {
            //  alert("Please enter enterprise code");
            params.api.startEditingCell({
              rowIndex: params.node.rowIndex,
              colKey:
                gridColumnApiRef.current.getDisplayedCenterColumns()[0].colId,
            });

            return;
          }
          if (originalData.effectiveDate != null && !input.effectiveDate) {
            //  alert("Please enter effective date");
            params.api.startEditingCell({
              rowIndex: params.node.rowIndex,
              colKey:
                gridColumnApiRef.current.getDisplayedCenterColumns()[0].colId,
            });

            return;
          }
        }

        console.log("originalData", originalData);
        console.log("editedData", editedData);

        updatedData !== originalData &&
          console.log("iiiiiiiitttttttttttttttttttt", input);
        InsertUpdateMultipleDmsDetails(input)
          .then((response) => {
            if (response.statusCode === 1) {
              getStoreQuery();
              getDataFeed();
              setStatusMessage("Data feed details update successfully");
              setStatusMessageType(Constants.statusType.success);
            } else {
              getStoreQuery();
              getDataFeed();
              setStatusMessage(response.statusMsg);
              setStatusMessageType(Constants.statusType.error);
            }
          })
          .finally(() => {
            getDataFeedDmsList();
            setOpenSnackbar(true);
          });
      }
      setsaveNewValue(false);
    }
  };

  const onCellClicked = (params: any) => {
    const target = params.event.target;
    let action = target.closest("[data-action]")?.dataset.action;
    // Handle click event for action cells
    if (params.column.colId === "action" && action) {
      if (action === Constants.actions.edit) {
        //     traceSpan(`click_edit_icon`, {
        //   event: `click_edit_icon`,
        //   pageUrl: window.location.pathname,
        //   timestamp: new Date().toISOString(),
        //   userId:localStorage.getItem('user') || ''
        // });
        const node = params.node;
        const data = node.data;
        console.log("dddddddddddddddddaaaaaaaaaaaaaaaaaaaaa", data);
        params.api.startEditingCell({
          rowIndex: params.node.rowIndex,
          // gets the first columnKey
          colKey:
            gridColumnApiRef?.current?.getDisplayedCenterColumns()[0].colId,
        });
        setOriginalRowData({ ...data });
      } else if (action === Constants.actions.delete) {
        traceSpan(`click_datafeeddelete_icon`, {
          event: `click_datafeeddelete_icon`,
          pageUrl: window.location.pathname,
          timestamp: new Date().toISOString(),
          userId: localStorage.getItem("user") || "",
        });
        const node = params.node;
        const data = node.data;
        setDeleteNode(data);
        setOpenDeleteModal(true);
      }
      if (action === Constants.actions.update) {
        traceSpan(`click_datafeedsave_icon`, {
          event: `click_datafeedsave_icon`,
          pageUrl: window.location.pathname,
          timestamp: new Date().toISOString(),
          userId: localStorage.getItem("user") || "",
        });
        setsaveNewValue(true);
        cancelClickedRef.current = false;
        params.api.stopEditing(false);
      }
      if (action === Constants.actions.cancel) {
        traceSpan(`click_editdatafeedcancel_icon`, {
          event: `click_editdatafeedcancel_icon`,
          pageUrl: window.location.pathname,
          timestamp: new Date().toISOString(),
          userId: localStorage.getItem("user") || "",
        });
        cancelClickedRef.current = true;
        params.api.stopEditing(true);
      }
    }
  };

  const onGridReady = (params: any) => {
    params?.api?.showLoadingOverlay();
    gridApiRef.current = params.api;
    gridRef.current = params.api;
    gridColumnApiRef.current = params.columnApi;
    getDataFeedDmsList();
  };

  const handleDialogClose = () => {
    traceSpan(`click_datafeeddeletecancel_button`, {
      event: `click_datafeeddeletecancel_button`,
      pageUrl: window.location.pathname,
      timestamp: new Date().toISOString(),
      userId: localStorage.getItem("user") || "",
    });
    setOpenDeleteModal(false);
  };

  const handleConfirm = () => {
    traceSpan(`click_datafeeddeleteconfirm_button`, {
      event: `click_datafeeddeleteconfirm_button`,
      pageUrl: window.location.pathname,
      timestamp: new Date().toISOString(),
      userId: localStorage.getItem("user") || "",
    });
    const input = {
      ...record,
      ...deleteNode,
      action: "delete",
    };
    InsertUpdateMultipleDmsDetails(input)
      .then((response) => {
        if (response.statusCode === 1) {
          getStoreQuery();
          getDataFeed();
          setStatusMessage("Data feed details deleted successfully");
          setStatusMessageType(Constants.statusType.success);
        } else {
          getStoreQuery();
          getDataFeed();
          setStatusMessage(response.statusMsg);
          setStatusMessageType(Constants.statusType.error);
        }
      })
      .finally(() => {
        getDataFeedDmsList();
        setOpenSnackbar(true);
      });
    setOpenDeleteModal(false);
  };

  return {
    onRowEditingStopped,
    onRowEditingStarted,
    onCellClicked,
    onGridReady,
    gridApiRef,
    openDeleteModal,
    setOpenDeleteModal,
    handleDialogClose,
    handleConfirm,
  };
};

export default useDataFeedDmsList;
