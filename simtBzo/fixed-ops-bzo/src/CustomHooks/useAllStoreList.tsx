import { useState } from "react";
import StoreQueries from "../service/DataFetchQueries/storeQueries";

const useAllStoreList = (record?: any) => {
  const { GetAllOnboardingStores, GetAllLaunchedStores } = StoreQueries;
  const [allStoresList, setAllStoresList] = useState<any>([]);

  const [selectedStore, setSelectedStore] = useState<string>("onboarding");
  const [agGridApi, setAgGridApi] = useState<any>();

  const fetchAllStores = async (status: string) => {
    try {
      const result =
        status === "onboarding"
          ? await GetAllOnboardingStores()
          : await GetAllLaunchedStores();
      if (result) {
        setAllStoresList(result);
      }
    } catch (err) {
      console.error("Error fetching store data:", err);
    }
  };

  const onGridReady = (params: any) => {
    params?.api?.showLoadingOverlay();
    setAgGridApi(params.api);
    fetchAllStores("onboarding");
  };

  const handleRadioChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    agGridApi?.showLoadingOverlay();
    setSelectedStore(event.target.value);
    fetchAllStores(event.target.value);
  };
  return {
    selectedStore,
    setSelectedStore,
    handleRadioChange,
    onGridReady,
    allStoresList,
  };
};
export default useAllStoreList;
