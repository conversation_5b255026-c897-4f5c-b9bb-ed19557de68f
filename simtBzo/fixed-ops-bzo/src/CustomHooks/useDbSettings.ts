import { useEffect, useState } from "react";
import SettingsQueries from "../service/Mutations/Settings";

const useDbSettingsHandlers = (api?: any, node?: any, rowIndex?: number) => {
  const [isRowEditing, setIsRowEditing] = useState(false);
  const [allDataList, setAllDataList] = useState<any[]>([]);

  const { UpsertDbSettings, GetDbSettings } = SettingsQueries;

  const getDbSettingsList = () => {
    GetDbSettings().then((res: any) => {
      setAllDataList(res);
    });
  };

  useEffect(() => {
    if (!api || rowIndex === undefined) return;

    const checkIsEditing = () => {
      const editingCells = api.getEditingCells();
      const isEditing = editingCells.some((cell: any) => cell.rowIndex === rowIndex);
      setIsRowEditing(isEditing);
    };

    api.addEventListener("cellEditingStarted", checkIsEditing);
    api.addEventListener("cellEditingStopped", checkIsEditing);
    checkIsEditing();

    return () => {
      api.removeEventListener("cellEditingStarted", checkIsEditing);
      api.removeEventListener("cellEditingStopped", checkIsEditing);
    };
  }, [api, rowIndex]);

  const handleEdit = () => {
    api?.startEditingCell({
      rowIndex,
      colKey: "serverNo",
    });
  };

  const handleSave = () => {
    api?.stopEditing(false);
    const updatedData = node?.data;
    updatedData.dbPort = parseInt(updatedData.dbPort);
    console.log("Saved data:", updatedData);
    UpsertDbSettings(updatedData).then((res: any) => {
      console.log("vvvvvvvvvv----", res);
    });
  };

  const handleCancel = () => {
    api?.stopEditing(true);
  };

  const handleDelete = () => {
    console.log("Delete clicked", node?.data);
    // Add delete logic here
  };

  return {
    isRowEditing,
    handleEdit,
    handleSave,
    handleCancel,
    handleDelete,
    getDbSettingsList,
    allDataList,
  };
};

export default useDbSettingsHandlers;
