import { useState } from "react";
import TenantQueries from "../service/DataFetchQueries/tenantQueries";

const useBugStatus = (props: any) => {
  const { record } = props;
  const { GetBugStatusList } = TenantQueries;
  const [allBugs, setAllBugs] = useState<any>([]);

  const [openBugs, setOpenBugs] = useState<any>([]);
  const [closedBugs, setClosedBugs] = useState<any>([]);
  const [priorityLevel, setPriorityLevel] = useState<any>([]);
  const [dataRelated, setDataRelated] = useState<any>([]);
  const [performanceRelated, setPerformanceRelated] = useState<any>([]);
  const [uiRelated, setUiRelated] = useState<any>([]);
  const openBugColors = [
    "#C71585",
    "#8B0000",
    "#006400",
    "#800080",
    "#191970",
    "#ff7803",
    "#BDB76B",
    "#8B4513",
  ];
  const closedBugColors = [
    "#008080",
    "#574a2b",
    "#247a05",
    "#099c80",
    "#032259",
    "#340275",
    "#2E8B57",
    "#83449c",
    "#FFE4E1",
    "#660404",
    "#ff7803",
    "#593f03",
  ];
  const priorityColors = ["#cc0e0e", "#f0ad73", "#244203", "#620dd1"];
  const dataColors = ["#099c80", "#1E90FF", "#034263", "#75056a"];
  const performanceColors = ["#8B4513", "#032259", "#800080", "#f0ad73"];
  const uiColors = ["#099c80", "#032259", "#340275", "#2E8B57", "#83449c"];
  const transformData = (
    category: string,
    data1: any,
    excludeTitle: any,
    colors: any[]
  ) =>
    data1
      .filter(
        (item: any) => item.category === category && item.label !== excludeTitle
      )
      .map(({ label, value }: any, index: number) => ({
        title: `${label} [${value}]`,
        label: label,
        value: parseInt(value, 10), // Convert value to number
        color: colors[index % colors.length], // Assign color based on category
      }));

  const getBugStatus = () => {
    GetBugStatusList(record.tenantName).then((res) => {
      setAllBugs(res);
      const open = transformData("open_bugs", res, "Total Open", openBugColors);
      const closed = transformData(
        "closed_bugs",
        res,
        "Total Closed",
        closedBugColors
      );
      const priority = transformData("bugs_priority", res, "", priorityColors);
      const data = transformData("bugs_data_related", res, "", dataColors);
      const performance = transformData(
        "bugs_performance_related",
        res,
        "",
        performanceColors
      );
      const ui = transformData("bugs_ui_related", res, "", uiColors);
      setOpenBugs(open);
      setClosedBugs(closed);
      setPriorityLevel(priority);
      setDataRelated(data);
      setPerformanceRelated(performance);
      setUiRelated(ui);
    });
  };

  return {
    getBugStatus,
    openBugs,
    closedBugs,
    priorityLevel,
    allBugs,
    dataRelated,
    performanceRelated,
    uiRelated,
  };
};

export default useBugStatus;
