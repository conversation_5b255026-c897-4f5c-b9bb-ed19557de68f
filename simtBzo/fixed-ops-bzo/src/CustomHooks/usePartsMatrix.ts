import { useCallback, useState } from "react";
import {
  fetchKpiScorecardPartsMatrix,
  fetchKpiScorecardPartsMatrixDetails,
  InsertOrUpdatePartsMatrix,
  PartsMatrixFileUpload,
  UpdateSubmitStatus,
} from "../service/mutations";
import dayjs from "dayjs";
import { GetGridTypeOptions } from "../service/dataFetchQueries";
import moment from "moment";
import { useTranslate } from "react-admin";
import { Constants } from "../utils/constants";
import { NotificationType } from "../types";
import { traceSpan } from "../utils/OTTTracing";

const usePartsMatrix = (record: any, gridRef: any, getStoreQuery: any) => {
  const [matrixValidationMessage, setMatrixValidationMessage] =
    useState<any>("");
  const [installDateValue, setInstallDateValue] = useState<any>();
  const [isSuccessful, setIsSuccessful] = useState<boolean>(true);
  const [addMatrix, setAddMatrix] = useState<string>("");
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [statusMessage, setStatusMessage] = useState<string>("");
  const [statusMessageType, setStatusMessageType] =
    useState<NotificationType | undefined>("success");
  const [saveClickedFlag, setSaveClickedFlag] = useState<boolean>(false);
  const [rowData, setRowData] = useState<any>();
  const [initialRowData, setInitialRowData] = useState<any>();
  const [matrices, setMatrices] = useState<any>([]);
  const [formData, setFormData] = useState<any>({});
  const [edit, setEdit] = useState(false);
  const [enableSave, setEnableSave] = useState<boolean>(false);
  const [matrixType, setMatrixType] = useState<any>([]);
  const [matrixSource, setMatrixSource] = useState<any>([]);
  const [listLoading, setListLoading] = useState<boolean>(true);
  const [rowDataList, setRowDataList] = useState<any>([]);
  const [sourceLoading, setSourceLoading] = useState<boolean>(false);
  const [expandedAccordionIndex, setExpandedAccordionIndex] = useState(null);
  const [installDate, setInstallDate] = useState<any>();
  const [selectedSources, setSelectedSources] = useState<any[]>([]);
  const [submitLoading, setSubmitLoading] = useState<boolean>(false);
  const [updateDate, setUpdateDate] = useState(false);
  const [openMatrixNameModal, setOpenMatrixNameModal] =
    useState<boolean>(false);
  const [matrixNameChanged, setMatrixNameChanged] = useState<boolean>(false);
  const [isAccordionExpanded, setIsAccordionExpanded] =
    useState<boolean>(false);
  const translate = useTranslate();

  const validatePriceRanges = (data: any) => {
    let isValid = true;
    setMatrixValidationMessage("");
    for (const item of data) {
      const priceStart = parseFloat(item.priceStartRange);
      const priceEnd = parseFloat(item.priceEndRange);

      if (priceEnd <= priceStart) {
        // Set the flag to false if validation fails
        isValid = false;
        setMatrixValidationMessage("To value must be greater than From Value.");
        break; // No need to continue checking if one fails
      }
      if (
        item.addPercentage.match(/[^0-9.]/g) ||
        item.priceEndRange.match(/[^0-9.]/g)
      ) {
        isValid = false;
        setMatrixValidationMessage("Enter numeric values only.");
        break;
      }
      if (Object.values(item).some((value) => value === "")) {
        // Set the flag to false if there's an empty string in the item
        isValid = false;
        setMatrixValidationMessage(
          "All fields must be filled out for each item."
        );
        break;
      }
    }
    return isValid;
  };

  const handleChange = (event: any) => {
    setSelectedSources(event.target.value);
  };

  const handleSubmitPartsMatrix = (values: any, pCall?: string) => {
    let combinedSource: any = values?.source ? [...values?.source] : [];
    if (values?.customsource) {
      combinedSource = combinedSource.concat(
        values.customsource.split(",").map((item: any) => item.trim())
      );
    }
    if (pCall == "upload") {
      const reader: any = new FileReader();
      if (values.csvFile.rawFile) {
        reader.readAsDataURL(values.csvFile.rawFile);
        reader.onloadend = () => {
          const base64Content = reader.result.split(",")[1]; // remove prefix value from base64 string
          const pValData: any = {
            inMatrixOrFleet: values.partsFor,
            realm: values.realmName,
            base64Data: base64Content,
            inFileName: values.csvFile.title,
            inMatrixType:
              values.partsFor === "opcode" ? values.opcode : values.matrixtype,
            inInstallationDate: dayjs(installDateValue).format("MM/DD/YYYY"),
            inStoreId: values.storeId,
            inTenantId: values.tenantId,
            inPrtsource:
              record.dms === "Reynolds" || record.dms === "Tekion"
                ? [""]
                : combinedSource.sort(
                    (a: any, b: any) => parseInt(a, 10) - parseInt(b, 10)
                  ),
          };
          PartsMatrixFileUpload(pValData).then((res: any) => {
            if (res.results[0].status === 1) {
              setStatusMessageType("success")
              setIsSuccessful(true);
              setAddMatrix("");
              fetchPartsMatrixRowData();
            } else {
              setIsSuccessful(false);
              setStatusMessageType("error")
            }
            setOpenSnackbar(true);
            setStatusMessage(res.results[0].msg);
          });
        };
      }
    } else if (pCall == "edit") {
      setSaveClickedFlag(true);
      const isValidated = validatePriceRanges(rowData);
      if (isValidated) {
        setSaveClickedFlag(false);
        const data = {
          installation_date: values.installation_date,
          pCreatedDate: rowData[0].createdDate,
          matrixtype: rowData[0].matrixType,
          source: combinedSource,
          storeId: record.storeId,
          partsFor: rowData[0].partsFor,
        };
        const newMatrix = {
          tableData: [...rowData],
          formData: { ...data },
        };
        createOrUpdatePartsMatrix(newMatrix, "update_count_change");
      }
    } else {
      setSaveClickedFlag(true);
      const newMatrix = {
        tableData: [...initialRowData],
        formData: { ...values },
      };
      const isPriceRangeValid = validatePriceRanges(newMatrix.tableData);
      if (isPriceRangeValid) {
        setMatrices([...matrices, newMatrix]);
        createOrUpdatePartsMatrix(newMatrix, "insert");
        setFormData({});
      }
    }
  };
  const createOrUpdatePartsMatrix = async (newMatrix: any, action: string) => {
    try {
      let combinedSource: any = newMatrix?.formData?.source
        ? [...newMatrix?.formData?.source]
        : [];
      if (newMatrix?.formData?.customsource) {
        combinedSource = combinedSource.concat(
          newMatrix?.formData?.customsource
            .split(",")
            .map((item: any) => item.trim())
        );
      }
      const formData = {
        storeId: newMatrix.formData.storeId,
        pNewPrtsource: combinedSource,
        pOldPrtsource:
          action === "delete"
            ? combinedSource
            : rowData && rowData[0]?.prtsource
            ? rowData[0].prtsource
            : combinedSource,
        p_matrix_type:
          newMatrix.formData.partsFor === "opcode" && action === "insert"
            ? newMatrix.formData.opcode
            : newMatrix.formData.matrixtype,
        p_matrix_or_fleet: newMatrix.formData.partsFor,
        p_created_date: newMatrix.formData.pCreatedDate
          ? newMatrix.formData.pCreatedDate
          : dayjs(installDateValue).format("MM/DD/YYYY"),
        p_store_install_date: dayjs(installDateValue).format("MM/DD/YYYY"),
      };
      const pMatrix =
        action !== "delete"
          ? newMatrix.tableData.map((item: any) => ({
              price_start_range: item.priceStartRange,
              price_end_range: item.priceEndRange,
              add_percentage: item.addPercentage,
              calc_base: item?.calcBase,
              break_field: item?.breakField,
            }))
          : rowData;
      const result = await InsertOrUpdatePartsMatrix(
        record.realmName,
        action,
        formData.storeId,
        record.dms === "Reynolds" || record.dms === "Tekion"
          ? [""]
          : formData.pNewPrtsource.sort(
              (a: any, b: any) => parseInt(a, 10) - parseInt(b, 10)
            ),
        record.dms === "Reynolds" || record.dms === "Tekion"
          ? [""]
          : formData.pOldPrtsource.sort(
              (a: any, b: any) => parseInt(a, 10) - parseInt(b, 10)
            ),
        formData.p_matrix_type,
        formData.p_matrix_or_fleet,
        formData.p_created_date,
        formData.p_store_install_date,
        pMatrix,
        record.tenantId
      );
      if (result.results[0].rStatus === 1) {
        setIsSuccessful(true);
        fetchPartsMatrixRowData();
        action === "update_count_change" &&
          fetchPartsMatrixRowDetails({
            matrixType: formData.p_matrix_type,
            createdDate: formData.p_created_date,
            pStore: formData.storeId,
            prtsource: combinedSource,
          });
        setAddMatrix("");
        setMatrixValidationMessage("");
        setEdit(false);
        setEnableSave(false);
        setStatusMessageType("success");
      } else {
        setIsSuccessful(false);
        setStatusMessageType("error");
      }
      setOpenSnackbar(true);
      setStatusMessage(result.results[0].msg);
    } catch (error) {
      console.error(error);
      // TODO: Show Error Message
      const statusMessage = translate("ERROR_MESSAGES.SOMETHING_WENT_WRONG");
      setStatusMessage(statusMessage);
      setOpenSnackbar(true);
      setStatusMessageType("error");
    }
  };

  const addEmptyRow = (data: any, setData: any) => {
    setSaveClickedFlag(true);
    const isValidated = validatePriceRanges(data);
    const lastIndexedData = data[data.length - 1];
    if (isValidated) {
      setSaveClickedFlag(false);
      const newId = data.length;
      const newEmptyRow = {
        id: newId,
        priceStartRange: (Number(lastIndexedData.priceEndRange) + 0.01)
          .toFixed(2)
          .toString(),
        priceEndRange: "",
        addPercentage: "",
        calcBase: lastIndexedData.calcBase,
        breakField: lastIndexedData.breakField,
      };

      setData((prevData: any) => [...prevData, newEmptyRow]);
      // Focus on the first cell of the newly added row
      const timer = setTimeout(() => {
        onBtStartEditing(newId, undefined);
      }, 500);
    }
  };
  const onBtStartEditing = useCallback((key?: any, pinned?: any) => {
    gridRef?.current?.api?.setFocusedCell(0, "priceEndRange", pinned);
    gridRef?.current?.api?.startEditingCell({
      rowIndex: key ? key : 0,
      colKey: "priceEndRange",
      rowPinned: pinned,
      key: key,
    });
  }, []);

  const addEmptyRowForCreate = () => {
    addEmptyRow(initialRowData, setInitialRowData);
  };

  const addEmptyRowForEdit = () => {
    addEmptyRow(rowData, setRowData);
  };

  const handleRemoveLastRow = (data: any, setData: any) => {
    const updatedRowData = [...data];
    updatedRowData.pop();
    setData(updatedRowData);
  };

  const handleRemoveLastRowForCreate = () => {
    handleRemoveLastRow(initialRowData, setInitialRowData);
  };
  const handleRemoveLastRowForEdit = () => {
    traceSpan(`click_removepartsmatrixrow_button`, {
      event: `click_removepartsmatrixrow_button`,
      pageUrl: window.location.pathname,
      timestamp: new Date().toISOString(),
      userId:localStorage.getItem('user') || ''
    });
    handleRemoveLastRow(rowData, setRowData);
  };
  const getMatrixTypesList = async (record: any) => {
    const res = await GetGridTypeOptions(
      record.tenantId,
      record.storeId,
      record.realmName
    );
    const typeChoice = res.map((item: any) => ({
      id: item.value,
      name: item.value,
    }));
    setMatrixType(typeChoice);
  };

  const fetchPartsMatrixRowData = async (callType?: string) => {
    // setEnableSave(false);
    // setEdit(false);
    try {
      const input = {
        pCallType: callType ? callType : "matrix_type",
        pStore: record.storeId,
        pMatrixType: "",
        pPrtsource: "",
        pCreatedDate: "",
        realm: record.realmName,
        tenantId:record.tenantId
      };
      const [result, res] = await Promise.all([
        fetchKpiScorecardPartsMatrix(input),
        getMatrixTypesList(record),
      ]);
      if (callType === "prtsource_list") {
        const choice = result.map((item: any) => ({
          id: item.prtsourceList,
          name: item.prtsourceList,
        }));
        choice.sort((a: any, b: any) => parseInt(a.id) - parseInt(b.id));
        const updatedChoice = [{ id: "All", name: "All" }, ...choice];
        setMatrixSource(updatedChoice);
        setListLoading(false);
      } else {
        const sortedData = result.sort((a: any, b: any) => {
          if (a.gridOrder === 1 && b.gridOrder !== 1) return -1;
          if (a.gridOrder !== 1 && b.gridOrder === 1) return 1;
          return 0;
        });
        setRowDataList(sortedData);
      }
    } catch (error) {
      console.log(error);
    } finally {
      setListLoading(false);
      setSourceLoading(false); // Set loading to false in the finally block
    }
  };

  const fetchPartsMatrixRowDetails = async (rowData: any) => {
    try {
      const input = {
        pMatrixType: rowData.matrixType,
        pCreatedDate: rowData.createdDate,
        pStore: record.storeId,
        pCallType: "matrix",
        pPrtsource: rowData.prtsource,
        realm: record.realmName,
         tenantId:record.tenantId
      };
      const result = await fetchKpiScorecardPartsMatrixDetails(input).then(
        (res) => {
          return res;
        }
      );
      setRowData(result);
    } catch (error) {
      console.log(error);
      return [];
    }
  };

  const cancel = () => {
    setEnableSave(false);
    setEdit(false);
    setExpandedAccordionIndex(null);
    setMatrixValidationMessage("");
  };

  const handleEditButton = (params: any) => {
    fetchPartsMatrixRowData("prtsource_list");
    setEdit(true);
    // setMatrixSizeOnEdit(rowData.length);
    const timer = setTimeout(() => {
      onBtStartEditing(undefined, undefined);
    }, 500);
  };
  const editMatrix = (rowData: any) => {
    setSaveClickedFlag(true);
    const isValidated = validatePriceRanges(rowData);
    if (isValidated) {
      setSaveClickedFlag(false);
      const values = {
        installation_date: moment(
          installDate,
          "ddd MMM DD YYYY HH:mm:ss [GMT]Z (z)"
        ).format("YYYY-MM-DD"),
        pCreatedDate: rowData[0].createdDate,
        matrixtype: rowData[0].matrixType,
        source: selectedSources,
        storeId: record.storeId,
      };
      const newMatrix = {
        tableData: [...rowData],
        formData: { ...values },
      };
      createOrUpdatePartsMatrix(newMatrix, "update_count_change");
    }
  };

  const handleRadioChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setExpandedAccordionIndex(null);
    setAddMatrix((event.target as HTMLInputElement).value);
    setInstallDateValue("");
    setUpdateDate(false);
    if (event.target.value === "matrix") {
      fetchPartsMatrixRowData("prtsource_list");
      setSourceLoading(true);
      setSaveClickedFlag(false);
      setIsAccordionExpanded(false);
      setRowData(null);
      setExpandedAccordionIndex(null);
      setMatrixValidationMessage("");
    } else if (event.target.value === "upload") {
      fetchPartsMatrixRowData("prtsource_list");
      setSourceLoading(true);
      // setOpenFileUpload(true);
    }
  };
  const findMatrixOrder = (order: any) => {
    const matrixOrder = order === 1 ? "Current" : "Prior " + Number(order - 1);
    return matrixOrder;
  };
  const handleSubmit = async () => {
    setSubmitLoading(true);
    try {
      const result = await UpdateSubmitStatus(
        record.tenantId,
        record.storeId,
        "parts_matrix"
      );
      if (result?.results[0]?.status === 1) {
        const statusMessage = translate(result?.results[0]?.msg);
        setStatusMessage(statusMessage);
        setOpenSnackbar(true);
        // fetch store details
        getStoreQuery();
      }
    } catch (error) {
      const statusMessage = translate("ERROR_MESSAGES.SOMETHING_WENT_WRONG");
      setStatusMessage(statusMessage);
      setOpenSnackbar(true);
    } finally {
      setSubmitLoading(false);
    }
  };

  return {
    setMatrixValidationMessage,
    matrixValidationMessage,
    saveClickedFlag,
    setInitialRowData,
    rowData,
    onBtStartEditing,
    initialRowData,
    setEnableSave,
    enableSave,
    setListLoading,
    fetchPartsMatrixRowData,
    setOpenSnackbar,
    openSnackbar,
    statusMessage,
    isSuccessful,
    rowDataList,
    handleSubmit,
    submitLoading,
    addMatrix,
    handleRadioChange,
    handleSubmitPartsMatrix,
    setAddMatrix,
    addEmptyRowForCreate,
    handleRemoveLastRowForCreate,
    matrixSource,
    matrixType,
    sourceLoading,
    installDateValue,
    setInstallDateValue,
    updateDate,
    setUpdateDate,
    listLoading,
    expandedAccordionIndex,
    setIsAccordionExpanded,
    setRowData,
    setExpandedAccordionIndex,
    setEdit,
    setSaveClickedFlag,
    fetchPartsMatrixRowDetails,
    setSelectedSources,
    setInstallDate,
    findMatrixOrder,
    handleEditButton,
    createOrUpdatePartsMatrix,
    cancel,
    edit,
    addEmptyRowForEdit,
    handleRemoveLastRowForEdit,
    setOpenMatrixNameModal,
    openMatrixNameModal,
    matrixNameChanged,
    setMatrixNameChanged,
    getMatrixTypesList,
    statusMessageType,
    setStatusMessageType,
    setStatusMessage,
  };
};

export default usePartsMatrix;
