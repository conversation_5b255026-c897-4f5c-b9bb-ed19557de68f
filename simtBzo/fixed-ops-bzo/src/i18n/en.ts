import { TranslationMessages } from "react-admin";
import englishMessages from "ra-language-english";
const customEnglishMessages: TranslationMessages = {
  ...englishMessages,
  LABELS: {
    GROUP_NAME: "Group name",
    GROUP_DESCRIPTION: "Group Description",
    STORES: "STORES",
    STORE_NAME: "Store Name",
    DEALERSHIP: "Dealership",
    ADDRESS: "Address",
    MANUFACTURER: "Manufacturer",
    DMS: "DMS",
    LOGO: "Logo",
    TENANT_LOGO: "Tenant Logo",
    DMS_NAME: "DMS Name",
    DEALERSHIP_NAME: "Dealership Name",
    DISPLAY_NAME: "Display Name",
    TENANT_ID: "Tenant Id",
    STORE_GROUPS: " Store Groups",
    DATA_FEED_DETAILS: "Data Feed Details",
    STORE_INFO: "Store Info",
    WEBSITE: "Website",
    DMS_LOGO: "DMS Logo",
    SITE_ADDRESS: "FOPC site address",
  },
  BUTTONS: {
    CREATE: "Create",
    UPDATE: "Update",
    DELETE: "Delete",
    EDIT: "Edit",
    CANCEL: "Cancel",
    UPDATE_DATA: "Update Data",
    BULK_DATA_LOAD: "Bulk Data Load",
    Add_STORE: "Add Store",
    CREATE_GROUP: "Create Group",
    EDIT_GROUP: "Edit Group",
    DELETE_GROUP: "Delete Group",
    EDIT_STORE: "Edit Store",
    SHOW_STORE: "Show Store",
    NEW_TENANT: "New Tenant",
    configure_Tenant: "Configure Tenant",
    configured_Tenant: "Configured Tenant",
    HIDE: "Hide",
    SHOW: "Show",
    SAVE_CHANGES: "Submit",
  },
  TABS: {
    DEALERSHIPS: "Dealerships",
    ALL_STORES: "All Stores",
    ONBOARDING: "Onboarding",
    DATA_FEEDS: "Data Feeds",
    OPCODES: "Opcodes",
    PAYTYPES: "Pay Types",
    DAILY_LOAD_SETUP: "Daily Load Setup",
    DATA_LOAD_STATUS: "Data Load Status",
    STORES: "Stores",
    POST_ONBOARD: "Post-Onboard",
    DATA_REFRESH: "Data Refresh",
    ADVISORS: "Advisors",
    TECHNICIANS: "Technicians",
    LABOR_GRID: "Labor Grid(s)",
    PARTS_MATRIX: "Parts Matrix(S)",
    STORE_SETTINGS: "Store Settings",
  },
  SIDEBAR: {
    MENU_DASHBOARD: {
      NAME: "Dealerships",
      ITEMS: {
        ALLTENANTS: "All Tenants",
        ONBOARDING: "Onboarding",
        NS_QA_VALIDATION: "NS QA Validation",
        IN_REVIEW: "In Review",
        READY_TO_LAUNCH: "Ready To Launch",
        LAUNCHED: "Launched",
        CANCELLED: "Cancelled",
        TEST_TENANT: "Test Tenant",
      },
    },
    MENU_STORES: {
      NAME: "Stores",
      ITEMS: {
        ALL_STORES: "All Stores",
        ALL_MODULE_DETAILS: "All Enrollments",
      },
    },
    MENU_NEVER_EVENTS: {
      NAME: "Never Events",
      ITEMS: {
        EDI_EVENTS: "EDI Events",
        FOPC_EVENTS: "FOPC Events",
        MAINTENANCE_EVENTS: "Maintenance Events",
        USER_ROLES: "User Roles",
        DAILY_DATA_AS_OF: "Data as of Report",
        DAILY_LOGINS: "Login Tracker",
        ADU_USERS: "ADU Users",
        EMAIL_REPORT: "Auto Report Emails",
        MNTHRO13_REPORT: "13-Month RO Report",
        MISSED_REPORT: "No Data Days Report",
      },
    },
    MENU_TEST_RESULTS: {
      NAME: "Test Results",
      ITEMS: {
        REGRESSION_TEST: "Reggression Test",
        SMOKE_TEST: "Smoke Test",
      },
    },
    MENU_SETTINGS: {
      NAME: "Settings",
      ITEMS: {
        DB_Settings: "DB Settings",
        Build_Application: "Build Application",
        SCHEMA_CONFIG: "Schema Configuration",
      },
    },
    MENU_TELEMETRY: {
      NAME: "Telemetry",
      ITEMS: {
        FOPC_Telemetry: "FOPC Telemetry",
        BZO_Telemetry: "BZO Telemetry",
      },
    },
    LAUNCH_REPORT: "Launch Report",
    ACTIVITY_LOG: "Activity Log",
    DMS_SETTINGS: "DMS Settings",
    DMS_BILLINGS: "DMS Billings",
    TELEMETRY: "Telemetry",
  },
  MESSAGES: {
    // Genral messages. Eg: NO_PERMISSION: "You do not have permission",
    NO_STORES: "No stores found",
    NO_GROUPS: "No groups found",
    NO_TENANTS: "No tenants found",
    CANNOT_EDIT: "Cannot be edited. Please contact support team.",
    SubDomain_HelperText: `Enter only lowercase alphabets or integers without spaces. The site address cannot be edited later.`,
  },
  DIALOG_BOX: {
    STORE_DELETE: "Are you sure you want to remove this store from the group?",
    GROUP_DELETE: "Are you sure you want to delete this group?",
    DMS_DELETE: "Are you sure you want to delete this item?",
    TENANT_DELETE:
      "Deleting this tenant will also delete its associated store. Are you sure you want to proceed?",
    MATRIX_DELETE: "Are you sure you want to delete this Matrix?",
    ROW_DELETE: "Are you sure you want to delete the last row?",
    DELETE_STORE: "Are you sure you want to remove this store?",
  },
  TITLES: {
    HOME_TITLE: "FOPC BZO",
    SINGLE_STORES: "Stores",
    STORE_GROUPS: "Store Groups",
    STORES: "Stores",
    Edit_Store: "Edit Store",
    Add_Store: "Add Store",
    Edit_Dms: "Edit Dms",
    Create_Dms: "Create Dms",
  },
  GENERAL: {
    RESET_CATEGORY: "Reset Category",
    LOADING_TEXT: "Loading...",
    HIDE_UNCATEGORIZED_TABLE: "Hide Uncategorized table",
    SHOW_UNCATEGORIZED_TABLE: "Show Uncategorized table",
    SAVE_CHANGES: "Submit",
  },
  PLACEHOLDERS: {
    // Eg : SEARCH: "Search",
  },
  SUCCESS_MESSAGES: {
    UPDATE_MESSAGE: "%{entityName} updated successfully!",
    CREATE_MESSAGE: "%{entityName} created successfully! ",
    DELETE_MESSAGE: "%{entityName} deleted successfully!",
    SET_CATEGORY_MESSAGE:
      "Successfully set category for opcode %{opcodeName} to %{categoryName}.",
    SET_DEPARTMENT_MESSAGE:
      "Successfully set department for opcode %{opcodeName} to %{newDepartment}.",
    SET_CATEGORY_MESSAGE_FOR_MULTIPLE_OPCODE:
      "Successfully set category to %{categoryName}.",
    RESET_CATEGORY_MESSAGE:
      "Successfully reset category for opcode %{opcodeName}.",
    CONFIG_COMPLETE: "%{entityName} configuration completed",
    TEN_ENABLE: "Tenant enabled",
    TEN_DISABLE: "Tenant disabled",
    MAKE_UNMAPPED: "Make Unmapped Successfully!",
    MODEL_UNMAPPED: "Model Unmapped Successfully!",
  },
  ERROR_MESSAGES: {
    SOMETHING_WENT_WRONG: "Something went wrong, Please try again later",
  },
  WARNING_MESSAGES: {
    MENU_NOT_AVAILABLE: "Menu is not Available",
    ASSIGN_PROPER_MENU: "Please Assign to Proper Menu",
  },
  VALIDATION_MESSAGES: {
    REQUIRED: "Required Field",
    INVALID_INPUT: "Invalid input",
    ALPHANUMERIC: "Invalid input. Alphanumeric values only.",
    ALPHANUMERIC_WITH_HYPHEN: "Invalid input.",
    // "Invalid input. Special characters other than (-), (') and (`) are not allowed.",
    ALPHABETS_ONLY: "Invalid input. Alphabets only.",
    LOWERCASE_ALPHABETS_ONLY:
      "Invalid input. Enter only lowercase alphabets without spaces or special characters.",
    URL: "Invalid input. Please provide a valid website URL.",
    TO_VALUE_GREATER_THAN_FROM_VALUE:
      "To value should be greater than from value.",
    ALL_FIELDS_REQUIRED: "All fields are required.",
    INVALID_GUID: "Invalid Subscrption Id format.",
    INVALID_DEPARTMENT: "Invalid input. Alphanumeric values only.",
  },
  CUSTOM_ERROR: {
    // Eg: TECHNICAL_LOCAL:"Unknown technical error occurred on your device. Please try again or contact support",
  },
};

export default customEnglishMessages;
