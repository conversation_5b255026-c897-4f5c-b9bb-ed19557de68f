import React, { useEffect } from "react";
import { <PERSON>ton, Box, CircularProgress, IconButton } from "@mui/material";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import { useNavigate } from "react-router-dom";
import LogGridDefs from "./LogGridDefs";
import { AgGridReact } from "ag-grid-react";
import { useLocation } from "react-router-dom";
import { Constants } from "../../utils/constants";
import BackButton from "../../Layout/BackButton";
import dayjs from "dayjs";
import RefreshIcon from "@mui/icons-material/Refresh";
function DetailPage() {
  const location = useLocation();
  const props = location.state;
  console.log("proooo--", props);
  const navigate = useNavigate();
  console.log("rowDataaaa", location.state);
  if (props == null) {
    navigate("/Telemetry/FopcTelemetry");
  }
  const {
    allLogList,
    columnDefs,
    defaultColDef,
    onGridReady,
    getEventDetails,
    loading,
    setLoading,
    sideBar,
  } = LogGridDefs({});

  const logInput = {
    realmId: props && props.realmId && props.realmId,
    storeId: "", //props && props.storeName && props.storeName,
    dateEvent:
      props && props.accessDate
        ? dayjs(props.accessDate).format("YYYY-MM-DD")
        : "",
    username: props && props.userName && props.userName,
    traceid: props && props.traceid && props.traceid,
  };
  const fetchDetails = () => {
    getEventDetails(logInput);
  };

  // then use inside useEffect
  useEffect(() => {
    setLoading(true);
    fetchDetails();
  }, []);
  return (
    <Box sx={{ pt: 4 }}>
      <Box
        display="flex"
        alignItems="center"
        justifyContent="space-between"
        mb={2}>
        <Box display="flex" alignItems="center" gap={2}>
          <Button
            onClick={() => window.history.back()}
            variant="contained"
            sx={{ textTransform: "none" }}>
            Back
          </Button>
          <h3 style={{ margin: 0 }}>Detailed User Telemetry</h3>
        </Box>

        <IconButton onClick={fetchDetails} title="refresh">
          <RefreshIcon color="primary" />
        </IconButton>
      </Box>

      {loading ? (
        <Box
          display="flex"
          justifyContent="center"
          alignItems="center"
          height={200}>
          <CircularProgress />
        </Box>
      ) : (
        <div
          className={Constants.ag_grid_theme}
          style={{
            height: "83vh",
            //   width: selectedReport === "Access Report" ? "65vw" : "85vw",
            padding: "10px",
            marginTop: "-15px",
          }}>
          <AgGridReact
            columnDefs={columnDefs}
            editType="fullRow"
            rowData={allLogList}
            defaultColDef={defaultColDef}
            rowSelection="single"
            singleClickEdit={true}
            suppressColumnVirtualisation={true}
            suppressChangeDetection={true}
            stopEditingWhenCellsLoseFocus={true}
            onGridReady={(params: any) => onGridReady(params)}
            pivotMode={false} // ✅ Pivot mode enabled
            sideBar={sideBar}
            // onFilterChanged={onFilterChanged}
          />
        </div>
      )}
    </Box>
  );
}

export default DetailPage;
