import { ColDef } from "ag-grid-community";
import moment from "moment";
import React, { useCallback, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import DataFetchQueries from "../../service/telemetryFunctions";
import dayjs from "dayjs";
import { Button } from "@mui/material";
const LogGridDefs = (props: any) => {
  const { selectedReport, appliedreport, selectedDateRange } = props;
  console.log("pppppp----", props);
  const { GetSimtTraces, GetUserAccessData, GetEventDetails } =
    DataFetchQueries;
  const [allLogList, setAllLogList] = React.useState([]);

  // const [selectedDateRange, setSelectedDateRange] = React.useState<any>([
  //   dayjs().subtract(1, "week"),
  //   dayjs(),
  // ]);
  const [showDateSaveButton, setShowDateSaveButton] = React.useState(false);
  const [loading, setLoading] = React.useState(false);
  const navigate = useNavigate();
  useEffect(() => {
    if (appliedreport == "Access Report") {
      getUserAccessData();
    } else {
      getSimtTraces();
    }
  }, [selectedReport, appliedreport]);

  const getSimtTraces = () => {
    const logInput = {
      startDate:
        selectedDateRange &&
        selectedDateRange[0].add(1, "day").toISOString().split("T")[0],
      endDate:
        selectedDateRange &&
        selectedDateRange[1].add(1, "day").toISOString().split("T")[0],
      reportType: selectedReport,
    };

    GetSimtTraces(logInput).then((res: any) => {
      setAllLogList(res);
      setTimeout(() => {
        setLoading(false);
      }, 500);
    });
  };
  const getEventDetails = (params: any) => {
    console.log("GetDetailssssssssssss", params);
    GetEventDetails(params).then((res: any) => {
      setAllLogList(res);
      setTimeout(() => {
        setLoading(false);
      }, 500);
    });
  };
  const getUserAccessData = () => {
    const logInput = {
      startDate:
        selectedDateRange &&
        selectedDateRange[0].add(1, "day").toISOString().split("T")[0],
      endDate:
        selectedDateRange &&
        selectedDateRange[1].add(1, "day").toISOString().split("T")[0],
    };

    GetUserAccessData(logInput).then((res: any) => {
      setAllLogList(res);
      setTimeout(() => {
        setLoading(false);
      }, 500);
    });
  };

  const onGridReady = useCallback((params: any) => {
    params.api.closeToolPanel();
  }, []);
  const handleGetDetails = (rowData: any) => {
    navigate("/Telemetry/FopcTelemetry/DetailPage", {
      state: rowData,
    });
  };
  const columnDefs: ColDef[] = [
    {
      headerName: "User Name",
      field: "userName",
      tooltipField: "userName",
      flex: 4,
      cellStyle: {
        textAlign: "left",
        whiteSpace: "nowrap",
        overflow: "hidden",
        textOverflow: "ellipsis",
      },
    },
    {
      headerName: "Tenant",
      field: "realmId",
      tooltipField: "realmId",
      flex: 4,
      cellStyle: {
        textAlign: "left",
        whiteSpace: "nowrap",
        overflow: "hidden",
        textOverflow: "ellipsis",
      },
    },
    {
      headerName: "Store",
      field: "storeName",
      tooltipField: "storeName",
      flex: 4,
      cellStyle: {
        textAlign: "left",
        whiteSpace: "nowrap",
        overflow: "hidden",
        textOverflow: "ellipsis",
      },
    },
    {
      headerName: "Page Url",
      field: "pageUrl",
      tooltipField: "pageUrl",
      flex: 3,
      cellStyle: {
        textAlign: "left",
        whiteSpace: "nowrap",
        overflow: "hidden",
        textOverflow: "ellipsis",
      },
    },
    {
      headerName: "Event Name",
      field: "name",
      flex: 4,
      cellStyle: { textAlign: "left" },
    },
    {
      headerName: "Is From",
      field: "isFrom",
      flex: 4,
      tooltipField: "isFrom",
      cellStyle: {
        textAlign: "left",
        whiteSpace: "nowrap",
        overflow: "hidden",
        textOverflow: "ellipsis",
      },
    },
    {
      headerName: "Value",
      field: "value",
      flex: 4,
      cellStyle: { textAlign: "left" },
    },
    {
      headerName: "Is Data",
      hide: appliedreport == "Access Report" ? true : false,
      field: "isData",
      flex: 4,
      cellStyle: { textAlign: "left" },
    },

    {
      headerName: "EST",
      field: "estTime",
      hide: true,
      cellStyle: {
        textAlign: "right",
        whiteSpace: "nowrap",
        overflow: "hidden",
        textOverflow: "ellipsis",
      },
      valueFormatter: (params: any) => {
        return params.value
          ? dayjs(params.value).format("MM-DD-YYYY HH:mm:ss")
          : "";
      },
      tooltipValueGetter: (params: any) =>
        params.value ? dayjs(params.value).format("MM-DD-YYYY HH:mm:ss") : "",
      flex: 4,
    },
    // {
    //   headerName: "Time",
    //   field: "eventTime",
    //   cellStyle: { textAlign: "left" },
    //   flex: 4,
    // },
    {
      headerName: "UTC",
      field: "utcTime",
      flex: 4,
      hide: true,
      cellStyle: {
        textAlign: "left",
        whiteSpace: "nowrap",
        overflow: "hidden",
        textOverflow: "ellipsis",
      },
      valueFormatter: (params: any) => {
        return params.value
          ? dayjs(params.value).format("MM-DD-YYYY HH:mm:ss")
          : "";
      },
      tooltipValueGetter: (params: any) =>
        params.value ? dayjs(params.value).format("MM-DD-YYYY HH:mm:ss") : "",
    },

    {
      headerName: "IST",
      field: "istTime",
      flex: 4,
      hide: true,
      valueFormatter: (params: any) => {
        return params.value
          ? dayjs(params.value).format("MM-DD-YYYY HH:mm:ss")
          : "";
      },
      tooltipValueGetter: (params: any) =>
        params.value ? dayjs(params.value).format("MM-DD-YYYY HH:mm:ss") : "",

      cellStyle: {
        textAlign: "left",
        whiteSpace: "nowrap",
        overflow: "hidden",
        textOverflow: "ellipsis",
      },
    },

    // {
    //   headerName: "serviceName",
    //   field: "serviceName",
    //   flex: 4,
    //   cellStyle: { textAlign: "left" },
    // },

    {
      headerName: "User Ip",
      field: "userIp",
      hide: true,
      flex: 4.5,
      cellStyle: { textAlign: "left" },
    },

    {
      headerName: "User Location",
      field: "userLocation",
      tooltipField: "userLocation",
      hide: true,
      flex: 4,
      cellStyle: {
        textAlign: "left",
        whiteSpace: "nowrap",
        overflow: "hidden",
        textOverflow: "ellipsis",
      },
    },
  ];
  const accessDataColumnDefs: ColDef[] = [
    {
      headerName: "Email Id",
      field: "userName",
      tooltipField: "userName",
      flex: 1.7,
      cellStyle: {
        textAlign: "left",
        whiteSpace: "nowrap",
        overflow: "hidden",
        textOverflow: "ellipsis",
      },
    },
    {
      headerName: "Realm",
      field: "realmId",
      tooltipField: "realmId",
      flex: 1.5,
      cellStyle: {
        textAlign: "left",
        whiteSpace: "nowrap",
        overflow: "hidden",
        textOverflow: "ellipsis",
      },
    },
    {
      headerName: "Store Name",
      hide: true,
      field: "storeName",
      tooltipField: "storeName",
      flex: 1.7,
      cellStyle: {
        textAlign: "left",
        whiteSpace: "nowrap",
        overflow: "hidden",
        textOverflow: "ellipsis",
      },
    },
    {
      headerName: "Access Date",
      field: "accessDate",
      cellStyle: {
        textAlign: "left",
        whiteSpace: "nowrap",
        overflow: "hidden",
        textOverflow: "ellipsis",
      },
      flex: 1,
      valueFormatter: (params: any) => {
        return params.value
          ? dayjs(params.value).format("MM-DD-YYYY HH:mm:ss")
          : "";
      },
      tooltipValueGetter: (params: any) =>
        params.value ? dayjs(params.value).format("MM-DD-YYYY HH:mm:ss") : "",
    },
    {
      headerName: "Access Duration",
      field: "accessDuration",
      tooltipField: "accessDuration",
      flex: 1.5,
      cellStyle: {
        textAlign: "left",
        whiteSpace: "nowrap",
        overflow: "hidden",
        textOverflow: "ellipsis",
      },
    },

    {
      headerName: "Action",
      flex: 1.5,
      cellStyle: {
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
      },
      cellRenderer: (params: any) => {
        return (
          <Button
            type="submit"
            variant="outlined"
            size="small"
            onClick={() => handleGetDetails(params.data)}
            sx={{
              minWidth: "60px", // smaller width
              padding: "1px 4px", // tighter padding
              fontSize: "10px", // smaller label
              borderRadius: "3px",
            }}>
            Get Details
          </Button>
        );
      },
    },
  ];
  const defaultColDef = React.useMemo(() => {
    return {
      cellStyle: { fontSize: "12px", textAlign: "left" },
      resizable: true,
      suppressMenu: true,
      unSortIcon: true,
      enableValue: true,
      sortable: true,
      floatingFilter: true,
      filter: true,
      wrapHeaderText: true,
      autoHeaderHeight: true,
      wrapText: true,
      autoHeight: true,
      flex: 3,
      enablePivot: true,
      filterParams: {
        applyMiniFilterWhileTyping: true,
      },
    };
  }, []);
  const sideBar = React.useMemo(() => {
    return {
      toolPanels: [
        {
          id: "columns",
          labelDefault: "Columns",
          labelKey: "columns",
          iconKey: "columns",
          toolPanel: "agColumnsToolPanel",
          toolPanelParams: {
            suppressRowGroups: true,
            suppressValues: true,
            suppressPivots: true,
            suppressPivotMode: true,
            // suppressColumnFilter: true,
            // suppressColumnSelectAll: true,
            suppressColumnExpandAll: true,
          },
        },
        {
          id: "filters",
          labelDefault: "Filters",
          labelKey: "filters",
          iconKey: "filter",
          toolPanel: "agFiltersToolPanel",
        },
      ],
      defaultToolPanel: "columns", // opens columns panel by default
    };
  }, []);
  return {
    columnDefs,
    sideBar,
    defaultColDef,
    onGridReady,
    accessDataColumnDefs,
    allLogList,
    selectedDateRange,
    //setSelectedDateRange,
    showDateSaveButton,
    setShowDateSaveButton,
    getSimtTraces,
    getUserAccessData,
    getEventDetails,
    loading,
    setLoading,
  };
};

export default LogGridDefs;
