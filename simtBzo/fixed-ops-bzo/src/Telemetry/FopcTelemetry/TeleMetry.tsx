import { useEffect, useState } from "react";
import { Button, Space, Select, DatePicker, Input, Card } from "antd";
import dayjs, { Dayjs } from "dayjs";
import { AgGridReact } from "ag-grid-react";
import { Box, CircularProgress, IconButton } from "@mui/material";
import { Constants } from "../../utils/constants";
import {
  FopcTelemetryReports,
  GetUserAccessData,
} from "../../service/telemetryFunctions";
import LogGridDefs from "./LogGridDefs";
import RefreshIcon from "@mui/icons-material/Refresh";

const { RangePicker } = DatePicker;

interface Report {
  reportName: string;
  reportDisplayname: string;
  displayOrder: number;
}

export default function ReportButtons() {
  const [reports, setReports] = useState<Report[]>([]);

  const [selectedEnv, setSelectedEnv] = useState<string | null>(null);
  // state for selected report (default = Access Report)
  const [selectedReport, setSelectedReport] = useState<string>("Access Report");
  const [appliedreport, setAppliedReport] = useState<string>("Access Report");
  // Default = last 7 days
  const [selectedDateRange, setSelectedDateRange] = useState<[Dayjs, Dayjs]>([
    dayjs().subtract(7, "day"),
    dayjs(),
  ]);
  const [appliedRange, setAppliedRange] = useState<[Dayjs, Dayjs] | null>(null);

  const {
    allLogList,
    columnDefs,
    defaultColDef,
    onGridReady,
    sideBar,
    accessDataColumnDefs,
    getUserAccessData,
    getSimtTraces,
    loading,
    setLoading,
  } = LogGridDefs({ selectedReport, appliedreport, selectedDateRange });

  useEffect(() => {
    setLoading(true);
    const fetchData = async () => {
      try {
        const res = await FopcTelemetryReports();
        setReports(res);
      } catch (err) {
        console.error(err);
      }
    };

    fetchData();
  }, []);

  useEffect(() => {
    const interval = setInterval(() => {
      handleApply();
    }, 60000); // 60,000 ms = 1 minute

    return () => clearInterval(interval); // cleanup on unmount
  }, [() => handleApply]);

  const handleApply = () => {
    setLoading(true);

    setAppliedRange(selectedDateRange);
    setAppliedReport(selectedReport);

    if (selectedReport === "Access Report") {
      getUserAccessData();
    } else {
      getSimtTraces();
    }
  };

  return (
    <Space direction="vertical" style={{ padding: "30px", width: "100%" }}>
      <h2 style={{ marginBottom: "10px", textAlign: "center" }}>
        FOPC Telemetry
      </h2>

      {/* Box for Filters */}
      <Card
        style={{
          display: "inline-block",
          padding: "8px 12px",
          borderRadius: "10px",
          boxShadow: "0 2px 6px rgba(0,0,0,0.1)",
        }}>
        <Space size="large" align="center">
          {/* Date Range */}
          <div style={{ display: "flex", alignItems: "center", gap: 8 }}>
            <label style={{ fontWeight: 600 }}>Date Range:</label>
            <RangePicker
              format="MM-DD-YYYY"
              value={selectedDateRange as any}
              onChange={(values) => {
                if (values) {
                  setSelectedDateRange([dayjs(values[0]), dayjs(values[1])]);
                } else {
                  setSelectedDateRange([dayjs().subtract(7, "day"), dayjs()]);
                }
              }}
            />
          </div>

          {/* Event Types */}
          <div style={{ display: "flex", alignItems: "center", gap: 8 }}>
            <label style={{ fontWeight: 600 }}>Event Types:</label>
            <Select
              placeholder="Select Report"
              value={selectedReport}
              onChange={(val) => {
                setSelectedReport(val);
                setAppliedRange(null);
              }}
              style={{ width: 200 }}
              options={reports
                .slice()
                .sort((a, b) => a.displayOrder - b.displayOrder)
                .map((r) => ({
                  value: r.reportDisplayname,
                  label: r.reportDisplayname,
                }))}
            />
          </div>

          {/* Apply Button */}
          <Button type="primary" onClick={handleApply}>
            Apply
          </Button>
          <IconButton onClick={handleApply} title="refresh">
            <RefreshIcon color="primary" />
          </IconButton>
        </Space>
      </Card>

      {/* Show Grid after Apply */}
      {loading ? (
        <Box
          display="flex"
          justifyContent="center"
          alignItems="center"
          height={200}>
          <CircularProgress />
        </Box>
      ) : (
        <Box sx={{ pt: 4 }}>
          <div
            className={Constants.ag_grid_theme}
            style={{
              height: "66vh",
              width: appliedreport === "Access Report" ? "70vw" : "75vw",
              paddingLeft: "6px",
              paddingRight: "6px",
              marginTop: "-15px",
            }}>
            <AgGridReact
              columnDefs={
                // selectedReport === "Access Report" ||
                appliedreport === "Access Report"
                  ? accessDataColumnDefs
                  : columnDefs
              }
              editType="fullRow"
              rowData={allLogList}
              defaultColDef={defaultColDef}
              rowSelection="single"
              singleClickEdit
              suppressColumnVirtualisation
              suppressChangeDetection
              stopEditingWhenCellsLoseFocus
              onGridReady={(params: any) => onGridReady(params)}
              pivotMode={false} // ✅ Pivot mode enabled
              sideBar={sideBar}
            />
          </div>
        </Box>
      )}
    </Space>
  );
}
