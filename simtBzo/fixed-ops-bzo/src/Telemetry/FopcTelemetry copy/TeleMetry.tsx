import { useEffect, useState } from "react";
import { Button, Space } from "antd";
import dayjs, { Dayjs } from "dayjs";
import { AgGridReact } from "ag-grid-react";
import { Box } from "@mui/material";
import { Constants } from "../../utils/constants";
import {
  FopcTelemetryReports,
  GetUserAccessData,
} from "../../service/telemetryFunctions";
import DateRangePickerWithApply from "../../components/DateRangePickerr";
import LogGridDefs from "./LogGridDefs";
interface Report {
  reportName: string;
  reportDisplayname: string;
}

export default function ReportButtons() {
  const [reports, setReports] = useState<Report[]>([]);
  const [selectedReport, setSelectedReport] = useState<string | null>(null);
  const [appliedRange, setAppliedRange] = useState<[Dayjs, Dayjs] | null>(null);
  const {
    allLogList,
    columnDefs,
    defaultColDef,
    onGridReady,
    selectedDateRange,
    setSelectedDateRange,
    setShowDateSaveButton,
    getSimtTraces,
    accessDataColumnDefs,
    getUserAccessData,
  } = LogGridDefs({ selectedReport });

  useEffect(() => {
    const fetchData = async () => {
      try {
        const res = await FopcTelemetryReports();
        console.log("responseeee", res);
        setReports(res);
      } catch (err) {
        console.error(err);
      }
    };

    fetchData();
  }, []);
  const handleDateApply = () => {
    console.log("dateRangePickerrrrrrrrr", selectedReport);
    //Access Report
    setAppliedRange(selectedDateRange);
    if (selectedReport == "Access Report") {
      getUserAccessData();
    } else {
      getSimtTraces();
    }
  };

  return (
    <Space direction="vertical" style={{ padding: "30px", width: "100%" }}>
      {/* Dynamic Buttons */}
      <h2 style={{ marginBottom: "10px", textAlign: "center" }}>
        FOPC Telemetry
      </h2>
      <Space wrap>
        {reports
          .slice() // create a shallow copy so original state is not mutated
          .sort((a: any, b: any) => a.displayOrder - b.displayOrder)
          .map((report, index) => (
            <Button
              key={index}
              type={
                selectedReport === report.reportDisplayname
                  ? "primary"
                  : "default"
              } // highlight active one
              onClick={() => {
                setSelectedReport(report.reportDisplayname);
                setAppliedRange(null); // reset grid when new report is selected
              }}>
              {report.reportDisplayname}
            </Button>
          ))}
      </Space>

      {/* Step 1: Pick range */}
      {selectedReport && (
        <div style={{ marginTop: 20, paddingLeft: 2.8 }}>
          <DateRangePickerWithApply onApply={handleDateApply} />
        </div>
      )}

      {/* Step 2: Show grid after Apply */}
      {appliedRange && (
        <Box sx={{ pt: 4 }}>
          <div
            className={Constants.ag_grid_theme}
            style={{
              height: "83vh",
              width: selectedReport === "Access Report" ? "65vw" : "85vw",
              paddingLeft: "6px",
              paddingRight: "6px",
              marginTop: "-15px",
            }}>
            <AgGridReact
              columnDefs={
                // columnDefs
                selectedReport === "Access Report"
                  ? accessDataColumnDefs
                  : columnDefs
              }
              editType="fullRow"
              rowData={allLogList}
              defaultColDef={defaultColDef}
              rowSelection="single"
              singleClickEdit={true}
              suppressColumnVirtualisation={true}
              suppressChangeDetection={true}
              stopEditingWhenCellsLoseFocus={true}
              onGridReady={(params: any) => onGridReady(params)}
              // onFilterChanged={onFilterChanged}
            />
          </div>
        </Box>
      )}
    </Space>
  );
}
