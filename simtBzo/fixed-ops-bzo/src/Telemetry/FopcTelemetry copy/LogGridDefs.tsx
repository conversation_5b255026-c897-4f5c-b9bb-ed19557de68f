import { ColDef } from "ag-grid-community";
import moment from "moment";
import React, { useCallback } from "react";
import { useNavigate } from "react-router-dom";
import DataFetchQueries from "../../service/telemetryFunctions";
import dayjs from "dayjs";
import { Button } from "@mui/material";
const LogGridDefs = (props: any) => {
  const { selectedReport } = props;
  const { GetSimtTraces, GetUserAccessData, GetEventDetails } =
    DataFetchQueries;
  const [allLogList, setAllLogList] = React.useState([]);
  const [selectedDateRange, setSelectedDateRange] = React.useState<any>([
    dayjs().subtract(1, "week"),
    dayjs(),
  ]);
  const [showDateSaveButton, setShowDateSaveButton] = React.useState(false);
  const navigate = useNavigate();
  console.log("tableDtaaaaaaaaaaaa", selectedReport);
  const getSimtTraces = () => {
    const logInput = {
      startDate: selectedDateRange[0].toISOString().split("T")[0],
      endDate: selectedDateRange[1].toISOString().split("T")[0],
      reportType: selectedReport,
    };

    GetSimtTraces(logInput).then((res: any) => {
      setAllLogList(res);
    });
  };
  const getEventDetails = (params: any) => {
    console.log("GetDetailssssssssssss", params);
    GetEventDetails(params).then((res: any) => {
      setAllLogList(res);
    });
  };
  const getUserAccessData = () => {
    const logInput = {
      startDate: selectedDateRange[0].toISOString().split("T")[0],
      endDate: selectedDateRange[1].toISOString().split("T")[0],
    };

    GetUserAccessData(logInput).then((res: any) => {
      setAllLogList(res);
    });
  };

  const onGridReady = useCallback(
    (params: any) => {
      params?.api?.showLoadingOverlay();
      console.log("tableDtaaaaaaaaaaaa44444444", selectedReport);
      if (selectedReport == "Access Report") {
        getUserAccessData();
      } else {
        getSimtTraces();
      }
    },
    [selectedReport]
  );
  const handleGetDetails = (params: any) => {
    getEventDetails(params);
  };
  const columnDefs: ColDef[] = [
    {
      headerName: "User Name",
      field: "userName",
      tooltipField: "userName",
      flex: 4,
      cellStyle: {
        textAlign: "left",
        whiteSpace: "nowrap",
        overflow: "hidden",
        textOverflow: "ellipsis",
      },
    },
    {
      headerName: "Tenant",
      field: "realmId",
      tooltipField: "realmId",
      flex: 4,
      cellStyle: {
        textAlign: "left",
        whiteSpace: "nowrap",
        overflow: "hidden",
        textOverflow: "ellipsis",
      },
    },
    {
      headerName: "Store",
      field: "storeName",
      tooltipField: "storeName",
      flex: 4,
      cellStyle: {
        textAlign: "left",
        whiteSpace: "nowrap",
        overflow: "hidden",
        textOverflow: "ellipsis",
      },
    },
    {
      headerName: "Page Url",
      field: "pageUrl",
      tooltipField: "pageUrl",
      flex: 3,
      cellStyle: {
        textAlign: "left",
        whiteSpace: "nowrap",
        overflow: "hidden",
        textOverflow: "ellipsis",
      },
    },
    {
      headerName: "Event Name",
      field: "name",
      flex: 4,
      cellStyle: { textAlign: "left" },
    },
    {
      headerName: "Is From",
      field: "isFrom",
      flex: 4,
      tooltipField: "isFrom",
      cellStyle: {
        textAlign: "left",
        whiteSpace: "nowrap",
        overflow: "hidden",
        textOverflow: "ellipsis",
      },
    },
    {
      headerName: "Value",
      field: "value",
      flex: 4,
      cellStyle: { textAlign: "left" },
    },

    {
      headerName: "EST",
      field: "estTime",

      cellStyle: {
        textAlign: "right",
        whiteSpace: "nowrap",
        overflow: "hidden",
        textOverflow: "ellipsis",
      },
      valueFormatter: (params: any) => {
        if (!params.value) return "";
        const date = new Date(params.value);
        return date.toISOString().replace("T", " ").substring(0, 19);
      },
      tooltipField: "estTime",
      flex: 4,
    },
    // {
    //   headerName: "Time",
    //   field: "eventTime",
    //   cellStyle: { textAlign: "left" },
    //   flex: 4,
    // },
    {
      headerName: "UTC",
      field: "utcTime",
      tooltipField: "utcTime",
      flex: 4,
      cellStyle: {
        textAlign: "left",
        whiteSpace: "nowrap",
        overflow: "hidden",
        textOverflow: "ellipsis",
      },
      valueFormatter: (params: any) => {
        if (!params.value) return "";
        const date = new Date(params.value);
        return date.toISOString().replace("T", " ").substring(0, 19);
      },
    },

    {
      headerName: "IST",
      field: "istTime",
      tooltipField: "istTime",
      flex: 4,
      valueFormatter: (params: any) => {
        if (!params.value) return "";
        const date = new Date(params.value);
        return date.toISOString().replace("T", " ").substring(0, 19);
      },

      cellStyle: {
        textAlign: "left",
        whiteSpace: "nowrap",
        overflow: "hidden",
        textOverflow: "ellipsis",
      },
    },

    // {
    //   headerName: "serviceName",
    //   field: "serviceName",
    //   flex: 4,
    //   cellStyle: { textAlign: "left" },
    // },

    {
      headerName: "User Ip",
      field: "userIp",
      flex: 4.5,
      cellStyle: { textAlign: "left" },
    },

    {
      headerName: "User Location",
      field: "userLocation",
      tooltipField: "userLocation",
      flex: 4,
      cellStyle: {
        textAlign: "left",
        whiteSpace: "nowrap",
        overflow: "hidden",
        textOverflow: "ellipsis",
      },
    },
  ];
  const accessDataColumnDefs: ColDef[] = [
    {
      headerName: "Email Id",
      field: "userName",
      tooltipField: "userName",
      flex: 1.7,
      cellStyle: {
        textAlign: "left",
        whiteSpace: "nowrap",
        overflow: "hidden",
        textOverflow: "ellipsis",
      },
    },
    {
      headerName: "Realm",
      field: "realmId",
      tooltipField: "realmId",
      flex: 1.5,
      cellStyle: {
        textAlign: "left",
        whiteSpace: "nowrap",
        overflow: "hidden",
        textOverflow: "ellipsis",
      },
    },
    {
      headerName: "Store Name",
      field: "storeName",
      tooltipField: "storeName",
      flex: 1.7,
      cellStyle: {
        textAlign: "left",
        whiteSpace: "nowrap",
        overflow: "hidden",
        textOverflow: "ellipsis",
      },
    },
    {
      headerName: "Access Date",
      field: "accessDate",
      tooltipField: "accessDate",
      cellStyle: {
        textAlign: "left",
        whiteSpace: "nowrap",
        overflow: "hidden",
        textOverflow: "ellipsis",
      },
      flex: 1,
    },
    {
      headerName: "Access Duration",
      field: "accessDuration",
      tooltipField: "accessDuration",
      flex: 1.5,
      cellStyle: {
        textAlign: "left",
        whiteSpace: "nowrap",
        overflow: "hidden",
        textOverflow: "ellipsis",
      },
    },

    {
      headerName: "Action",
      flex: 1.5,
      cellStyle: {
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
      },
      cellRenderer: (params: any) => {
        return (
          <Button
            type="submit"
            variant="outlined"
            size="small"
            onClick={() =>
              navigate("/Telemetry/FopcTelemetry/DetailPage", {
                state: params.data,
              })
            }
            sx={{
              minWidth: "60px", // smaller width
              padding: "1px 4px", // tighter padding
              fontSize: "10px", // smaller label
              borderRadius: "3px",
            }}>
            Get Details
          </Button>
        );
      },
    },
  ];
  const defaultColDef = React.useMemo(() => {
    return {
      cellStyle: { fontSize: "12px", textAlign: "left" },
      resizable: true,
      suppressMenu: true,
      unSortIcon: true,
      enableValue: true,
      sortable: true,
      floatingFilter: true,
      filter: true,
      wrapHeaderText: true,
      autoHeaderHeight: true,
      wrapText: true,
      autoHeight: true,
      flex: 3,
    };
  }, []);
  return {
    columnDefs,
    defaultColDef,
    onGridReady,
    accessDataColumnDefs,
    allLogList,
    selectedDateRange,
    setSelectedDateRange,
    showDateSaveButton,
    setShowDateSaveButton,
    getSimtTraces,
    getUserAccessData,
    getEventDetails,
  };
};

export default LogGridDefs;
