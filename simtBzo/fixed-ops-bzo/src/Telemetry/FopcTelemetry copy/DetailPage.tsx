import React, { useEffect } from "react";
import { Button, Box } from "@mui/material";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import { useNavigate } from "react-router-dom";
import LogGridDefs from "./LogGridDefs";
import { AgGridReact } from "ag-grid-react";
import { useLocation } from "react-router-dom";
import { Constants } from "../../utils/constants";
import BackButton from "../../Layout/BackButton";
function DetailPage() {
  const location = useLocation();
  const props = location.state;
  const navigate = useNavigate();
  console.log("rowDataaaa", location.state);
  const {
    allLogList,
    columnDefs,
    defaultColDef,
    onGridReady,
    getEventDetails,
  } = LogGridDefs({});

  const logInput = {
    realmId: props.realmId,
    // storeId: props.storeName,
    dateEvent: props.accessDate,
    username: props.userName,
  };

  useEffect(() => {
    getEventDetails(logInput);
  }, []);
  return (
    <Box sx={{ pt: 4 }}>
      <Box display="flex" alignItems="center" gap={2} mb={2}>
        <BackButton />
        <h3 style={{ margin: 0 }}>Detailed User Telemetry</h3>
      </Box>

      <div
        className={Constants.ag_grid_theme}
        style={{
          height: "83vh",
          //   width: selectedReport === "Access Report" ? "65vw" : "85vw",
          padding: "10px",
          marginTop: "-15px",
        }}>
        <AgGridReact
          columnDefs={columnDefs}
          editType="fullRow"
          rowData={allLogList}
          defaultColDef={defaultColDef}
          rowSelection="single"
          singleClickEdit={true}
          suppressColumnVirtualisation={true}
          suppressChangeDetection={true}
          stopEditingWhenCellsLoseFocus={true}
          onGridReady={(params: any) => onGridReady(params)}
          // onFilterChanged={onFilterChanged}
        />
      </div>
    </Box>
  );
}

export default DetailPage;
