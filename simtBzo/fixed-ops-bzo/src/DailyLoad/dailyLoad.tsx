import { Box, Typography } from "@mui/material";
import React, { useEffect } from "react";
import DailyLoadGridDefs from "./dailyLoadGridDefs";
import { AgGridReact } from "ag-grid-react";
import { Constants } from "../utils/constants";
import CustomLinearProgress from "../components/CustomLinearProgress";
import moment from "moment";

const DailyLoad = () => {
  const {
    columnDefs,
    defaultColDef,
    onGridReady,
    getDailyLoadList,
    tenantList,
    groupedData,
    loading
  } = DailyLoadGridDefs();

  useEffect(() => {
    getDailyLoadList();
  }, []);

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        margin: "20px 16px 0px",
        gap: "12px",
      }}
    >
      <Box sx={{ display: "flex", justifyContent: "center" }}>
        <Typography sx={{ fontWeight: "bold", fontSize: "20px" }}>
          Daily Data Load Details : {moment().subtract(1, "days").format("MM-DD-YYYY")}
        </Typography>
      </Box>
      {!loading ? (
        tenantList?.map((item: any) => (
          <Box mb={2} sx={{ paddingX: "10px" }}>
            <Typography sx={{ fontWeight: "bold", fontSize: "16px" }}>
              {item}
            </Typography>
            <Box sx={{ width: "100%", marginTop: "4px" }}>
              <div
                className={Constants.ag_grid_theme}
                style={{
                  width: "100%",
                  height: groupedData[item].length * 30 + 36,
                }}
              >
                <AgGridReact
                  columnDefs={columnDefs}
                  editType="fullRow"
                  rowData={groupedData[item]}
                  defaultColDef={defaultColDef}
                  animateRows={true}
                  onGridReady={(params: any) => onGridReady(params)}
                  suppressColumnVirtualisation={true}
                  suppressChangeDetection={true}
                  rowHeight={30}
                />
              </div>
            </Box>
          </Box>
        ))
      ) : (
        <Box
          sx={{
            display: "flex",
            justifyContent: "center",
          }}
        >
          <CustomLinearProgress />
        </Box>
      )}
    </Box>
  );
};

export default DailyLoad;
