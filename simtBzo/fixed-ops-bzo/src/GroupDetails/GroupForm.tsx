import React from "react";
import { useEffect, useState } from "react";
import { Box, Button } from "@mui/material";
import { makeStyles } from "@material-ui/core/styles";

import {
  SaveButton,
  SelectArrayInput,
  SimpleForm,
  TextInput,
  maxLength,
  required,
  useNotify,
  useRecordContext,
  useTranslate,
} from "react-admin";
import CancelIcon from "@mui/icons-material/Cancel";
import { MutateGroupingMaster } from "../service/mutations";
import { Constants } from "../utils/constants";
import { Store } from "../types";
import SnackBarMessage from "../components/SnackBarMessage";
import StoreQueries from "../service/DataFetchQueries/storeQueries";

const useStyles: any = makeStyles((theme: any) => ({
  helperTextError: {
    "& .MuiFormHelperText-root": {
      color: theme.palette.error.main,
    },
  },
}));

const GroupForm = (props: any) => {
  const { GetStoreNamesQuery } = StoreQueries;
  const classes = useStyles();
  const record = useRecordContext();
  const translate = useTranslate();
  const { setOpen, action, editGroup, getGroupingDetalis } = props;
  const notify = useNotify();
  const [tenantStores, setTenantStores] = useState<Store[]>();
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [statusMessage, setStatusMessage] = useState<any>("");
  useEffect(() => {
    GetStoreNamesQuery(record.tenantId).then((data) => {
      setTenantStores(data);
    });
  }, []);

  const onSubmit = async (values: any) => {
    const addStore: string[] =
      action === Constants.actions.insert
        ? values.stores
        : values.stores.filter(
            (item: string) => !editGroup.stores.includes(item)
          );
    const groupid: number =
      action === Constants.actions.update ? editGroup.groupId : null;
    await MutateGroupingMaster(
      action,
      values.desc,
      groupid,
      addStore,
      values.name,
      record.tenantId
    )
      .then((result: any) => {
        if (result.string === Constants.success) {
          const messageToNotify =
            action === Constants.actions.insert
              ? translate("SUCCESS_MESSAGES.CREATE_MESSAGE", {
                  entityName: Constants.group,
                })
              : translate("SUCCESS_MESSAGES.UPDATE_MESSAGE", {
                  entityName: Constants.groupingDetails,
                });
          getGroupingDetalis();
          setStatusMessage(messageToNotify);
          setOpenSnackbar(true);
          setTimeout(() => {
            // setOpenSnackbar(false);
            setOpen(false);
          }, 1000);
        }
      })
      .catch((error: any) => {
        // ToDo: Need show Error Message
      });
  };
  if (tenantStores === undefined) return null;
  return (
    <>
      <SimpleForm
        defaultValues={
          action === Constants.actions.update
            ? {
                name: editGroup.name,
                desc: editGroup.desc,
                stores: editGroup.stores,
              }
            : { index: 0 }
        }
        sx={{ width: "550px" }}
        onSubmit={onSubmit}
        toolbar={false}
      >
        <TextInput
          source="name"
          label={translate("LABELS.GROUP_NAME")}
          fullWidth
          validate={[
            required(),
            maxLength(30, "Should not exceed 30 characters"),
          ]}
        />
        <TextInput
          source="desc"
          label={translate("LABELS.GROUP_DESCRIPTION")}
          fullWidth
          validate={[
            required(),
            maxLength(100, "Should not exceed 100 characters"),
          ]}
        />
        <SelectArrayInput
          className={classes.helperText}
          fullWidth
          source="stores"
          label={"Stores"}
          choices={tenantStores}
          optionText="storeName"
          optionValue="storeId"
          validate={required()}
        />
        <Box>
          <SaveButton />
          <Button
            color="primary"
            variant="contained"
            startIcon={<CancelIcon />}
            sx={{ m: 2 }}
            onClick={() => setOpen(false)}
          >
            {translate("BUTTONS.CANCEL")}
          </Button>
        </Box>
      </SimpleForm>
      <SnackBarMessage
        onClose={() => setOpenSnackbar(false)}
        open={openSnackbar}
        message={statusMessage}
      />
    </>
  );
};

export default GroupForm;
