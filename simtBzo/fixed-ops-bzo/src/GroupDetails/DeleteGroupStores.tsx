import { useState } from "react";
import { <PERSON><PERSON>, Confirm, useRecord<PERSON>ontext, useTranslate } from "react-admin";
import DeleteIcon from "@mui/icons-material/Delete";
import { MutateGroupingMaster } from "../service/mutations";
import { Constants } from "../utils/constants";
import SnackBarMessage from "../components/SnackBarMessage";
import { IconButton, Tooltip } from "@mui/material";

export const DeleteGroupStore = (props: any) => {
  const record = useRecordContext();
  const translate = useTranslate();
  const { group, getGroupingDetalis } = props;
  const [open, setOpen] = useState(false);
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [statusMessage, setStatusMessage] = useState<any>("");
  const handleClick = () => setOpen(true);
  const handleConfirm = async () => {
    await MutateGroupingMaster(
      Constants.actions.delete,
      group.desc,
      group.groupId,
      [record.storeId],
      group.name,
      group.tenantId,
    )
      .then((result: any) => {
        if (result.string === Constants.success) {
          getGroupingDetalis();
          setStatusMessage(
            translate("SUCCESS_MESSAGES.DELETE_MESSAGE", {
              entityName: Constants.store,
            })
          );
        }
      })
      .catch((error: any) => {
        // ToDo: Need show Error Message
      })
      .finally(() => {
        setOpen(false);
        setOpenSnackbar(true);
      });
  };
  return (
    <>
      <Tooltip title="Delete Store">
        <IconButton
          aria-label="delete"
          onClick={handleClick}
          size="small"
          color="primary"
        >
          <DeleteIcon />
        </IconButton>
      </Tooltip>
      <Confirm
        isOpen={open}
        // loading={isLoading}
        title={`Remove ${record.storeName}`}
        content={translate("DIALOG_BOX.STORE_DELETE")} // ToDo: Need to change this to en.ts
        onConfirm={handleConfirm}
        onClose={() => setOpen(false)}
      />
      <SnackBarMessage
        onClose={() => setOpenSnackbar(false)}
        open={openSnackbar}
        message={statusMessage}
      />
    </>
  );
};
