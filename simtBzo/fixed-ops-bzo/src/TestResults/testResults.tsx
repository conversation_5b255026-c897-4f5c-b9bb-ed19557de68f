import { <PERSON>, <PERSON><PERSON>, Card, CardContent, Typography } from "@mui/material";
import { useEffect, useState } from "react";
import { AgGridReact } from "ag-grid-react";
import TestResultsGridDefs from "./testResultsGridDefs";
import PlayArrowIcon from "@mui/icons-material/PlayArrow";
import TestRun from "./TestRun";
import SnackBarMessage from "../components/SnackBarMessage";
import { NotificationType } from "../types";
import { Constants } from "../utils/constants";
import { useLocation } from "react-router-dom";
import { DatePicker, DatePickerProps } from "antd";
import dayjs from "dayjs";
import { traceSpan } from "../utils/OTTTracing";
const TestResults = (props: any) => {
  const location = useLocation();
  const [selectedDate, setSelectedDate] = useState<string>(
    dayjs().format("YYYY/MM/DD")
  );
  const {
    columnDefs,
    defaultColDef,
    onGridReady,
    regressionResult,
    smokeResult,
    getTestResults,
    setShowDateSaveButton,
    showDateSaveButton,
    handleApplyDate,
  } = TestResultsGridDefs(selectedDate);
  const [isHovered, setIsHovered] = useState("");
  const [isCardSelected, setIsCardSelected] = useState("regression");
  const [openTestModal, setOpenTestModal] = useState<boolean>(false);
  const [openSnackbar, setOpenSnackbar] = useState<boolean>(false);
  const [statusMessage, setStatusMessage] = useState<string>("");
  const [statusMessageType, setStatusMessageType] = useState<
    NotificationType | undefined
  >("success");
  const onDateChange: DatePickerProps["onChange"] = (date, dateString) => {
    setSelectedDate(date.format("YYYY/MM/DD"));
    setShowDateSaveButton(true);
  };

  const onFilterChanged = (e: any) => {
    const filterValues = e.api.getFilterModel();
    Object.keys(filterValues).forEach((colId) => {
      traceSpan(`test_filter_grid_${colId}`, {
        event: `filter_grid_${colId}`,
        pageUrl: window.location.pathname,
        timestamp: new Date().toISOString(),
        userId: localStorage.getItem("user") || "",
        column: colId,
        filterValue: JSON.stringify(filterValues[colId]),
      });
    });
  };

  return (
    <Box>
      <Box sx={{ paddingY: "15px" }}>
        <SnackBarMessage
          onClose={() => setOpenSnackbar(false)}
          open={openSnackbar}
          message={statusMessage}
          type={statusMessageType}
        />
        <TestRun
          openTestModal={openTestModal}
          setOpenTestModal={setOpenTestModal}
          setStatusMessageType={setStatusMessageType}
          setOpenSnackbar={setOpenSnackbar}
          setStatusMessage={setStatusMessage}
        />
        <Box display="flex" flexWrap="wrap" width="100%" gap={2}>
          <Box
            display="flex"
            sx={{ width: "100%", justifyContent: "space-between" }}>
            <Box display="flex" sx={{ alignItems: "center" }}>
              <Typography
                sx={{
                  fontSize: "14px",
                  fontWeight: "bold",
                  marginRight: "4px",
                }}>
                Date :{" "}
              </Typography>
              <DatePicker
                // disabled={storeRecord.storeLaunched === "Completed"}
                onChange={onDateChange}
                onFocus={() => {
                  traceSpan("test_datepicker_focused", {
                    event: "test_datepicker_focused",
                    pageUrl: window.location.pathname,
                    timestamp: new Date().toISOString(),
                    userId: localStorage.getItem("user") || "",
                  });
                }}
                // placeholder="Estimated Launch Date"
                format={"MM/DD/YYYY"}
                value={dayjs(selectedDate)}
              />
              <Button
                disabled={!showDateSaveButton}
                onClick={handleApplyDate}
                sx={{
                  backgroundColor: showDateSaveButton ? "#1976d2" : "#e6e3e3",
                  color: "#ffffff",
                  marginLeft: "10px",
                  textTransform: "none",
                  "&:hover": {
                    bgcolor: "#1c84eb",
                  },
                }}>
                Apply
              </Button>
            </Box>
            <Button
              endIcon={<PlayArrowIcon sx={{ color: "white" }} />}
              sx={{
                "&:hover": {
                  backgroundColor: "#006c00",
                },
                textTransform: "none",
                color: "white",
                backgroundColor: "#008e00",
                fontFamily: "Montserrat",
              }}
              onClick={() => {
                traceSpan(`click_runtest_button`, {
                  event: `click_runtest_button`,
                  pageUrl: window.location.pathname,
                  timestamp: new Date().toISOString(),
                  userId: localStorage.getItem("user") || "",
                });
                setOpenTestModal(true);
              }}>
              Run Test
            </Button>
          </Box>

          <Box sx={{ width: "100%" }}>
            <div
              className={Constants.ag_grid_theme}
              style={{ height: "83vh", width: "100%" }}>
              <AgGridReact
                columnDefs={columnDefs}
                editType="fullRow"
                rowData={
                  props.eventType === "smoke" ? smokeResult : regressionResult
                }
                defaultColDef={defaultColDef}
                rowSelection="single"
                onGridReady={(params: any) => onGridReady(params)}
                // rowHeight={30}
                singleClickEdit={true}
                suppressColumnVirtualisation={true}
                suppressChangeDetection={true}
                stopEditingWhenCellsLoseFocus={true}
                onFilterChanged={onFilterChanged}
              />
            </div>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default TestResults;
