import {
  Box,
  Button,
  CircularProgress,
  Modal,
  Typography,
} from "@mui/material";
import React, { useEffect, useState, useCallback } from "react";
import TenantQueries from "../service/DataFetchQueries/tenantQueries";
import { NotificationType, SmokeTestRunList } from "../types";
import { AgGridReact } from "ag-grid-react";
import { ColDef, RowNode } from "ag-grid-community";
import TenantMutations from "../service/Mutations/tenantMutations";
import { useTranslate } from "react-admin";
import { traceSpan } from "../utils/OTTTracing";

interface Props {
  openTestModal: boolean;
  setOpenTestModal: (value: boolean) => void;
  setStatusMessageType: (value: NotificationType | undefined) => void;
  setOpenSnackbar: (value: boolean) => void;
  setStatusMessage: (value: string) => void;
}

const TestRun: React.FC<Props> = ({
  openTestModal,
  setOpenTestModal,
  setStatusMessageType,
  setOpenSnackbar,
  setStatusMessage,
}) => {
  const { GetSmokeTestRunList } = TenantQueries;
  const { RunTestTenants } = TenantMutations;
  const [smokeTestRunsDetails, setSmokeTestRunsDetails] = useState<
    SmokeTestRunList[]
  >([]);
  const [selectedRows, setSelectedRows] = useState<SmokeTestRunList[]>([]);
  const [testRunning, setTestRunning] = useState<boolean>(false);
  const [lastSelectedNode, setLastSelectedNode] = useState<RowNode | null>(
    null
  );
  const translate = useTranslate();

  useEffect(() => {
    if (openTestModal) {
      GetSmokeTestRunList().then((res: SmokeTestRunList[]) => {
        setSmokeTestRunsDetails(res);
      });
    }
  }, [openTestModal]);

  const onRowSelected = useCallback((event: any) => {
    const selectedNodes = event.api.getSelectedNodes();
    if (selectedNodes.length > 10) {
      event.node.setSelected(false);
    } else {
      setLastSelectedNode(event.node);
      setSelectedRows(event.api.getSelectedRows());
    }
  }, []);

  const getTesRunList = () => {
    GetSmokeTestRunList().then((res: SmokeTestRunList[]) => {
      setSmokeTestRunsDetails(res);
    });
  };

  const onGridReady = (params: any) => {
    params?.api?.showLoadingOverlay();
  };
  const handleTestRun = () => {
    traceSpan(`click_testmodalrun_button`, {
      event: `click_testmodalrun_button`,
      pageUrl: window.location.pathname,
      timestamp: new Date().toISOString(),
      userId: localStorage.getItem("user") || "",
    });
    setTestRunning(true);
    const testInput = selectedRows.map((item: any) => {
      return {
        tenant_id: item.tenantId,
        tenant_name: item.tenantName,
        store_id: item.storeId,
        store_name: item.storeName,
        user_role: item.userRole,
        sub_domain: item.subDomain,
      };
    });

    RunTestTenants(testInput)
      .then((res: any) => {
        res.status === 0
          ? setStatusMessageType("error")
          : setStatusMessageType("success");
        setOpenSnackbar(true);
        setStatusMessage(res.msg);
        setOpenTestModal(false);
        setTestRunning(false);
      })
      .catch((err: any) => {
        console.log("err", err);
        const statusMessage = translate("ERROR_MESSAGES.SOMETHING_WENT_WRONG");
        setStatusMessageType("error");
        setStatusMessage(statusMessage);
        setOpenSnackbar(true);
        setTestRunning(false);
      });
  };

  const modalColumnDefs: ColDef[] = [
    {
      headerName: "Tenant Name",
      field: "tenantName",
      cellStyle: { textAlign: "left" },
      flex: 4,
    },
    {
      headerName: "Store Name",
      field: "storeName",
      flex: 5,
      cellStyle: { textAlign: "left" },
    },
    {
      headerName: "User Role",
      field: "userRole",
      flex: 3,
      cellStyle: { textAlign: "left" },
    },
    {
      headerName: "Test Status",
      field: "testStatus",
      flex: 3,
      cellStyle: { textAlign: "left" },
    },
  ];

  const onFilterChanged = (e: any) => {
    const filterValues = e.api.getFilterModel();
    Object.keys(filterValues).forEach((colId) => {
      traceSpan(`testmodal_filter_grid_${colId}`, {
        event: `testmodal_filter_grid_${colId}`,
        pageUrl: window.location.pathname,
        timestamp: new Date().toISOString(),
        userId: localStorage.getItem("user") || "",
        column: colId,
        filterValue: JSON.stringify(filterValues[colId]),
      });
    });
  };

  const modalDefaultColDef = React.useMemo(() => {
    return {
      cellStyle: { fontSize: "12px", textAlign: "left" },
      resizable: true,
      suppressMenu: true,
      unSortIcon: true,
      enableValue: true,
      sortable: true,
      floatingFilter: true,
      filter: true,
      wrapHeaderText: true,
      autoHeaderHeight: true,
      wrapText: true,
      autoHeight: true,
      flex: 3,
    };
  }, []);

  return (
    <Modal
      open={openTestModal}
      onClose={() => setOpenTestModal(false)}
      aria-labelledby="modal-modal-title"
      aria-describedby="modal-modal-description">
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          position: "absolute" as "absolute",
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)",
          width: "auto",
          bgcolor: "background.paper",
          border: "1px solid #6a6a6a",
          boxShadow: 24,
          p: "16px 24px",
          borderRadius: "10px",
        }}>
        <Typography
          sx={{
            mb: 1,
            fontSize: "14px",
            fontFamily: "Montserrat",
            fontWeight: 600,
          }}>
          Run Individual Test
        </Typography>

        {/* Wrapper for AgGrid and Loading */}
        <Box
          sx={{
            width: "50vw",
            mb: 1,
            position: "relative", // Makes sure the loading is positioned relative to this box
          }}>
          {/* AgGrid with reduced opacity when loading */}
          <div
            className="ag-theme-alpine"
            style={{
              height: "83vh",
              width: "100%",
              opacity: testRunning ? 0.8 : 1, // Reduce opacity when loading
              pointerEvents: testRunning ? "none" : "auto", // Prevent interaction while loading
            }}>
            <AgGridReact
              columnDefs={modalColumnDefs}
              editType="fullRow"
              rowData={smokeTestRunsDetails}
              defaultColDef={modalDefaultColDef}
              onGridReady={onGridReady}
              onSelectionChanged={(params) =>
                setSelectedRows(params.api.getSelectedRows())
              }
              onRowSelected={onRowSelected}
              rowHeight={30}
              singleClickEdit={true}
              suppressColumnVirtualisation={true}
              suppressChangeDetection={true}
              stopEditingWhenCellsLoseFocus={true}
              rowSelection="single"
              onFilterChanged={onFilterChanged}
            />
          </div>

          {/* Loading component centered over the grid */}
          {testRunning && (
            <Box
              sx={{
                position: "absolute",
                top: "50%",
                left: "50%",
                transform: "translate(-50%, -50%)",
                display: "flex",
                flexDirection: "column",
                justifyContent: "center",
                alignItems: "center",
                backgroundColor: "rgba(255, 255, 255, 0.5)", // Semi-transparent background for the overlay
                width: "100%",
                height: "100%",
                zIndex: 1, // Ensures the loading is above the grid
              }}>
              <CircularProgress style={{ width: "50px", height: "50px" }} />
              <Typography
                sx={{
                  color: "grey",
                  fontSize: "12px",
                  fontFamily: "Montserrat",
                }}>
                Test running...
              </Typography>
            </Box>
          )}
        </Box>

        <Box
          sx={{
            display: "flex",
            justifyContent: "end",
            textTransform: "none",
          }}>
          <Button
            disabled={selectedRows.length === 0 || testRunning}
            onClick={handleTestRun}
            sx={{
              "&:hover": {
                backgroundColor: "#006c00",
              },
              textTransform: "none",
              color: "white",
              backgroundColor:
                selectedRows.length === 0 || testRunning
                  ? "#8dd28d"
                  : "#008e00",
              fontFamily: "Montserrat",
              marginRight: "4px",
            }}>
            Run
          </Button>
          <Button
            onClick={() => {
              traceSpan(`click_testmodalclose_button`, {
                event: `click_testmodalclose_button`,
                pageUrl: window.location.pathname,
                timestamp: new Date().toISOString(),
                userId: localStorage.getItem("user") || "",
              });
              setOpenTestModal(false);
            }}
            sx={{
              textTransform: "none",
              fontFamily: "Montserrat",
            }}>
            Close
          </Button>
        </Box>
      </Box>
    </Modal>
  );
};

export default TestRun;
