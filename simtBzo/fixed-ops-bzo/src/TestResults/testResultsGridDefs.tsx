import React, { useState } from "react";
import DataFetchQueries from "../service/dataFetchQueries";
import { ColDef } from "ag-grid-community";
import moment from "moment";
import { Button } from "@mui/material";
import { traceSpan } from "../utils/OTTTracing";

const TestResultsGridDefs = (selectedDate: string) => {
  const { GetQaTestContent } = DataFetchQueries;
  const [regressionResult, setRegressionResult] = useState([]);
  const [smokeResult, setSmokeResult] = useState([]);
  const [gridParams, setGridParams] = useState<any>({});
  const [showDateSaveButton, setShowDateSaveButton] = useState(false);

  const getTestResults = () => {
    GetQaTestContent(selectedDate)
      .then((res: any) => {
        const regression = res?.filter((obj: any) => obj.type === "regression");
        const smoke = res?.filter((obj: any) => obj.type === "smoke");
        setRegressionResult(regression);
        setSmokeResult(smoke);
      })
      .finally(() => {
        // setIsLoading(false);
      });
  };
  const handleApplyDate = () => {
    traceSpan(`click_testapply_button`, {
          event: `click_testapply_button`,
          pageUrl: window.location.pathname,
          timestamp: new Date().toISOString(),
          userId:localStorage.getItem('user') || ''
        });
    gridParams?.api?.showLoadingOverlay();
    getTestResults();
    setShowDateSaveButton(false);
  };

  const onGridReady = (params: any) => {
    setGridParams(params);
    params?.api?.showLoadingOverlay();
    getTestResults();
  };

  const handleViewDetails = (htmlContent: string) => {
    const blob = new Blob([htmlContent], { type: "text/html" });
    const url = URL.createObjectURL(blob);
    window.open(url, "_blank");
  };

  const columnDefs: ColDef[] = [
    {
      headerName: "Tenant Name",
      field: "tenant",
      cellStyle: { textAlign: "left" },
      flex: 4,
    },
    {
      headerName: "Store Name",
      field: "store",
      flex: 5,
      cellStyle: { textAlign: "left" },
    },
    {
      headerName: "User Role",
      field: "userRole",
      flex: 3,
      cellStyle: { textAlign: "left" },
    },
    {
      headerName: "Last Run Date",
      field: "date",
      cellStyle: { textAlign: "left", fontSize: "12px" },
      flex: 2,
      valueFormatter: (params: any) =>
        params.value
          ? moment(params.value).format("MM/DD/YYYY h:mma")
          : params.value,
    },
    {
      headerName: "Details",
      field: "content",
      cellRenderer: (params: any) => {
        const htmlContent = params.data.content;
        return (
          <Button
            // startIcon={}
            onClick={() => handleViewDetails(htmlContent)}
            sx={{ fontFamily: "Montserrat", textTransform: "none" }}
          >
            View details
          </Button>
        );
      },
      flex: 2,
    },
  ];

  const defaultColDef = React.useMemo(() => {
    return {
      cellStyle: { fontSize: "12px", textAlign: "left" },
      resizable: true,
      suppressMenu: true,
      unSortIcon: true,
      enableValue: true,
      sortable: true,
      floatingFilter: true,
      filter: true,
      wrapHeaderText: true,
      autoHeaderHeight: true,
      wrapText: true,
      autoHeight: true,
      flex: 3,
    };
  }, []);

  return {
    columnDefs,
    defaultColDef,
    onGridReady,
    regressionResult,
    smokeResult,
    getTestResults,
    setShowDateSaveButton,
    showDateSaveButton,
    handleApplyDate,
  };
};

export default TestResultsGridDefs;
