# Fixed Ops BZO

## Project status

If you have run out of energy or time for your project, put a note at the top of the README saying that development has slowed down or stopped completely. Someone may choose to fork your project or volunteer to step in as a maintainer or owner, allowing your project to keep going. You can also make an explicit request for maintainers.

#### [<PERSON><PERSON><PERSON><PERSON>] 22-2-2023
- Tenant logos rendered on dealership views.
- Created form for new tenant entry.

#### [<PERSON><PERSON><PERSON><PERSON>] 23-2-2023
- Created route and form for adding new store.
- Customised form fields.

#### [<PERSON>k<PERSON><PERSON>] 24-2-2023
- Edited tenant and store form field labels.
- Added new tab in dashboard for store list.
- Added filtering based on manufacturer for store list.
- Onboarding tab created inside tenant view.

#### [Sukru<PERSON>] 01-3-2023
- Added postgrest url to .env file.
- Rendered store list as a table.

#### [Suk<PERSON><PERSON>] 06-3-2023
- Keycloak integration done.
- Rendered keycloak login page.

#### [<PERSON><PERSON><PERSON><PERSON>] 10-3-2023
- Implemented keycloak login and logout.
- Keycloak token parameters stored in local storage.

#### [VishnuVinod] 14-3-2023
- Added Headers in data provider.
- Added schema switching.

#### [<PERSON><PERSON><PERSON><PERSON>] 14-3-2023
- Show page created for stores.
- Edited store show page with available data.

#### [<PERSON>Vinod] 15-3-2023
- Fetched dms data directly from db and displayed as an opltions.

#### [Sukrutha] 15-3-2023
- Button added for store grouping.
- Created new form for adding group of stores.
- Rendered store options in a multi-select

#### [VishnuVinod] 16-3-2023
- Added UI for creating new dms.

#### [VishnuVinod] 17-3-2023
- Added ProfileKey and Profile value to .env file.

#### [VishnuVinod] 22-3-2023
- Added functionality for dms logo updation:- Conversion "images to base64".

#### [Sukrutha] 23-3-2023
- Function used in database to insert store group details.
- Edited data format passed from group creating form.
- New group creation successful.

#### [VishnuVinod] 23-3-2023
- Displayed uploaded base64 image in a sample list.
- Passing dms value Instead of passing id while new tenant creation.

#### [Sukrutha] 24-3-2023
- Listed groups in tenant view.













